(()=>{var e={n:t=>{var i=t&&t.__esModule?()=>t.default:()=>t;return e.d(i,{a:i}),i},d:(t,i)=>{for(var s in i)e.o(i,s)&&!e.o(t,s)&&Object.defineProperty(t,s,{enumerable:!0,get:i[s]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)};(()=>{"use strict";const t=flarum.core.compat["common/extend"],i=flarum.core.compat["forum/app"];var s=e.n(i);const n=flarum.core.compat["forum/components/HeaderPrimary"];var r=e.n(n);function a(e){return null!==e&&"object"==typeof e&&"constructor"in e&&e.constructor===Object}function o(e,t){void 0===e&&(e={}),void 0===t&&(t={});const i=["__proto__","constructor","prototype"];Object.keys(t).filter(e=>i.indexOf(e)<0).forEach(i=>{void 0===e[i]?e[i]=t[i]:a(t[i])&&a(e[i])&&Object.keys(t[i]).length>0&&o(e[i],t[i])})}const l={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector:()=>null,querySelectorAll:()=>[],getElementById:()=>null,createEvent:()=>({initEvent(){}}),createElement:()=>({children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName:()=>[]}),createElementNS:()=>({}),importNode:()=>null,location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function d(){const e="undefined"!=typeof document?document:{};return o(e,l),e}const c={document:l,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle:()=>({getPropertyValue:()=>""}),Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia:()=>({}),requestAnimationFrame:e=>"undefined"==typeof setTimeout?(e(),null):setTimeout(e,0),cancelAnimationFrame(e){"undefined"!=typeof setTimeout&&clearTimeout(e)}};function p(){const e="undefined"!=typeof window?window:{};return o(e,c),e}function u(e,t){return void 0===t&&(t=0),setTimeout(e,t)}function m(){return Date.now()}function h(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)}function f(e){return"undefined"!=typeof window&&void 0!==window.HTMLElement?e instanceof HTMLElement:e&&(1===e.nodeType||11===e.nodeType)}function v(){const e=Object(arguments.length<=0?void 0:arguments[0]),t=["__proto__","constructor","prototype"];for(let i=1;i<arguments.length;i+=1){const s=i<0||arguments.length<=i?void 0:arguments[i];if(null!=s&&!f(s)){const i=Object.keys(Object(s)).filter(e=>t.indexOf(e)<0);for(let t=0,n=i.length;t<n;t+=1){const n=i[t],r=Object.getOwnPropertyDescriptor(s,n);void 0!==r&&r.enumerable&&(h(e[n])&&h(s[n])?s[n].__swiper__?e[n]=s[n]:v(e[n],s[n]):!h(e[n])&&h(s[n])?(e[n]={},s[n].__swiper__?e[n]=s[n]:v(e[n],s[n])):e[n]=s[n])}}}return e}function g(e,t,i){e.style.setProperty(t,i)}function w(e){let{swiper:t,targetPosition:i,side:s}=e;const n=p(),r=-t.translate;let a,o=null;const l=t.params.speed;t.wrapperEl.style.scrollSnapType="none",n.cancelAnimationFrame(t.cssModeFrameID);const d=i>r?"next":"prev",c=(e,t)=>"next"===d&&e>=t||"prev"===d&&e<=t,u=()=>{a=(new Date).getTime(),null===o&&(o=a);const e=Math.max(Math.min((a-o)/l,1),0),d=.5-Math.cos(e*Math.PI)/2;let p=r+d*(i-r);if(c(p,i)&&(p=i),t.wrapperEl.scrollTo({[s]:p}),c(p,i))return t.wrapperEl.style.overflow="hidden",t.wrapperEl.style.scrollSnapType="",setTimeout(()=>{t.wrapperEl.style.overflow="",t.wrapperEl.scrollTo({[s]:p})}),void n.cancelAnimationFrame(t.cssModeFrameID);t.cssModeFrameID=n.requestAnimationFrame(u)};u()}function b(e,t){void 0===t&&(t="");const i=p(),s=[...e.children];return i.HTMLSlotElement&&e instanceof HTMLSlotElement&&s.push(...e.assignedElements()),t?s.filter(e=>e.matches(t)):s}function T(e){try{return void console.warn(e)}catch(e){}}function y(e,t){void 0===t&&(t=[]);const i=document.createElement(e);return i.classList.add(...Array.isArray(t)?t:function(e){return void 0===e&&(e=""),e.trim().split(" ").filter(e=>!!e.trim())}(t)),i}function S(e,t){return p().getComputedStyle(e,null).getPropertyValue(t)}function x(e){let t,i=e;if(i){for(t=0;null!==(i=i.previousSibling);)1===i.nodeType&&(t+=1);return t}}function E(e,t,i){const s=p();return i?e["width"===t?"offsetWidth":"offsetHeight"]+parseFloat(s.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-right":"margin-top"))+parseFloat(s.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-left":"margin-bottom")):e.offsetWidth}let C,M,k;function P(){return C||(C=function(){const e=p(),t=d();return{smoothScroll:t.documentElement&&t.documentElement.style&&"scrollBehavior"in t.documentElement.style,touch:!!("ontouchstart"in e||e.DocumentTouch&&t instanceof e.DocumentTouch)}}()),C}function I(e){return void 0===e&&(e={}),M||(M=function(e){let{userAgent:t}=void 0===e?{}:e;const i=P(),s=p(),n=s.navigator.platform,r=t||s.navigator.userAgent,a={ios:!1,android:!1},o=s.screen.width,l=s.screen.height,d=r.match(/(Android);?[\s\/]+([\d.]+)?/);let c=r.match(/(iPad).*OS\s([\d_]+)/);const u=r.match(/(iPod)(.*OS\s([\d_]+))?/),m=!c&&r.match(/(iPhone\sOS|iOS)\s([\d_]+)/),h="Win32"===n;let f="MacIntel"===n;return!c&&f&&i.touch&&["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"].indexOf(`${o}x${l}`)>=0&&(c=r.match(/(Version)\/([\d.]+)/),c||(c=[0,1,"13_0_0"]),f=!1),d&&!h&&(a.os="android",a.android=!0),(c||m||u)&&(a.os="ios",a.ios=!0),a}(e)),M}function L(){return k||(k=function(){const e=p(),t=I();let i=!1;function s(){const t=e.navigator.userAgent.toLowerCase();return t.indexOf("safari")>=0&&t.indexOf("chrome")<0&&t.indexOf("android")<0}if(s()){const t=String(e.navigator.userAgent);if(t.includes("Version/")){const[e,s]=t.split("Version/")[1].split(" ")[0].split(".").map(e=>Number(e));i=e<16||16===e&&s<2}}const n=/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(e.navigator.userAgent),r=s();return{isSafari:i||r,needPerspectiveFix:i,need3dFix:r||n&&t.ios,isWebView:n}}()),k}var z={on(e,t,i){const s=this;if(!s.eventsListeners||s.destroyed)return s;if("function"!=typeof t)return s;const n=i?"unshift":"push";return e.split(" ").forEach(e=>{s.eventsListeners[e]||(s.eventsListeners[e]=[]),s.eventsListeners[e][n](t)}),s},once(e,t,i){const s=this;if(!s.eventsListeners||s.destroyed)return s;if("function"!=typeof t)return s;function n(){s.off(e,n),n.__emitterProxy&&delete n.__emitterProxy;for(var i=arguments.length,r=new Array(i),a=0;a<i;a++)r[a]=arguments[a];t.apply(s,r)}return n.__emitterProxy=t,s.on(e,n,i)},onAny(e,t){const i=this;if(!i.eventsListeners||i.destroyed)return i;if("function"!=typeof e)return i;const s=t?"unshift":"push";return i.eventsAnyListeners.indexOf(e)<0&&i.eventsAnyListeners[s](e),i},offAny(e){const t=this;if(!t.eventsListeners||t.destroyed)return t;if(!t.eventsAnyListeners)return t;const i=t.eventsAnyListeners.indexOf(e);return i>=0&&t.eventsAnyListeners.splice(i,1),t},off(e,t){const i=this;return!i.eventsListeners||i.destroyed?i:i.eventsListeners?(e.split(" ").forEach(e=>{void 0===t?i.eventsListeners[e]=[]:i.eventsListeners[e]&&i.eventsListeners[e].forEach((s,n)=>{(s===t||s.__emitterProxy&&s.__emitterProxy===t)&&i.eventsListeners[e].splice(n,1)})}),i):i},emit(){const e=this;if(!e.eventsListeners||e.destroyed)return e;if(!e.eventsListeners)return e;let t,i,s;for(var n=arguments.length,r=new Array(n),a=0;a<n;a++)r[a]=arguments[a];return"string"==typeof r[0]||Array.isArray(r[0])?(t=r[0],i=r.slice(1,r.length),s=e):(t=r[0].events,i=r[0].data,s=r[0].context||e),i.unshift(s),(Array.isArray(t)?t:t.split(" ")).forEach(t=>{e.eventsAnyListeners&&e.eventsAnyListeners.length&&e.eventsAnyListeners.forEach(e=>{e.apply(s,[t,...i])}),e.eventsListeners&&e.eventsListeners[t]&&e.eventsListeners[t].forEach(e=>{e.apply(s,i)})}),e}};const O=(e,t,i)=>{t&&!e.classList.contains(i)?e.classList.add(i):!t&&e.classList.contains(i)&&e.classList.remove(i)},A=(e,t,i)=>{t&&!e.classList.contains(i)?e.classList.add(i):!t&&e.classList.contains(i)&&e.classList.remove(i)},B=(e,t)=>{if(!e||e.destroyed||!e.params)return;const i=t.closest(e.isElement?"swiper-slide":`.${e.params.slideClass}`);if(i){let t=i.querySelector(`.${e.params.lazyPreloaderClass}`);!t&&e.isElement&&(i.shadowRoot?t=i.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`):requestAnimationFrame(()=>{i.shadowRoot&&(t=i.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`),t&&t.remove())})),t&&t.remove()}},G=(e,t)=>{if(!e.slides[t])return;const i=e.slides[t].querySelector('[loading="lazy"]');i&&i.removeAttribute("loading")},_=e=>{if(!e||e.destroyed||!e.params)return;let t=e.params.lazyPreloadPrevNext;const i=e.slides.length;if(!i||!t||t<0)return;t=Math.min(t,i);const s="auto"===e.params.slidesPerView?e.slidesPerViewDynamic():Math.ceil(e.params.slidesPerView),n=e.activeIndex;if(e.params.grid&&e.params.grid.rows>1){const i=n,r=[i-t];return r.push(...Array.from({length:t}).map((e,t)=>i+s+t)),void e.slides.forEach((t,i)=>{r.includes(t.column)&&G(e,i)})}const r=n+s-1;if(e.params.rewind||e.params.loop)for(let s=n-t;s<=r+t;s+=1){const t=(s%i+i)%i;(t<n||t>r)&&G(e,t)}else for(let s=Math.max(n-t,0);s<=Math.min(r+t,i-1);s+=1)s!==n&&(s>r||s<n)&&G(e,s)};var D={updateSize:function(){const e=this;let t,i;const s=e.el;t=void 0!==e.params.width&&null!==e.params.width?e.params.width:s.clientWidth,i=void 0!==e.params.height&&null!==e.params.height?e.params.height:s.clientHeight,0===t&&e.isHorizontal()||0===i&&e.isVertical()||(t=t-parseInt(S(s,"padding-left")||0,10)-parseInt(S(s,"padding-right")||0,10),i=i-parseInt(S(s,"padding-top")||0,10)-parseInt(S(s,"padding-bottom")||0,10),Number.isNaN(t)&&(t=0),Number.isNaN(i)&&(i=0),Object.assign(e,{width:t,height:i,size:e.isHorizontal()?t:i}))},updateSlides:function(){const e=this;function t(t,i){return parseFloat(t.getPropertyValue(e.getDirectionLabel(i))||0)}const i=e.params,{wrapperEl:s,slidesEl:n,size:r,rtlTranslate:a,wrongRTL:o}=e,l=e.virtual&&i.virtual.enabled,d=l?e.virtual.slides.length:e.slides.length,c=b(n,`.${e.params.slideClass}, swiper-slide`),p=l?e.virtual.slides.length:c.length;let u=[];const m=[],h=[];let f=i.slidesOffsetBefore;"function"==typeof f&&(f=i.slidesOffsetBefore.call(e));let v=i.slidesOffsetAfter;"function"==typeof v&&(v=i.slidesOffsetAfter.call(e));const w=e.snapGrid.length,T=e.slidesGrid.length;let y=i.spaceBetween,x=-f,C=0,M=0;if(void 0===r)return;"string"==typeof y&&y.indexOf("%")>=0?y=parseFloat(y.replace("%",""))/100*r:"string"==typeof y&&(y=parseFloat(y)),e.virtualSize=-y,c.forEach(e=>{a?e.style.marginLeft="":e.style.marginRight="",e.style.marginBottom="",e.style.marginTop=""}),i.centeredSlides&&i.cssMode&&(g(s,"--swiper-centered-offset-before",""),g(s,"--swiper-centered-offset-after",""));const k=i.grid&&i.grid.rows>1&&e.grid;let P;k?e.grid.initSlides(c):e.grid&&e.grid.unsetSlides();const I="auto"===i.slidesPerView&&i.breakpoints&&Object.keys(i.breakpoints).filter(e=>void 0!==i.breakpoints[e].slidesPerView).length>0;for(let s=0;s<p;s+=1){let n;if(P=0,c[s]&&(n=c[s]),k&&e.grid.updateSlide(s,n,c),!c[s]||"none"!==S(n,"display")){if("auto"===i.slidesPerView){I&&(c[s].style[e.getDirectionLabel("width")]="");const r=getComputedStyle(n),a=n.style.transform,o=n.style.webkitTransform;if(a&&(n.style.transform="none"),o&&(n.style.webkitTransform="none"),i.roundLengths)P=e.isHorizontal()?E(n,"width",!0):E(n,"height",!0);else{const e=t(r,"width"),i=t(r,"padding-left"),s=t(r,"padding-right"),a=t(r,"margin-left"),o=t(r,"margin-right"),l=r.getPropertyValue("box-sizing");if(l&&"border-box"===l)P=e+a+o;else{const{clientWidth:t,offsetWidth:r}=n;P=e+i+s+a+o+(r-t)}}a&&(n.style.transform=a),o&&(n.style.webkitTransform=o),i.roundLengths&&(P=Math.floor(P))}else P=(r-(i.slidesPerView-1)*y)/i.slidesPerView,i.roundLengths&&(P=Math.floor(P)),c[s]&&(c[s].style[e.getDirectionLabel("width")]=`${P}px`);c[s]&&(c[s].swiperSlideSize=P),h.push(P),i.centeredSlides?(x=x+P/2+C/2+y,0===C&&0!==s&&(x=x-r/2-y),0===s&&(x=x-r/2-y),Math.abs(x)<.001&&(x=0),i.roundLengths&&(x=Math.floor(x)),M%i.slidesPerGroup===0&&u.push(x),m.push(x)):(i.roundLengths&&(x=Math.floor(x)),(M-Math.min(e.params.slidesPerGroupSkip,M))%e.params.slidesPerGroup===0&&u.push(x),m.push(x),x=x+P+y),e.virtualSize+=P+y,C=P,M+=1}}if(e.virtualSize=Math.max(e.virtualSize,r)+v,a&&o&&("slide"===i.effect||"coverflow"===i.effect)&&(s.style.width=`${e.virtualSize+y}px`),i.setWrapperSize&&(s.style[e.getDirectionLabel("width")]=`${e.virtualSize+y}px`),k&&e.grid.updateWrapperSize(P,u),!i.centeredSlides){const t=[];for(let s=0;s<u.length;s+=1){let n=u[s];i.roundLengths&&(n=Math.floor(n)),u[s]<=e.virtualSize-r&&t.push(n)}u=t,Math.floor(e.virtualSize-r)-Math.floor(u[u.length-1])>1&&u.push(e.virtualSize-r)}if(l&&i.loop){const t=h[0]+y;if(i.slidesPerGroup>1){const s=Math.ceil((e.virtual.slidesBefore+e.virtual.slidesAfter)/i.slidesPerGroup),n=t*i.slidesPerGroup;for(let e=0;e<s;e+=1)u.push(u[u.length-1]+n)}for(let s=0;s<e.virtual.slidesBefore+e.virtual.slidesAfter;s+=1)1===i.slidesPerGroup&&u.push(u[u.length-1]+t),m.push(m[m.length-1]+t),e.virtualSize+=t}if(0===u.length&&(u=[0]),0!==y){const t=e.isHorizontal()&&a?"marginLeft":e.getDirectionLabel("marginRight");c.filter((e,t)=>!(i.cssMode&&!i.loop)||t!==c.length-1).forEach(e=>{e.style[t]=`${y}px`})}if(i.centeredSlides&&i.centeredSlidesBounds){let e=0;h.forEach(t=>{e+=t+(y||0)}),e-=y;const t=e>r?e-r:0;u=u.map(e=>e<=0?-f:e>t?t+v:e)}if(i.centerInsufficientSlides){let e=0;h.forEach(t=>{e+=t+(y||0)}),e-=y;const t=(i.slidesOffsetBefore||0)+(i.slidesOffsetAfter||0);if(e+t<r){const i=(r-e-t)/2;u.forEach((e,t)=>{u[t]=e-i}),m.forEach((e,t)=>{m[t]=e+i})}}if(Object.assign(e,{slides:c,snapGrid:u,slidesGrid:m,slidesSizesGrid:h}),i.centeredSlides&&i.cssMode&&!i.centeredSlidesBounds){g(s,"--swiper-centered-offset-before",-u[0]+"px"),g(s,"--swiper-centered-offset-after",e.size/2-h[h.length-1]/2+"px");const t=-e.snapGrid[0],i=-e.slidesGrid[0];e.snapGrid=e.snapGrid.map(e=>e+t),e.slidesGrid=e.slidesGrid.map(e=>e+i)}if(p!==d&&e.emit("slidesLengthChange"),u.length!==w&&(e.params.watchOverflow&&e.checkOverflow(),e.emit("snapGridLengthChange")),m.length!==T&&e.emit("slidesGridLengthChange"),i.watchSlidesProgress&&e.updateSlidesOffset(),e.emit("slidesUpdated"),!(l||i.cssMode||"slide"!==i.effect&&"fade"!==i.effect)){const t=`${i.containerModifierClass}backface-hidden`,s=e.el.classList.contains(t);p<=i.maxBackfaceHiddenSlides?s||e.el.classList.add(t):s&&e.el.classList.remove(t)}},updateAutoHeight:function(e){const t=this,i=[],s=t.virtual&&t.params.virtual.enabled;let n,r=0;"number"==typeof e?t.setTransition(e):!0===e&&t.setTransition(t.params.speed);const a=e=>s?t.slides[t.getSlideIndexByData(e)]:t.slides[e];if("auto"!==t.params.slidesPerView&&t.params.slidesPerView>1)if(t.params.centeredSlides)(t.visibleSlides||[]).forEach(e=>{i.push(e)});else for(n=0;n<Math.ceil(t.params.slidesPerView);n+=1){const e=t.activeIndex+n;if(e>t.slides.length&&!s)break;i.push(a(e))}else i.push(a(t.activeIndex));for(n=0;n<i.length;n+=1)if(void 0!==i[n]){const e=i[n].offsetHeight;r=e>r?e:r}(r||0===r)&&(t.wrapperEl.style.height=`${r}px`)},updateSlidesOffset:function(){const e=this,t=e.slides,i=e.isElement?e.isHorizontal()?e.wrapperEl.offsetLeft:e.wrapperEl.offsetTop:0;for(let s=0;s<t.length;s+=1)t[s].swiperSlideOffset=(e.isHorizontal()?t[s].offsetLeft:t[s].offsetTop)-i-e.cssOverflowAdjustment()},updateSlidesProgress:function(e){void 0===e&&(e=this&&this.translate||0);const t=this,i=t.params,{slides:s,rtlTranslate:n,snapGrid:r}=t;if(0===s.length)return;void 0===s[0].swiperSlideOffset&&t.updateSlidesOffset();let a=-e;n&&(a=e),t.visibleSlidesIndexes=[],t.visibleSlides=[];let o=i.spaceBetween;"string"==typeof o&&o.indexOf("%")>=0?o=parseFloat(o.replace("%",""))/100*t.size:"string"==typeof o&&(o=parseFloat(o));for(let e=0;e<s.length;e+=1){const l=s[e];let d=l.swiperSlideOffset;i.cssMode&&i.centeredSlides&&(d-=s[0].swiperSlideOffset);const c=(a+(i.centeredSlides?t.minTranslate():0)-d)/(l.swiperSlideSize+o),p=(a-r[0]+(i.centeredSlides?t.minTranslate():0)-d)/(l.swiperSlideSize+o),u=-(a-d),m=u+t.slidesSizesGrid[e],h=u>=0&&u<=t.size-t.slidesSizesGrid[e],f=u>=0&&u<t.size-1||m>1&&m<=t.size||u<=0&&m>=t.size;f&&(t.visibleSlides.push(l),t.visibleSlidesIndexes.push(e)),O(l,f,i.slideVisibleClass),O(l,h,i.slideFullyVisibleClass),l.progress=n?-c:c,l.originalProgress=n?-p:p}},updateProgress:function(e){const t=this;if(void 0===e){const i=t.rtlTranslate?-1:1;e=t&&t.translate&&t.translate*i||0}const i=t.params,s=t.maxTranslate()-t.minTranslate();let{progress:n,isBeginning:r,isEnd:a,progressLoop:o}=t;const l=r,d=a;if(0===s)n=0,r=!0,a=!0;else{n=(e-t.minTranslate())/s;const i=Math.abs(e-t.minTranslate())<1,o=Math.abs(e-t.maxTranslate())<1;r=i||n<=0,a=o||n>=1,i&&(n=0),o&&(n=1)}if(i.loop){const i=t.getSlideIndexByData(0),s=t.getSlideIndexByData(t.slides.length-1),n=t.slidesGrid[i],r=t.slidesGrid[s],a=t.slidesGrid[t.slidesGrid.length-1],l=Math.abs(e);o=l>=n?(l-n)/a:(l+a-r)/a,o>1&&(o-=1)}Object.assign(t,{progress:n,progressLoop:o,isBeginning:r,isEnd:a}),(i.watchSlidesProgress||i.centeredSlides&&i.autoHeight)&&t.updateSlidesProgress(e),r&&!l&&t.emit("reachBeginning toEdge"),a&&!d&&t.emit("reachEnd toEdge"),(l&&!r||d&&!a)&&t.emit("fromEdge"),t.emit("progress",n)},updateSlidesClasses:function(){const e=this,{slides:t,params:i,slidesEl:s,activeIndex:n}=e,r=e.virtual&&i.virtual.enabled,a=e.grid&&i.grid&&i.grid.rows>1,o=e=>b(s,`.${i.slideClass}${e}, swiper-slide${e}`)[0];let l,d,c;if(r)if(i.loop){let t=n-e.virtual.slidesBefore;t<0&&(t=e.virtual.slides.length+t),t>=e.virtual.slides.length&&(t-=e.virtual.slides.length),l=o(`[data-swiper-slide-index="${t}"]`)}else l=o(`[data-swiper-slide-index="${n}"]`);else a?(l=t.find(e=>e.column===n),c=t.find(e=>e.column===n+1),d=t.find(e=>e.column===n-1)):l=t[n];l&&(a||(c=function(e,t){const i=[];for(;e.nextElementSibling;){const s=e.nextElementSibling;t?s.matches(t)&&i.push(s):i.push(s),e=s}return i}(l,`.${i.slideClass}, swiper-slide`)[0],i.loop&&!c&&(c=t[0]),d=function(e,t){const i=[];for(;e.previousElementSibling;){const s=e.previousElementSibling;t?s.matches(t)&&i.push(s):i.push(s),e=s}return i}(l,`.${i.slideClass}, swiper-slide`)[0],i.loop&&0===!d&&(d=t[t.length-1]))),t.forEach(e=>{A(e,e===l,i.slideActiveClass),A(e,e===c,i.slideNextClass),A(e,e===d,i.slidePrevClass)}),e.emitSlidesClasses()},updateActiveIndex:function(e){const t=this,i=t.rtlTranslate?t.translate:-t.translate,{snapGrid:s,params:n,activeIndex:r,realIndex:a,snapIndex:o}=t;let l,d=e;const c=e=>{let i=e-t.virtual.slidesBefore;return i<0&&(i=t.virtual.slides.length+i),i>=t.virtual.slides.length&&(i-=t.virtual.slides.length),i};if(void 0===d&&(d=function(e){const{slidesGrid:t,params:i}=e,s=e.rtlTranslate?e.translate:-e.translate;let n;for(let e=0;e<t.length;e+=1)void 0!==t[e+1]?s>=t[e]&&s<t[e+1]-(t[e+1]-t[e])/2?n=e:s>=t[e]&&s<t[e+1]&&(n=e+1):s>=t[e]&&(n=e);return i.normalizeSlideIndex&&(n<0||void 0===n)&&(n=0),n}(t)),s.indexOf(i)>=0)l=s.indexOf(i);else{const e=Math.min(n.slidesPerGroupSkip,d);l=e+Math.floor((d-e)/n.slidesPerGroup)}if(l>=s.length&&(l=s.length-1),d===r&&!t.params.loop)return void(l!==o&&(t.snapIndex=l,t.emit("snapIndexChange")));if(d===r&&t.params.loop&&t.virtual&&t.params.virtual.enabled)return void(t.realIndex=c(d));const p=t.grid&&n.grid&&n.grid.rows>1;let u;if(t.virtual&&n.virtual.enabled&&n.loop)u=c(d);else if(p){const e=t.slides.find(e=>e.column===d);let i=parseInt(e.getAttribute("data-swiper-slide-index"),10);Number.isNaN(i)&&(i=Math.max(t.slides.indexOf(e),0)),u=Math.floor(i/n.grid.rows)}else if(t.slides[d]){const e=t.slides[d].getAttribute("data-swiper-slide-index");u=e?parseInt(e,10):d}else u=d;Object.assign(t,{previousSnapIndex:o,snapIndex:l,previousRealIndex:a,realIndex:u,previousIndex:r,activeIndex:d}),t.initialized&&_(t),t.emit("activeIndexChange"),t.emit("snapIndexChange"),(t.initialized||t.params.runCallbacksOnInit)&&(a!==u&&t.emit("realIndexChange"),t.emit("slideChange"))},updateClickedSlide:function(e,t){const i=this,s=i.params;let n=e.closest(`.${s.slideClass}, swiper-slide`);!n&&i.isElement&&t&&t.length>1&&t.includes(e)&&[...t.slice(t.indexOf(e)+1,t.length)].forEach(e=>{!n&&e.matches&&e.matches(`.${s.slideClass}, swiper-slide`)&&(n=e)});let r,a=!1;if(n)for(let e=0;e<i.slides.length;e+=1)if(i.slides[e]===n){a=!0,r=e;break}if(!n||!a)return i.clickedSlide=void 0,void(i.clickedIndex=void 0);i.clickedSlide=n,i.virtual&&i.params.virtual.enabled?i.clickedIndex=parseInt(n.getAttribute("data-swiper-slide-index"),10):i.clickedIndex=r,s.slideToClickedSlide&&void 0!==i.clickedIndex&&i.clickedIndex!==i.activeIndex&&i.slideToClickedSlide()}},N={getTranslate:function(e){void 0===e&&(e=this.isHorizontal()?"x":"y");const{params:t,rtlTranslate:i,translate:s,wrapperEl:n}=this;if(t.virtualTranslate)return i?-s:s;if(t.cssMode)return s;let r=function(e,t){void 0===t&&(t="x");const i=p();let s,n,r;const a=function(e){const t=p();let i;return t.getComputedStyle&&(i=t.getComputedStyle(e,null)),!i&&e.currentStyle&&(i=e.currentStyle),i||(i=e.style),i}(e);return i.WebKitCSSMatrix?(n=a.transform||a.webkitTransform,n.split(",").length>6&&(n=n.split(", ").map(e=>e.replace(",",".")).join(", ")),r=new i.WebKitCSSMatrix("none"===n?"":n)):(r=a.MozTransform||a.OTransform||a.MsTransform||a.msTransform||a.transform||a.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,"),s=r.toString().split(",")),"x"===t&&(n=i.WebKitCSSMatrix?r.m41:16===s.length?parseFloat(s[12]):parseFloat(s[4])),"y"===t&&(n=i.WebKitCSSMatrix?r.m42:16===s.length?parseFloat(s[13]):parseFloat(s[5])),n||0}(n,e);return r+=this.cssOverflowAdjustment(),i&&(r=-r),r||0},setTranslate:function(e,t){const i=this,{rtlTranslate:s,params:n,wrapperEl:r,progress:a}=i;let o,l=0,d=0;i.isHorizontal()?l=s?-e:e:d=e,n.roundLengths&&(l=Math.floor(l),d=Math.floor(d)),i.previousTranslate=i.translate,i.translate=i.isHorizontal()?l:d,n.cssMode?r[i.isHorizontal()?"scrollLeft":"scrollTop"]=i.isHorizontal()?-l:-d:n.virtualTranslate||(i.isHorizontal()?l-=i.cssOverflowAdjustment():d-=i.cssOverflowAdjustment(),r.style.transform=`translate3d(${l}px, ${d}px, 0px)`);const c=i.maxTranslate()-i.minTranslate();o=0===c?0:(e-i.minTranslate())/c,o!==a&&i.updateProgress(e),i.emit("setTranslate",i.translate,t)},minTranslate:function(){return-this.snapGrid[0]},maxTranslate:function(){return-this.snapGrid[this.snapGrid.length-1]},translateTo:function(e,t,i,s,n){void 0===e&&(e=0),void 0===t&&(t=this.params.speed),void 0===i&&(i=!0),void 0===s&&(s=!0);const r=this,{params:a,wrapperEl:o}=r;if(r.animating&&a.preventInteractionOnTransition)return!1;const l=r.minTranslate(),d=r.maxTranslate();let c;if(c=s&&e>l?l:s&&e<d?d:e,r.updateProgress(c),a.cssMode){const e=r.isHorizontal();if(0===t)o[e?"scrollLeft":"scrollTop"]=-c;else{if(!r.support.smoothScroll)return w({swiper:r,targetPosition:-c,side:e?"left":"top"}),!0;o.scrollTo({[e?"left":"top"]:-c,behavior:"smooth"})}return!0}return 0===t?(r.setTransition(0),r.setTranslate(c),i&&(r.emit("beforeTransitionStart",t,n),r.emit("transitionEnd"))):(r.setTransition(t),r.setTranslate(c),i&&(r.emit("beforeTransitionStart",t,n),r.emit("transitionStart")),r.animating||(r.animating=!0,r.onTranslateToWrapperTransitionEnd||(r.onTranslateToWrapperTransitionEnd=function(e){r&&!r.destroyed&&e.target===this&&(r.wrapperEl.removeEventListener("transitionend",r.onTranslateToWrapperTransitionEnd),r.onTranslateToWrapperTransitionEnd=null,delete r.onTranslateToWrapperTransitionEnd,r.animating=!1,i&&r.emit("transitionEnd"))}),r.wrapperEl.addEventListener("transitionend",r.onTranslateToWrapperTransitionEnd))),!0}};function V(e){let{swiper:t,runCallbacks:i,direction:s,step:n}=e;const{activeIndex:r,previousIndex:a}=t;let o=s;o||(o=r>a?"next":r<a?"prev":"reset"),t.emit(`transition${n}`),i&&"reset"===o?t.emit(`slideResetTransition${n}`):i&&r!==a&&(t.emit(`slideChangeTransition${n}`),"next"===o?t.emit(`slideNextTransition${n}`):t.emit(`slidePrevTransition${n}`))}var H={slideTo:function(e,t,i,s,n){void 0===e&&(e=0),void 0===i&&(i=!0),"string"==typeof e&&(e=parseInt(e,10));const r=this;let a=e;a<0&&(a=0);const{params:o,snapGrid:l,slidesGrid:d,previousIndex:c,activeIndex:p,rtlTranslate:u,wrapperEl:m,enabled:h}=r;if(!h&&!s&&!n||r.destroyed||r.animating&&o.preventInteractionOnTransition)return!1;void 0===t&&(t=r.params.speed);const f=Math.min(r.params.slidesPerGroupSkip,a);let v=f+Math.floor((a-f)/r.params.slidesPerGroup);v>=l.length&&(v=l.length-1);const g=-l[v];if(o.normalizeSlideIndex)for(let e=0;e<d.length;e+=1){const t=-Math.floor(100*g),i=Math.floor(100*d[e]),s=Math.floor(100*d[e+1]);void 0!==d[e+1]?t>=i&&t<s-(s-i)/2?a=e:t>=i&&t<s&&(a=e+1):t>=i&&(a=e)}if(r.initialized&&a!==p){if(!r.allowSlideNext&&(u?g>r.translate&&g>r.minTranslate():g<r.translate&&g<r.minTranslate()))return!1;if(!r.allowSlidePrev&&g>r.translate&&g>r.maxTranslate()&&(p||0)!==a)return!1}let b;a!==(c||0)&&i&&r.emit("beforeSlideChangeStart"),r.updateProgress(g),b=a>p?"next":a<p?"prev":"reset";const T=r.virtual&&r.params.virtual.enabled;if((!T||!n)&&(u&&-g===r.translate||!u&&g===r.translate))return r.updateActiveIndex(a),o.autoHeight&&r.updateAutoHeight(),r.updateSlidesClasses(),"slide"!==o.effect&&r.setTranslate(g),"reset"!==b&&(r.transitionStart(i,b),r.transitionEnd(i,b)),!1;if(o.cssMode){const e=r.isHorizontal(),i=u?g:-g;if(0===t)T&&(r.wrapperEl.style.scrollSnapType="none",r._immediateVirtual=!0),T&&!r._cssModeVirtualInitialSet&&r.params.initialSlide>0?(r._cssModeVirtualInitialSet=!0,requestAnimationFrame(()=>{m[e?"scrollLeft":"scrollTop"]=i})):m[e?"scrollLeft":"scrollTop"]=i,T&&requestAnimationFrame(()=>{r.wrapperEl.style.scrollSnapType="",r._immediateVirtual=!1});else{if(!r.support.smoothScroll)return w({swiper:r,targetPosition:i,side:e?"left":"top"}),!0;m.scrollTo({[e?"left":"top"]:i,behavior:"smooth"})}return!0}const y=L().isSafari;return T&&!n&&y&&r.isElement&&r.virtual.update(!1,!1,a),r.setTransition(t),r.setTranslate(g),r.updateActiveIndex(a),r.updateSlidesClasses(),r.emit("beforeTransitionStart",t,s),r.transitionStart(i,b),0===t?r.transitionEnd(i,b):r.animating||(r.animating=!0,r.onSlideToWrapperTransitionEnd||(r.onSlideToWrapperTransitionEnd=function(e){r&&!r.destroyed&&e.target===this&&(r.wrapperEl.removeEventListener("transitionend",r.onSlideToWrapperTransitionEnd),r.onSlideToWrapperTransitionEnd=null,delete r.onSlideToWrapperTransitionEnd,r.transitionEnd(i,b))}),r.wrapperEl.addEventListener("transitionend",r.onSlideToWrapperTransitionEnd)),!0},slideToLoop:function(e,t,i,s){void 0===e&&(e=0),void 0===i&&(i=!0),"string"==typeof e&&(e=parseInt(e,10));const n=this;if(n.destroyed)return;void 0===t&&(t=n.params.speed);const r=n.grid&&n.params.grid&&n.params.grid.rows>1;let a=e;if(n.params.loop)if(n.virtual&&n.params.virtual.enabled)a+=n.virtual.slidesBefore;else{let e;if(r){const t=a*n.params.grid.rows;e=n.slides.find(e=>1*e.getAttribute("data-swiper-slide-index")===t).column}else e=n.getSlideIndexByData(a);const t=r?Math.ceil(n.slides.length/n.params.grid.rows):n.slides.length,{centeredSlides:i}=n.params;let o=n.params.slidesPerView;"auto"===o?o=n.slidesPerViewDynamic():(o=Math.ceil(parseFloat(n.params.slidesPerView,10)),i&&o%2==0&&(o+=1));let l=t-e<o;if(i&&(l=l||e<Math.ceil(o/2)),s&&i&&"auto"!==n.params.slidesPerView&&!r&&(l=!1),l){const s=i?e<n.activeIndex?"prev":"next":e-n.activeIndex-1<n.params.slidesPerView?"next":"prev";n.loopFix({direction:s,slideTo:!0,activeSlideIndex:"next"===s?e+1:e-t+1,slideRealIndex:"next"===s?n.realIndex:void 0})}if(r){const e=a*n.params.grid.rows;a=n.slides.find(t=>1*t.getAttribute("data-swiper-slide-index")===e).column}else a=n.getSlideIndexByData(a)}return requestAnimationFrame(()=>{n.slideTo(a,t,i,s)}),n},slideNext:function(e,t,i){void 0===t&&(t=!0);const s=this,{enabled:n,params:r,animating:a}=s;if(!n||s.destroyed)return s;void 0===e&&(e=s.params.speed);let o=r.slidesPerGroup;"auto"===r.slidesPerView&&1===r.slidesPerGroup&&r.slidesPerGroupAuto&&(o=Math.max(s.slidesPerViewDynamic("current",!0),1));const l=s.activeIndex<r.slidesPerGroupSkip?1:o,d=s.virtual&&r.virtual.enabled;if(r.loop){if(a&&!d&&r.loopPreventsSliding)return!1;if(s.loopFix({direction:"next"}),s._clientLeft=s.wrapperEl.clientLeft,s.activeIndex===s.slides.length-1&&r.cssMode)return requestAnimationFrame(()=>{s.slideTo(s.activeIndex+l,e,t,i)}),!0}return r.rewind&&s.isEnd?s.slideTo(0,e,t,i):s.slideTo(s.activeIndex+l,e,t,i)},slidePrev:function(e,t,i){void 0===t&&(t=!0);const s=this,{params:n,snapGrid:r,slidesGrid:a,rtlTranslate:o,enabled:l,animating:d}=s;if(!l||s.destroyed)return s;void 0===e&&(e=s.params.speed);const c=s.virtual&&n.virtual.enabled;if(n.loop){if(d&&!c&&n.loopPreventsSliding)return!1;s.loopFix({direction:"prev"}),s._clientLeft=s.wrapperEl.clientLeft}function p(e){return e<0?-Math.floor(Math.abs(e)):Math.floor(e)}const u=p(o?s.translate:-s.translate),m=r.map(e=>p(e)),h=n.freeMode&&n.freeMode.enabled;let f=r[m.indexOf(u)-1];if(void 0===f&&(n.cssMode||h)){let e;r.forEach((t,i)=>{u>=t&&(e=i)}),void 0!==e&&(f=h?r[e]:r[e>0?e-1:e])}let v=0;if(void 0!==f&&(v=a.indexOf(f),v<0&&(v=s.activeIndex-1),"auto"===n.slidesPerView&&1===n.slidesPerGroup&&n.slidesPerGroupAuto&&(v=v-s.slidesPerViewDynamic("previous",!0)+1,v=Math.max(v,0))),n.rewind&&s.isBeginning){const n=s.params.virtual&&s.params.virtual.enabled&&s.virtual?s.virtual.slides.length-1:s.slides.length-1;return s.slideTo(n,e,t,i)}return n.loop&&0===s.activeIndex&&n.cssMode?(requestAnimationFrame(()=>{s.slideTo(v,e,t,i)}),!0):s.slideTo(v,e,t,i)},slideReset:function(e,t,i){void 0===t&&(t=!0);const s=this;if(!s.destroyed)return void 0===e&&(e=s.params.speed),s.slideTo(s.activeIndex,e,t,i)},slideToClosest:function(e,t,i,s){void 0===t&&(t=!0),void 0===s&&(s=.5);const n=this;if(n.destroyed)return;void 0===e&&(e=n.params.speed);let r=n.activeIndex;const a=Math.min(n.params.slidesPerGroupSkip,r),o=a+Math.floor((r-a)/n.params.slidesPerGroup),l=n.rtlTranslate?n.translate:-n.translate;if(l>=n.snapGrid[o]){const e=n.snapGrid[o];l-e>(n.snapGrid[o+1]-e)*s&&(r+=n.params.slidesPerGroup)}else{const e=n.snapGrid[o-1];l-e<=(n.snapGrid[o]-e)*s&&(r-=n.params.slidesPerGroup)}return r=Math.max(r,0),r=Math.min(r,n.slidesGrid.length-1),n.slideTo(r,e,t,i)},slideToClickedSlide:function(){const e=this;if(e.destroyed)return;const{params:t,slidesEl:i}=e,s="auto"===t.slidesPerView?e.slidesPerViewDynamic():t.slidesPerView;let n,r=e.getSlideIndexWhenGrid(e.clickedIndex);const a=e.isElement?"swiper-slide":`.${t.slideClass}`,o=e.grid&&e.params.grid&&e.params.grid.rows>1;if(t.loop){if(e.animating)return;n=parseInt(e.clickedSlide.getAttribute("data-swiper-slide-index"),10),t.centeredSlides?e.slideToLoop(n):r>(o?(e.slides.length-s)/2-(e.params.grid.rows-1):e.slides.length-s)?(e.loopFix(),r=e.getSlideIndex(b(i,`${a}[data-swiper-slide-index="${n}"]`)[0]),u(()=>{e.slideTo(r)})):e.slideTo(r)}else e.slideTo(r)}},F={loopCreate:function(e,t){const i=this,{params:s,slidesEl:n}=i;if(!s.loop||i.virtual&&i.params.virtual.enabled)return;const r=()=>{b(n,`.${s.slideClass}, swiper-slide`).forEach((e,t)=>{e.setAttribute("data-swiper-slide-index",t)})},a=i.grid&&s.grid&&s.grid.rows>1;s.loopAddBlankSlides&&(s.slidesPerGroup>1||a)&&(()=>{const e=b(n,`.${s.slideBlankClass}`);e.forEach(e=>{e.remove()}),e.length>0&&(i.recalcSlides(),i.updateSlides())})();const o=s.slidesPerGroup*(a?s.grid.rows:1),l=i.slides.length%o!==0,d=a&&i.slides.length%s.grid.rows!==0,c=e=>{for(let t=0;t<e;t+=1){const e=i.isElement?y("swiper-slide",[s.slideBlankClass]):y("div",[s.slideClass,s.slideBlankClass]);i.slidesEl.append(e)}};l?(s.loopAddBlankSlides?(c(o-i.slides.length%o),i.recalcSlides(),i.updateSlides()):T("Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)"),r()):d?(s.loopAddBlankSlides?(c(s.grid.rows-i.slides.length%s.grid.rows),i.recalcSlides(),i.updateSlides()):T("Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)"),r()):r(),i.loopFix({slideRealIndex:e,direction:s.centeredSlides?void 0:"next",initial:t})},loopFix:function(e){let{slideRealIndex:t,slideTo:i=!0,direction:s,setTranslate:n,activeSlideIndex:r,initial:a,byController:o,byMousewheel:l}=void 0===e?{}:e;const d=this;if(!d.params.loop)return;d.emit("beforeLoopFix");const{slides:c,allowSlidePrev:p,allowSlideNext:u,slidesEl:m,params:h}=d,{centeredSlides:f,initialSlide:v}=h;if(d.allowSlidePrev=!0,d.allowSlideNext=!0,d.virtual&&h.virtual.enabled)return i&&(h.centeredSlides||0!==d.snapIndex?h.centeredSlides&&d.snapIndex<h.slidesPerView?d.slideTo(d.virtual.slides.length+d.snapIndex,0,!1,!0):d.snapIndex===d.snapGrid.length-1&&d.slideTo(d.virtual.slidesBefore,0,!1,!0):d.slideTo(d.virtual.slides.length,0,!1,!0)),d.allowSlidePrev=p,d.allowSlideNext=u,void d.emit("loopFix");let g=h.slidesPerView;"auto"===g?g=d.slidesPerViewDynamic():(g=Math.ceil(parseFloat(h.slidesPerView,10)),f&&g%2==0&&(g+=1));const w=h.slidesPerGroupAuto?g:h.slidesPerGroup;let b=f?Math.max(w,Math.ceil(g/2)):w;b%w!==0&&(b+=w-b%w),b+=h.loopAdditionalSlides,d.loopedSlides=b;const y=d.grid&&h.grid&&h.grid.rows>1;c.length<g+b||"cards"===d.params.effect&&c.length<g+2*b?T("Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled or not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters"):y&&"row"===h.grid.fill&&T("Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`");const S=[],x=[],E=y?Math.ceil(c.length/h.grid.rows):c.length,C=a&&E-v<g&&!f;let M=C?v:d.activeIndex;void 0===r?r=d.getSlideIndex(c.find(e=>e.classList.contains(h.slideActiveClass))):M=r;const k="next"===s||!s,P="prev"===s||!s;let I=0,L=0;const z=(y?c[r].column:r)+(f&&void 0===n?-g/2+.5:0);if(z<b){I=Math.max(b-z,w);for(let e=0;e<b-z;e+=1){const t=e-Math.floor(e/E)*E;if(y){const e=E-t-1;for(let t=c.length-1;t>=0;t-=1)c[t].column===e&&S.push(t)}else S.push(E-t-1)}}else if(z+g>E-b){L=Math.max(z-(E-2*b),w),C&&(L=Math.max(L,g-E+v+1));for(let e=0;e<L;e+=1){const t=e-Math.floor(e/E)*E;y?c.forEach((e,i)=>{e.column===t&&x.push(i)}):x.push(t)}}if(d.__preventObserver__=!0,requestAnimationFrame(()=>{d.__preventObserver__=!1}),"cards"===d.params.effect&&c.length<g+2*b&&(x.includes(r)&&x.splice(x.indexOf(r),1),S.includes(r)&&S.splice(S.indexOf(r),1)),P&&S.forEach(e=>{c[e].swiperLoopMoveDOM=!0,m.prepend(c[e]),c[e].swiperLoopMoveDOM=!1}),k&&x.forEach(e=>{c[e].swiperLoopMoveDOM=!0,m.append(c[e]),c[e].swiperLoopMoveDOM=!1}),d.recalcSlides(),"auto"===h.slidesPerView?d.updateSlides():y&&(S.length>0&&P||x.length>0&&k)&&d.slides.forEach((e,t)=>{d.grid.updateSlide(t,e,d.slides)}),h.watchSlidesProgress&&d.updateSlidesOffset(),i)if(S.length>0&&P){if(void 0===t){const e=d.slidesGrid[M],t=d.slidesGrid[M+I]-e;l?d.setTranslate(d.translate-t):(d.slideTo(M+Math.ceil(I),0,!1,!0),n&&(d.touchEventsData.startTranslate=d.touchEventsData.startTranslate-t,d.touchEventsData.currentTranslate=d.touchEventsData.currentTranslate-t))}else if(n){const e=y?S.length/h.grid.rows:S.length;d.slideTo(d.activeIndex+e,0,!1,!0),d.touchEventsData.currentTranslate=d.translate}}else if(x.length>0&&k)if(void 0===t){const e=d.slidesGrid[M],t=d.slidesGrid[M-L]-e;l?d.setTranslate(d.translate-t):(d.slideTo(M-L,0,!1,!0),n&&(d.touchEventsData.startTranslate=d.touchEventsData.startTranslate-t,d.touchEventsData.currentTranslate=d.touchEventsData.currentTranslate-t))}else{const e=y?x.length/h.grid.rows:x.length;d.slideTo(d.activeIndex-e,0,!1,!0)}if(d.allowSlidePrev=p,d.allowSlideNext=u,d.controller&&d.controller.control&&!o){const e={slideRealIndex:t,direction:s,setTranslate:n,activeSlideIndex:r,byController:!0};Array.isArray(d.controller.control)?d.controller.control.forEach(t=>{!t.destroyed&&t.params.loop&&t.loopFix({...e,slideTo:t.params.slidesPerView===h.slidesPerView&&i})}):d.controller.control instanceof d.constructor&&d.controller.control.params.loop&&d.controller.control.loopFix({...e,slideTo:d.controller.control.params.slidesPerView===h.slidesPerView&&i})}d.emit("loopFix")},loopDestroy:function(){const e=this,{params:t,slidesEl:i}=e;if(!t.loop||!i||e.virtual&&e.params.virtual.enabled)return;e.recalcSlides();const s=[];e.slides.forEach(e=>{const t=void 0===e.swiperSlideIndex?1*e.getAttribute("data-swiper-slide-index"):e.swiperSlideIndex;s[t]=e}),e.slides.forEach(e=>{e.removeAttribute("data-swiper-slide-index")}),s.forEach(e=>{i.append(e)}),e.recalcSlides(),e.slideTo(e.realIndex,0)}};function j(e,t,i){const s=p(),{params:n}=e,r=n.edgeSwipeDetection,a=n.edgeSwipeThreshold;return!r||!(i<=a||i>=s.innerWidth-a)||"prevent"===r&&(t.preventDefault(),!0)}function R(e){const t=this,i=d();let s=e;s.originalEvent&&(s=s.originalEvent);const n=t.touchEventsData;if("pointerdown"===s.type){if(null!==n.pointerId&&n.pointerId!==s.pointerId)return;n.pointerId=s.pointerId}else"touchstart"===s.type&&1===s.targetTouches.length&&(n.touchId=s.targetTouches[0].identifier);if("touchstart"===s.type)return void j(t,s,s.targetTouches[0].pageX);const{params:r,touches:a,enabled:o}=t;if(!o)return;if(!r.simulateTouch&&"mouse"===s.pointerType)return;if(t.animating&&r.preventInteractionOnTransition)return;!t.animating&&r.cssMode&&r.loop&&t.loopFix();let l=s.target;if("wrapper"===r.touchEventsTarget&&!function(e,t){const i=p();let s=t.contains(e);return!s&&i.HTMLSlotElement&&t instanceof HTMLSlotElement&&(s=[...t.assignedElements()].includes(e),s||(s=function(e,t){const i=[t];for(;i.length>0;){const t=i.shift();if(e===t)return!0;i.push(...t.children,...t.shadowRoot?t.shadowRoot.children:[],...t.assignedElements?t.assignedElements():[])}}(e,t))),s}(l,t.wrapperEl))return;if("which"in s&&3===s.which)return;if("button"in s&&s.button>0)return;if(n.isTouched&&n.isMoved)return;const c=!!r.noSwipingClass&&""!==r.noSwipingClass,u=s.composedPath?s.composedPath():s.path;c&&s.target&&s.target.shadowRoot&&u&&(l=u[0]);const h=r.noSwipingSelector?r.noSwipingSelector:`.${r.noSwipingClass}`,f=!(!s.target||!s.target.shadowRoot);if(r.noSwiping&&(f?function(e,t){return void 0===t&&(t=this),function t(i){if(!i||i===d()||i===p())return null;i.assignedSlot&&(i=i.assignedSlot);const s=i.closest(e);return s||i.getRootNode?s||t(i.getRootNode().host):null}(t)}(h,l):l.closest(h)))return void(t.allowClick=!0);if(r.swipeHandler&&!l.closest(r.swipeHandler))return;a.currentX=s.pageX,a.currentY=s.pageY;const v=a.currentX,g=a.currentY;if(!j(t,s,v))return;Object.assign(n,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),a.startX=v,a.startY=g,n.touchStartTime=m(),t.allowClick=!0,t.updateSize(),t.swipeDirection=void 0,r.threshold>0&&(n.allowThresholdMove=!1);let w=!0;l.matches(n.focusableElements)&&(w=!1,"SELECT"===l.nodeName&&(n.isTouched=!1)),i.activeElement&&i.activeElement.matches(n.focusableElements)&&i.activeElement!==l&&("mouse"===s.pointerType||"mouse"!==s.pointerType&&!l.matches(n.focusableElements))&&i.activeElement.blur();const b=w&&t.allowTouchMove&&r.touchStartPreventDefault;!r.touchStartForcePreventDefault&&!b||l.isContentEditable||s.preventDefault(),r.freeMode&&r.freeMode.enabled&&t.freeMode&&t.animating&&!r.cssMode&&t.freeMode.onTouchStart(),t.emit("touchStart",s)}function W(e){const t=d(),i=this,s=i.touchEventsData,{params:n,touches:r,rtlTranslate:a,enabled:o}=i;if(!o)return;if(!n.simulateTouch&&"mouse"===e.pointerType)return;let l,c=e;if(c.originalEvent&&(c=c.originalEvent),"pointermove"===c.type){if(null!==s.touchId)return;if(c.pointerId!==s.pointerId)return}if("touchmove"===c.type){if(l=[...c.changedTouches].find(e=>e.identifier===s.touchId),!l||l.identifier!==s.touchId)return}else l=c;if(!s.isTouched)return void(s.startMoving&&s.isScrolling&&i.emit("touchMoveOpposite",c));const p=l.pageX,u=l.pageY;if(c.preventedByNestedSwiper)return r.startX=p,void(r.startY=u);if(!i.allowTouchMove)return c.target.matches(s.focusableElements)||(i.allowClick=!1),void(s.isTouched&&(Object.assign(r,{startX:p,startY:u,currentX:p,currentY:u}),s.touchStartTime=m()));if(n.touchReleaseOnEdges&&!n.loop)if(i.isVertical()){if(u<r.startY&&i.translate<=i.maxTranslate()||u>r.startY&&i.translate>=i.minTranslate())return s.isTouched=!1,void(s.isMoved=!1)}else{if(a&&(p>r.startX&&-i.translate<=i.maxTranslate()||p<r.startX&&-i.translate>=i.minTranslate()))return;if(!a&&(p<r.startX&&i.translate<=i.maxTranslate()||p>r.startX&&i.translate>=i.minTranslate()))return}if(t.activeElement&&t.activeElement.matches(s.focusableElements)&&t.activeElement!==c.target&&"mouse"!==c.pointerType&&t.activeElement.blur(),t.activeElement&&c.target===t.activeElement&&c.target.matches(s.focusableElements))return s.isMoved=!0,void(i.allowClick=!1);s.allowTouchCallbacks&&i.emit("touchMove",c),r.previousX=r.currentX,r.previousY=r.currentY,r.currentX=p,r.currentY=u;const h=r.currentX-r.startX,f=r.currentY-r.startY;if(i.params.threshold&&Math.sqrt(h**2+f**2)<i.params.threshold)return;if(void 0===s.isScrolling){let e;i.isHorizontal()&&r.currentY===r.startY||i.isVertical()&&r.currentX===r.startX?s.isScrolling=!1:h*h+f*f>=25&&(e=180*Math.atan2(Math.abs(f),Math.abs(h))/Math.PI,s.isScrolling=i.isHorizontal()?e>n.touchAngle:90-e>n.touchAngle)}if(s.isScrolling&&i.emit("touchMoveOpposite",c),void 0===s.startMoving&&(r.currentX===r.startX&&r.currentY===r.startY||(s.startMoving=!0)),s.isScrolling||"touchmove"===c.type&&s.preventTouchMoveFromPointerMove)return void(s.isTouched=!1);if(!s.startMoving)return;i.allowClick=!1,!n.cssMode&&c.cancelable&&c.preventDefault(),n.touchMoveStopPropagation&&!n.nested&&c.stopPropagation();let v=i.isHorizontal()?h:f,g=i.isHorizontal()?r.currentX-r.previousX:r.currentY-r.previousY;n.oneWayMovement&&(v=Math.abs(v)*(a?1:-1),g=Math.abs(g)*(a?1:-1)),r.diff=v,v*=n.touchRatio,a&&(v=-v,g=-g);const w=i.touchesDirection;i.swipeDirection=v>0?"prev":"next",i.touchesDirection=g>0?"prev":"next";const b=i.params.loop&&!n.cssMode,T="next"===i.touchesDirection&&i.allowSlideNext||"prev"===i.touchesDirection&&i.allowSlidePrev;if(!s.isMoved){if(b&&T&&i.loopFix({direction:i.swipeDirection}),s.startTranslate=i.getTranslate(),i.setTransition(0),i.animating){const e=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0,detail:{bySwiperTouchMove:!0}});i.wrapperEl.dispatchEvent(e)}s.allowMomentumBounce=!1,!n.grabCursor||!0!==i.allowSlideNext&&!0!==i.allowSlidePrev||i.setGrabCursor(!0),i.emit("sliderFirstMove",c)}if((new Date).getTime(),!1!==n._loopSwapReset&&s.isMoved&&s.allowThresholdMove&&w!==i.touchesDirection&&b&&T&&Math.abs(v)>=1)return Object.assign(r,{startX:p,startY:u,currentX:p,currentY:u,startTranslate:s.currentTranslate}),s.loopSwapReset=!0,void(s.startTranslate=s.currentTranslate);i.emit("sliderMove",c),s.isMoved=!0,s.currentTranslate=v+s.startTranslate;let y=!0,S=n.resistanceRatio;if(n.touchReleaseOnEdges&&(S=0),v>0?(b&&T&&s.allowThresholdMove&&s.currentTranslate>(n.centeredSlides?i.minTranslate()-i.slidesSizesGrid[i.activeIndex+1]-("auto"!==n.slidesPerView&&i.slides.length-n.slidesPerView>=2?i.slidesSizesGrid[i.activeIndex+1]+i.params.spaceBetween:0)-i.params.spaceBetween:i.minTranslate())&&i.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),s.currentTranslate>i.minTranslate()&&(y=!1,n.resistance&&(s.currentTranslate=i.minTranslate()-1+(-i.minTranslate()+s.startTranslate+v)**S))):v<0&&(b&&T&&s.allowThresholdMove&&s.currentTranslate<(n.centeredSlides?i.maxTranslate()+i.slidesSizesGrid[i.slidesSizesGrid.length-1]+i.params.spaceBetween+("auto"!==n.slidesPerView&&i.slides.length-n.slidesPerView>=2?i.slidesSizesGrid[i.slidesSizesGrid.length-1]+i.params.spaceBetween:0):i.maxTranslate())&&i.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:i.slides.length-("auto"===n.slidesPerView?i.slidesPerViewDynamic():Math.ceil(parseFloat(n.slidesPerView,10)))}),s.currentTranslate<i.maxTranslate()&&(y=!1,n.resistance&&(s.currentTranslate=i.maxTranslate()+1-(i.maxTranslate()-s.startTranslate-v)**S))),y&&(c.preventedByNestedSwiper=!0),!i.allowSlideNext&&"next"===i.swipeDirection&&s.currentTranslate<s.startTranslate&&(s.currentTranslate=s.startTranslate),!i.allowSlidePrev&&"prev"===i.swipeDirection&&s.currentTranslate>s.startTranslate&&(s.currentTranslate=s.startTranslate),i.allowSlidePrev||i.allowSlideNext||(s.currentTranslate=s.startTranslate),n.threshold>0){if(!(Math.abs(v)>n.threshold||s.allowThresholdMove))return void(s.currentTranslate=s.startTranslate);if(!s.allowThresholdMove)return s.allowThresholdMove=!0,r.startX=r.currentX,r.startY=r.currentY,s.currentTranslate=s.startTranslate,void(r.diff=i.isHorizontal()?r.currentX-r.startX:r.currentY-r.startY)}n.followFinger&&!n.cssMode&&((n.freeMode&&n.freeMode.enabled&&i.freeMode||n.watchSlidesProgress)&&(i.updateActiveIndex(),i.updateSlidesClasses()),n.freeMode&&n.freeMode.enabled&&i.freeMode&&i.freeMode.onTouchMove(),i.updateProgress(s.currentTranslate),i.setTranslate(s.currentTranslate))}function q(e){const t=this,i=t.touchEventsData;let s,n=e;if(n.originalEvent&&(n=n.originalEvent),"touchend"===n.type||"touchcancel"===n.type){if(s=[...n.changedTouches].find(e=>e.identifier===i.touchId),!s||s.identifier!==i.touchId)return}else{if(null!==i.touchId)return;if(n.pointerId!==i.pointerId)return;s=n}if(["pointercancel","pointerout","pointerleave","contextmenu"].includes(n.type)&&(!["pointercancel","contextmenu"].includes(n.type)||!t.browser.isSafari&&!t.browser.isWebView))return;i.pointerId=null,i.touchId=null;const{params:r,touches:a,rtlTranslate:o,slidesGrid:l,enabled:d}=t;if(!d)return;if(!r.simulateTouch&&"mouse"===n.pointerType)return;if(i.allowTouchCallbacks&&t.emit("touchEnd",n),i.allowTouchCallbacks=!1,!i.isTouched)return i.isMoved&&r.grabCursor&&t.setGrabCursor(!1),i.isMoved=!1,void(i.startMoving=!1);r.grabCursor&&i.isMoved&&i.isTouched&&(!0===t.allowSlideNext||!0===t.allowSlidePrev)&&t.setGrabCursor(!1);const c=m(),p=c-i.touchStartTime;if(t.allowClick){const e=n.path||n.composedPath&&n.composedPath();t.updateClickedSlide(e&&e[0]||n.target,e),t.emit("tap click",n),p<300&&c-i.lastClickTime<300&&t.emit("doubleTap doubleClick",n)}if(i.lastClickTime=m(),u(()=>{t.destroyed||(t.allowClick=!0)}),!i.isTouched||!i.isMoved||!t.swipeDirection||0===a.diff&&!i.loopSwapReset||i.currentTranslate===i.startTranslate&&!i.loopSwapReset)return i.isTouched=!1,i.isMoved=!1,void(i.startMoving=!1);let h;if(i.isTouched=!1,i.isMoved=!1,i.startMoving=!1,h=r.followFinger?o?t.translate:-t.translate:-i.currentTranslate,r.cssMode)return;if(r.freeMode&&r.freeMode.enabled)return void t.freeMode.onTouchEnd({currentPos:h});const f=h>=-t.maxTranslate()&&!t.params.loop;let v=0,g=t.slidesSizesGrid[0];for(let e=0;e<l.length;e+=e<r.slidesPerGroupSkip?1:r.slidesPerGroup){const t=e<r.slidesPerGroupSkip-1?1:r.slidesPerGroup;void 0!==l[e+t]?(f||h>=l[e]&&h<l[e+t])&&(v=e,g=l[e+t]-l[e]):(f||h>=l[e])&&(v=e,g=l[l.length-1]-l[l.length-2])}let w=null,b=null;r.rewind&&(t.isBeginning?b=r.virtual&&r.virtual.enabled&&t.virtual?t.virtual.slides.length-1:t.slides.length-1:t.isEnd&&(w=0));const T=(h-l[v])/g,y=v<r.slidesPerGroupSkip-1?1:r.slidesPerGroup;if(p>r.longSwipesMs){if(!r.longSwipes)return void t.slideTo(t.activeIndex);"next"===t.swipeDirection&&(T>=r.longSwipesRatio?t.slideTo(r.rewind&&t.isEnd?w:v+y):t.slideTo(v)),"prev"===t.swipeDirection&&(T>1-r.longSwipesRatio?t.slideTo(v+y):null!==b&&T<0&&Math.abs(T)>r.longSwipesRatio?t.slideTo(b):t.slideTo(v))}else{if(!r.shortSwipes)return void t.slideTo(t.activeIndex);!t.navigation||n.target!==t.navigation.nextEl&&n.target!==t.navigation.prevEl?("next"===t.swipeDirection&&t.slideTo(null!==w?w:v+y),"prev"===t.swipeDirection&&t.slideTo(null!==b?b:v)):n.target===t.navigation.nextEl?t.slideTo(v+y):t.slideTo(v)}}function Y(){const e=this,{params:t,el:i}=e;if(i&&0===i.offsetWidth)return;t.breakpoints&&e.setBreakpoint();const{allowSlideNext:s,allowSlidePrev:n,snapGrid:r}=e,a=e.virtual&&e.params.virtual.enabled;e.allowSlideNext=!0,e.allowSlidePrev=!0,e.updateSize(),e.updateSlides(),e.updateSlidesClasses();const o=a&&t.loop;!("auto"===t.slidesPerView||t.slidesPerView>1)||!e.isEnd||e.isBeginning||e.params.centeredSlides||o?e.params.loop&&!a?e.slideToLoop(e.realIndex,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0):e.slideTo(e.slides.length-1,0,!1,!0),e.autoplay&&e.autoplay.running&&e.autoplay.paused&&(clearTimeout(e.autoplay.resizeTimeout),e.autoplay.resizeTimeout=setTimeout(()=>{e.autoplay&&e.autoplay.running&&e.autoplay.paused&&e.autoplay.resume()},500)),e.allowSlidePrev=n,e.allowSlideNext=s,e.params.watchOverflow&&r!==e.snapGrid&&e.checkOverflow()}function X(e){const t=this;t.enabled&&(t.allowClick||(t.params.preventClicks&&e.preventDefault(),t.params.preventClicksPropagation&&t.animating&&(e.stopPropagation(),e.stopImmediatePropagation())))}function K(){const e=this,{wrapperEl:t,rtlTranslate:i,enabled:s}=e;if(!s)return;let n;e.previousTranslate=e.translate,e.isHorizontal()?e.translate=-t.scrollLeft:e.translate=-t.scrollTop,0===e.translate&&(e.translate=0),e.updateActiveIndex(),e.updateSlidesClasses();const r=e.maxTranslate()-e.minTranslate();n=0===r?0:(e.translate-e.minTranslate())/r,n!==e.progress&&e.updateProgress(i?-e.translate:e.translate),e.emit("setTranslate",e.translate,!1)}function Q(e){const t=this;B(t,e.target),t.params.cssMode||"auto"!==t.params.slidesPerView&&!t.params.autoHeight||t.update()}function U(){const e=this;e.documentTouchHandlerProceeded||(e.documentTouchHandlerProceeded=!0,e.params.touchReleaseOnEdges&&(e.el.style.touchAction="auto"))}const Z=(e,t)=>{const i=d(),{params:s,el:n,wrapperEl:r,device:a}=e,o=!!s.nested,l="on"===t?"addEventListener":"removeEventListener",c=t;n&&"string"!=typeof n&&(i[l]("touchstart",e.onDocumentTouchStart,{passive:!1,capture:o}),n[l]("touchstart",e.onTouchStart,{passive:!1}),n[l]("pointerdown",e.onTouchStart,{passive:!1}),i[l]("touchmove",e.onTouchMove,{passive:!1,capture:o}),i[l]("pointermove",e.onTouchMove,{passive:!1,capture:o}),i[l]("touchend",e.onTouchEnd,{passive:!0}),i[l]("pointerup",e.onTouchEnd,{passive:!0}),i[l]("pointercancel",e.onTouchEnd,{passive:!0}),i[l]("touchcancel",e.onTouchEnd,{passive:!0}),i[l]("pointerout",e.onTouchEnd,{passive:!0}),i[l]("pointerleave",e.onTouchEnd,{passive:!0}),i[l]("contextmenu",e.onTouchEnd,{passive:!0}),(s.preventClicks||s.preventClicksPropagation)&&n[l]("click",e.onClick,!0),s.cssMode&&r[l]("scroll",e.onScroll),s.updateOnWindowResize?e[c](a.ios||a.android?"resize orientationchange observerUpdate":"resize observerUpdate",Y,!0):e[c]("observerUpdate",Y,!0),n[l]("load",e.onLoad,{capture:!0}))},J=(e,t)=>e.grid&&t.grid&&t.grid.rows>1;var ee={setBreakpoint:function(){const e=this,{realIndex:t,initialized:i,params:s,el:n}=e,r=s.breakpoints;if(!r||r&&0===Object.keys(r).length)return;const a=d(),o="window"!==s.breakpointsBase&&s.breakpointsBase?"container":s.breakpointsBase,l=["window","container"].includes(s.breakpointsBase)||!s.breakpointsBase?e.el:a.querySelector(s.breakpointsBase),c=e.getBreakpoint(r,o,l);if(!c||e.currentBreakpoint===c)return;const p=(c in r?r[c]:void 0)||e.originalParams,u=J(e,s),m=J(e,p),h=e.params.grabCursor,f=p.grabCursor,g=s.enabled;u&&!m?(n.classList.remove(`${s.containerModifierClass}grid`,`${s.containerModifierClass}grid-column`),e.emitContainerClasses()):!u&&m&&(n.classList.add(`${s.containerModifierClass}grid`),(p.grid.fill&&"column"===p.grid.fill||!p.grid.fill&&"column"===s.grid.fill)&&n.classList.add(`${s.containerModifierClass}grid-column`),e.emitContainerClasses()),h&&!f?e.unsetGrabCursor():!h&&f&&e.setGrabCursor(),["navigation","pagination","scrollbar"].forEach(t=>{if(void 0===p[t])return;const i=s[t]&&s[t].enabled,n=p[t]&&p[t].enabled;i&&!n&&e[t].disable(),!i&&n&&e[t].enable()});const w=p.direction&&p.direction!==s.direction,b=s.loop&&(p.slidesPerView!==s.slidesPerView||w),T=s.loop;w&&i&&e.changeDirection(),v(e.params,p);const y=e.params.enabled,S=e.params.loop;Object.assign(e,{allowTouchMove:e.params.allowTouchMove,allowSlideNext:e.params.allowSlideNext,allowSlidePrev:e.params.allowSlidePrev}),g&&!y?e.disable():!g&&y&&e.enable(),e.currentBreakpoint=c,e.emit("_beforeBreakpoint",p),i&&(b?(e.loopDestroy(),e.loopCreate(t),e.updateSlides()):!T&&S?(e.loopCreate(t),e.updateSlides()):T&&!S&&e.loopDestroy()),e.emit("breakpoint",p)},getBreakpoint:function(e,t,i){if(void 0===t&&(t="window"),!e||"container"===t&&!i)return;let s=!1;const n=p(),r="window"===t?n.innerHeight:i.clientHeight,a=Object.keys(e).map(e=>{if("string"==typeof e&&0===e.indexOf("@")){const t=parseFloat(e.substr(1));return{value:r*t,point:e}}return{value:e,point:e}});a.sort((e,t)=>parseInt(e.value,10)-parseInt(t.value,10));for(let e=0;e<a.length;e+=1){const{point:r,value:o}=a[e];"window"===t?n.matchMedia(`(min-width: ${o}px)`).matches&&(s=r):o<=i.clientWidth&&(s=r)}return s||"max"}},te={init:!0,direction:"horizontal",oneWayMovement:!1,swiperElementNodeName:"SWIPER-CONTAINER",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,eventsPrefix:"swiper",enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopAddBlankSlides:!0,loopAdditionalSlides:0,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-blank",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideFullyVisibleClass:"swiper-slide-fully-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};function ie(e,t){return function(i){void 0===i&&(i={});const s=Object.keys(i)[0],n=i[s];"object"==typeof n&&null!==n?(!0===e[s]&&(e[s]={enabled:!0}),"navigation"===s&&e[s]&&e[s].enabled&&!e[s].prevEl&&!e[s].nextEl&&(e[s].auto=!0),["pagination","scrollbar"].indexOf(s)>=0&&e[s]&&e[s].enabled&&!e[s].el&&(e[s].auto=!0),s in e&&"enabled"in n?("object"!=typeof e[s]||"enabled"in e[s]||(e[s].enabled=!0),e[s]||(e[s]={enabled:!1}),v(t,i)):v(t,i)):v(t,i)}}const se={eventsEmitter:z,update:D,translate:N,transition:{setTransition:function(e,t){const i=this;i.params.cssMode||(i.wrapperEl.style.transitionDuration=`${e}ms`,i.wrapperEl.style.transitionDelay=0===e?"0ms":""),i.emit("setTransition",e,t)},transitionStart:function(e,t){void 0===e&&(e=!0);const i=this,{params:s}=i;s.cssMode||(s.autoHeight&&i.updateAutoHeight(),V({swiper:i,runCallbacks:e,direction:t,step:"Start"}))},transitionEnd:function(e,t){void 0===e&&(e=!0);const i=this,{params:s}=i;i.animating=!1,s.cssMode||(i.setTransition(0),V({swiper:i,runCallbacks:e,direction:t,step:"End"}))}},slide:H,loop:F,grabCursor:{setGrabCursor:function(e){const t=this;if(!t.params.simulateTouch||t.params.watchOverflow&&t.isLocked||t.params.cssMode)return;const i="container"===t.params.touchEventsTarget?t.el:t.wrapperEl;t.isElement&&(t.__preventObserver__=!0),i.style.cursor="move",i.style.cursor=e?"grabbing":"grab",t.isElement&&requestAnimationFrame(()=>{t.__preventObserver__=!1})},unsetGrabCursor:function(){const e=this;e.params.watchOverflow&&e.isLocked||e.params.cssMode||(e.isElement&&(e.__preventObserver__=!0),e["container"===e.params.touchEventsTarget?"el":"wrapperEl"].style.cursor="",e.isElement&&requestAnimationFrame(()=>{e.__preventObserver__=!1}))}},events:{attachEvents:function(){const e=this,{params:t}=e;e.onTouchStart=R.bind(e),e.onTouchMove=W.bind(e),e.onTouchEnd=q.bind(e),e.onDocumentTouchStart=U.bind(e),t.cssMode&&(e.onScroll=K.bind(e)),e.onClick=X.bind(e),e.onLoad=Q.bind(e),Z(e,"on")},detachEvents:function(){Z(this,"off")}},breakpoints:ee,checkOverflow:{checkOverflow:function(){const e=this,{isLocked:t,params:i}=e,{slidesOffsetBefore:s}=i;if(s){const t=e.slides.length-1,i=e.slidesGrid[t]+e.slidesSizesGrid[t]+2*s;e.isLocked=e.size>i}else e.isLocked=1===e.snapGrid.length;!0===i.allowSlideNext&&(e.allowSlideNext=!e.isLocked),!0===i.allowSlidePrev&&(e.allowSlidePrev=!e.isLocked),t&&t!==e.isLocked&&(e.isEnd=!1),t!==e.isLocked&&e.emit(e.isLocked?"lock":"unlock")}},classes:{addClasses:function(){const e=this,{classNames:t,params:i,rtl:s,el:n,device:r}=e,a=function(e,t){const i=[];return e.forEach(e=>{"object"==typeof e?Object.keys(e).forEach(s=>{e[s]&&i.push(t+s)}):"string"==typeof e&&i.push(t+e)}),i}(["initialized",i.direction,{"free-mode":e.params.freeMode&&i.freeMode.enabled},{autoheight:i.autoHeight},{rtl:s},{grid:i.grid&&i.grid.rows>1},{"grid-column":i.grid&&i.grid.rows>1&&"column"===i.grid.fill},{android:r.android},{ios:r.ios},{"css-mode":i.cssMode},{centered:i.cssMode&&i.centeredSlides},{"watch-progress":i.watchSlidesProgress}],i.containerModifierClass);t.push(...a),n.classList.add(...t),e.emitContainerClasses()},removeClasses:function(){const{el:e,classNames:t}=this;e&&"string"!=typeof e&&(e.classList.remove(...t),this.emitContainerClasses())}}},ne={};class re{constructor(){let e,t;for(var i=arguments.length,s=new Array(i),n=0;n<i;n++)s[n]=arguments[n];1===s.length&&s[0].constructor&&"Object"===Object.prototype.toString.call(s[0]).slice(8,-1)?t=s[0]:[e,t]=s,t||(t={}),t=v({},t),e&&!t.el&&(t.el=e);const r=d();if(t.el&&"string"==typeof t.el&&r.querySelectorAll(t.el).length>1){const e=[];return r.querySelectorAll(t.el).forEach(i=>{const s=v({},t,{el:i});e.push(new re(s))}),e}const a=this;a.__swiper__=!0,a.support=P(),a.device=I({userAgent:t.userAgent}),a.browser=L(),a.eventsListeners={},a.eventsAnyListeners=[],a.modules=[...a.__modules__],t.modules&&Array.isArray(t.modules)&&a.modules.push(...t.modules);const o={};a.modules.forEach(e=>{e({params:t,swiper:a,extendParams:ie(t,o),on:a.on.bind(a),once:a.once.bind(a),off:a.off.bind(a),emit:a.emit.bind(a)})});const l=v({},te,o);return a.params=v({},l,ne,t),a.originalParams=v({},a.params),a.passedParams=v({},t),a.params&&a.params.on&&Object.keys(a.params.on).forEach(e=>{a.on(e,a.params.on[e])}),a.params&&a.params.onAny&&a.onAny(a.params.onAny),Object.assign(a,{enabled:a.params.enabled,el:e,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal:()=>"horizontal"===a.params.direction,isVertical:()=>"vertical"===a.params.direction,activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return Math.trunc(this.translate/2**23)*2**23},allowSlideNext:a.params.allowSlideNext,allowSlidePrev:a.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:a.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,pointerId:null,touchId:null},allowClick:!0,allowTouchMove:a.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),a.emit("_swiper"),a.params.init&&a.init(),a}getDirectionLabel(e){return this.isHorizontal()?e:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[e]}getSlideIndex(e){const{slidesEl:t,params:i}=this,s=x(b(t,`.${i.slideClass}, swiper-slide`)[0]);return x(e)-s}getSlideIndexByData(e){return this.getSlideIndex(this.slides.find(t=>1*t.getAttribute("data-swiper-slide-index")===e))}getSlideIndexWhenGrid(e){return this.grid&&this.params.grid&&this.params.grid.rows>1&&("column"===this.params.grid.fill?e=Math.floor(e/this.params.grid.rows):"row"===this.params.grid.fill&&(e%=Math.ceil(this.slides.length/this.params.grid.rows))),e}recalcSlides(){const{slidesEl:e,params:t}=this;this.slides=b(e,`.${t.slideClass}, swiper-slide`)}enable(){const e=this;e.enabled||(e.enabled=!0,e.params.grabCursor&&e.setGrabCursor(),e.emit("enable"))}disable(){const e=this;e.enabled&&(e.enabled=!1,e.params.grabCursor&&e.unsetGrabCursor(),e.emit("disable"))}setProgress(e,t){const i=this;e=Math.min(Math.max(e,0),1);const s=i.minTranslate(),n=(i.maxTranslate()-s)*e+s;i.translateTo(n,void 0===t?0:t),i.updateActiveIndex(),i.updateSlidesClasses()}emitContainerClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const t=e.el.className.split(" ").filter(t=>0===t.indexOf("swiper")||0===t.indexOf(e.params.containerModifierClass));e.emit("_containerClasses",t.join(" "))}getSlideClasses(e){const t=this;return t.destroyed?"":e.className.split(" ").filter(e=>0===e.indexOf("swiper-slide")||0===e.indexOf(t.params.slideClass)).join(" ")}emitSlidesClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const t=[];e.slides.forEach(i=>{const s=e.getSlideClasses(i);t.push({slideEl:i,classNames:s}),e.emit("_slideClass",i,s)}),e.emit("_slideClasses",t)}slidesPerViewDynamic(e,t){void 0===e&&(e="current"),void 0===t&&(t=!1);const{params:i,slides:s,slidesGrid:n,slidesSizesGrid:r,size:a,activeIndex:o}=this;let l=1;if("number"==typeof i.slidesPerView)return i.slidesPerView;if(i.centeredSlides){let e,t=s[o]?Math.ceil(s[o].swiperSlideSize):0;for(let i=o+1;i<s.length;i+=1)s[i]&&!e&&(t+=Math.ceil(s[i].swiperSlideSize),l+=1,t>a&&(e=!0));for(let i=o-1;i>=0;i-=1)s[i]&&!e&&(t+=s[i].swiperSlideSize,l+=1,t>a&&(e=!0))}else if("current"===e)for(let e=o+1;e<s.length;e+=1)(t?n[e]+r[e]-n[o]<a:n[e]-n[o]<a)&&(l+=1);else for(let e=o-1;e>=0;e-=1)n[o]-n[e]<a&&(l+=1);return l}update(){const e=this;if(!e||e.destroyed)return;const{snapGrid:t,params:i}=e;function s(){const t=e.rtlTranslate?-1*e.translate:e.translate,i=Math.min(Math.max(t,e.maxTranslate()),e.minTranslate());e.setTranslate(i),e.updateActiveIndex(),e.updateSlidesClasses()}let n;if(i.breakpoints&&e.setBreakpoint(),[...e.el.querySelectorAll('[loading="lazy"]')].forEach(t=>{t.complete&&B(e,t)}),e.updateSize(),e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),i.freeMode&&i.freeMode.enabled&&!i.cssMode)s(),i.autoHeight&&e.updateAutoHeight();else{if(("auto"===i.slidesPerView||i.slidesPerView>1)&&e.isEnd&&!i.centeredSlides){const t=e.virtual&&i.virtual.enabled?e.virtual.slides:e.slides;n=e.slideTo(t.length-1,0,!1,!0)}else n=e.slideTo(e.activeIndex,0,!1,!0);n||s()}i.watchOverflow&&t!==e.snapGrid&&e.checkOverflow(),e.emit("update")}changeDirection(e,t){void 0===t&&(t=!0);const i=this,s=i.params.direction;return e||(e="horizontal"===s?"vertical":"horizontal"),e===s||"horizontal"!==e&&"vertical"!==e||(i.el.classList.remove(`${i.params.containerModifierClass}${s}`),i.el.classList.add(`${i.params.containerModifierClass}${e}`),i.emitContainerClasses(),i.params.direction=e,i.slides.forEach(t=>{"vertical"===e?t.style.width="":t.style.height=""}),i.emit("changeDirection"),t&&i.update()),i}changeLanguageDirection(e){const t=this;t.rtl&&"rtl"===e||!t.rtl&&"ltr"===e||(t.rtl="rtl"===e,t.rtlTranslate="horizontal"===t.params.direction&&t.rtl,t.rtl?(t.el.classList.add(`${t.params.containerModifierClass}rtl`),t.el.dir="rtl"):(t.el.classList.remove(`${t.params.containerModifierClass}rtl`),t.el.dir="ltr"),t.update())}mount(e){const t=this;if(t.mounted)return!0;let i=e||t.params.el;if("string"==typeof i&&(i=document.querySelector(i)),!i)return!1;i.swiper=t,i.parentNode&&i.parentNode.host&&i.parentNode.host.nodeName===t.params.swiperElementNodeName.toUpperCase()&&(t.isElement=!0);const s=()=>`.${(t.params.wrapperClass||"").trim().split(" ").join(".")}`;let n=i&&i.shadowRoot&&i.shadowRoot.querySelector?i.shadowRoot.querySelector(s()):b(i,s())[0];return!n&&t.params.createElements&&(n=y("div",t.params.wrapperClass),i.append(n),b(i,`.${t.params.slideClass}`).forEach(e=>{n.append(e)})),Object.assign(t,{el:i,wrapperEl:n,slidesEl:t.isElement&&!i.parentNode.host.slideSlots?i.parentNode.host:n,hostEl:t.isElement?i.parentNode.host:i,mounted:!0,rtl:"rtl"===i.dir.toLowerCase()||"rtl"===S(i,"direction"),rtlTranslate:"horizontal"===t.params.direction&&("rtl"===i.dir.toLowerCase()||"rtl"===S(i,"direction")),wrongRTL:"-webkit-box"===S(n,"display")}),!0}init(e){const t=this;if(t.initialized)return t;if(!1===t.mount(e))return t;t.emit("beforeInit"),t.params.breakpoints&&t.setBreakpoint(),t.addClasses(),t.updateSize(),t.updateSlides(),t.params.watchOverflow&&t.checkOverflow(),t.params.grabCursor&&t.enabled&&t.setGrabCursor(),t.params.loop&&t.virtual&&t.params.virtual.enabled?t.slideTo(t.params.initialSlide+t.virtual.slidesBefore,0,t.params.runCallbacksOnInit,!1,!0):t.slideTo(t.params.initialSlide,0,t.params.runCallbacksOnInit,!1,!0),t.params.loop&&t.loopCreate(void 0,!0),t.attachEvents();const i=[...t.el.querySelectorAll('[loading="lazy"]')];return t.isElement&&i.push(...t.hostEl.querySelectorAll('[loading="lazy"]')),i.forEach(e=>{e.complete?B(t,e):e.addEventListener("load",e=>{B(t,e.target)})}),_(t),t.initialized=!0,_(t),t.emit("init"),t.emit("afterInit"),t}destroy(e,t){void 0===e&&(e=!0),void 0===t&&(t=!0);const i=this,{params:s,el:n,wrapperEl:r,slides:a}=i;return void 0===i.params||i.destroyed||(i.emit("beforeDestroy"),i.initialized=!1,i.detachEvents(),s.loop&&i.loopDestroy(),t&&(i.removeClasses(),n&&"string"!=typeof n&&n.removeAttribute("style"),r&&r.removeAttribute("style"),a&&a.length&&a.forEach(e=>{e.classList.remove(s.slideVisibleClass,s.slideFullyVisibleClass,s.slideActiveClass,s.slideNextClass,s.slidePrevClass),e.removeAttribute("style"),e.removeAttribute("data-swiper-slide-index")})),i.emit("destroy"),Object.keys(i.eventsListeners).forEach(e=>{i.off(e)}),!1!==e&&(i.el&&"string"!=typeof i.el&&(i.el.swiper=null),function(e){const t=e;Object.keys(t).forEach(e=>{try{t[e]=null}catch(e){}try{delete t[e]}catch(e){}})}(i)),i.destroyed=!0),null}static extendDefaults(e){v(ne,e)}static get extendedDefaults(){return ne}static get defaults(){return te}static installModule(e){re.prototype.__modules__||(re.prototype.__modules__=[]);const t=re.prototype.__modules__;"function"==typeof e&&t.indexOf(e)<0&&t.push(e)}static use(e){return Array.isArray(e)?(e.forEach(e=>re.installModule(e)),re):(re.installModule(e),re)}}Object.keys(se).forEach(e=>{Object.keys(se[e]).forEach(t=>{re.prototype[t]=se[e][t]})}),re.use([function(e){let{swiper:t,on:i,emit:s}=e;const n=p();let r=null,a=null;const o=()=>{t&&!t.destroyed&&t.initialized&&(s("beforeResize"),s("resize"))},l=()=>{t&&!t.destroyed&&t.initialized&&s("orientationchange")};i("init",()=>{t.params.resizeObserver&&void 0!==n.ResizeObserver?t&&!t.destroyed&&t.initialized&&(r=new ResizeObserver(e=>{a=n.requestAnimationFrame(()=>{const{width:i,height:s}=t;let n=i,r=s;e.forEach(e=>{let{contentBoxSize:i,contentRect:s,target:a}=e;a&&a!==t.el||(n=s?s.width:(i[0]||i).inlineSize,r=s?s.height:(i[0]||i).blockSize)}),n===i&&r===s||o()})}),r.observe(t.el)):(n.addEventListener("resize",o),n.addEventListener("orientationchange",l))}),i("destroy",()=>{a&&n.cancelAnimationFrame(a),r&&r.unobserve&&t.el&&(r.unobserve(t.el),r=null),n.removeEventListener("resize",o),n.removeEventListener("orientationchange",l)})},function(e){let{swiper:t,extendParams:i,on:s,emit:n}=e;const r=[],a=p(),o=function(e,i){void 0===i&&(i={});const s=new(a.MutationObserver||a.WebkitMutationObserver)(e=>{if(t.__preventObserver__)return;if(1===e.length)return void n("observerUpdate",e[0]);const i=function(){n("observerUpdate",e[0])};a.requestAnimationFrame?a.requestAnimationFrame(i):a.setTimeout(i,0)});s.observe(e,{attributes:void 0===i.attributes||i.attributes,childList:t.isElement||(void 0===i.childList||i).childList,characterData:void 0===i.characterData||i.characterData}),r.push(s)};i({observer:!1,observeParents:!1,observeSlideChildren:!1}),s("init",()=>{if(t.params.observer){if(t.params.observeParents){const e=function(e){const t=[];let i=e.parentElement;for(;i;)t.push(i),i=i.parentElement;return t}(t.hostEl);for(let t=0;t<e.length;t+=1)o(e[t])}o(t.hostEl,{childList:t.params.observeSlideChildren}),o(t.wrapperEl,{attributes:!1})}}),s("destroy",()=>{r.forEach(e=>{e.disconnect()}),r.splice(0,r.length)})}]);var ae,oe,le=!1,de=null,ce=!1,pe=null,ue=0,me=!1,he=null,fe=(oe=!1,ae=navigator.userAgent||navigator.vendor||window.opera,(/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(ae)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(ae.substr(0,4)))&&(oe=!0),oe);function ve(e){return de=[],[].push.apply(de,e),e}function ge(e){return he=[],[].push.apply(he,e),e}function we(e){return pe=[],[].push.apply(pe,e),e}s().initializers.add("wusong8899-client1-header-adv",function(){(0,t.extend)(r().prototype,"view",function(e){var t=s().current.get("routeName");t&&("tags"!==t||function(e){fe&&($(".item-newDiscussion").find("span.Button-label").html("<div class='buttonRegister'>登录</div>"),$(".item-newDiscussion").find("span.Button-label").css("display","block"),$(".item-newDiscussion").find("span.Button-label").css("font-size","14px"),$(".item-newDiscussion").find("span.Button-label").css("word-spacing","-1px")),$(".item-newDiscussion").find("i").css("display","none"),$(".item-nav").remove(),$(".TagTiles").css("display","none");var t=setInterval(function(){if(e.dom&&(clearInterval(t),void 0!==e.dom)){var i=s().forum.attribute("Client1HeaderAdvTransitionTime");i||(i=5e3);var n=2*$(window).width()-50,r=document.getElementById("swiperAdContainer");if(null!==r)return;(r=document.createElement("div")).className="swiperAdContainer",r.id="swiperAdContainer",!0===fe&&(r.style.width=n+"px",r.style.marginLeft=-.254*n+"px");var a=document.createElement("div");a.className="swiper adSwiper",r.appendChild(a);var o=document.createElement("div");o.className="swiper-wrapper",a.appendChild(o);for(var l=1;l<=30;l++){var d=document.createElement("div"),c=s().forum.attribute("Client1HeaderAdvImage"+l),p=s().forum.attribute("Client1HeaderAdvLink"+l);c&&(d.className="swiper-slide",d.innerHTML="<img onclick='window.location.href=\""+p+"\"' src='"+c+"' />",o.appendChild(d))}var u=document.createElement("div");u.className="swiper-button-next",a.appendChild(u);var m=document.createElement("div");m.className="swiper-button-prev",a.appendChild(m);var h=document.createElement("div");h.className="swiper-pagination",a.appendChild(h),$("#content .container").prepend(r),new re(".adSwiper",{autoplay:{delay:i},loop:!0,spaceBetween:30,effect:"coverflow",centeredSlides:!0,slidesPerView:2,coverflowEffect:{rotate:0,depth:100,modifier:1,slideShadows:!0,stretch:0},pagination:{el:".swiper-pagination",type:"bullets"},navigation:{nextEl:".swiper-button-next",prevEl:".swiper-button-prev"},modules:[re,re,re,re]}),function(){!1===ce&&(ce=!0,s().store.find("linksQueueList").catch(function(){}).then(we.bind(this)))}(),function(){!1===le&&(le=!0,s().store.find("syncTronscanList").catch(function(){}).then(ve.bind(this)))}(),function(){!1===me&&(me=!0,s().store.find("buttonsCustomizationList").catch(function(){}).then(ge.bind(this)))}();var f=setInterval(function(){var e;null!==de&&null!==pe&&null!==he&&(clearInterval(f),0===$("#swiperTagContainer").length&&(function(){var e=$(".TagTile");if(0===$("#swiperTagContainer").length){var t=document.createElement("div");t.className="swiperTagContainer",t.id="swiperTagContainer";var i=document.createElement("div");i.className="swiper tagSwiper";var s=document.createElement("div");s.className="TagTextOuterContainer",t.appendChild(s),s.appendChild(i);var n=document.createElement("div");n.className="swiper-wrapper",n.id="swiperTagWrapper",i.appendChild(n);for(var r=0;r<e.length;r++){var a=e[r],o=$(a).find("a").attr("href"),l=$(a).css("background"),d=$(a).find(".TagTile-name").text(),c=$(a).find(".TagTile-name").css("color"),p=($(a).find(".TagTile-description").text(),$(a).find(".TagTile-description").css("color"),document.createElement("div"));p.className="swiper-slide swiper-slide-tag",p.innerHTML="<a href='"+o+"'><div class='"+(fe?"swiper-slide-tag-inner-mobile":"swiper-slide-tag-inner")+"' style='background:"+l+";background-size: cover;background-position: center;background-repeat: no-repeat;'><div style='font-weight:bold;font-size:14px;color:"+c+"'>"+d+"</div></div></a>",n.appendChild(p)}$("#content .container .TagsPage-content").prepend(t),$(s).prepend("<div class='TagTextContainer'><div class='TagTextIcon'></div>中文玩家社区资讯</div>"),$(s).append('<div style="text-align:center;padding-top: 10px;"><button class="Button Button--primary" type="button" style="font-weight: normal !important; color:#ffa000; background: #1a1d2e !important;border-radius: 2rem !important;"><div style="margin-top: 5px;" class="Button-label"><img onClick="window.open(\'https://kick.com/wangming886\', \'_blank\')" style="width: 32px;" src="https://mutluresim.com/images/2023/04/10/KcgSG.png"><img onClick="window.open(\'https://m.facebook.com\', \'_blank\')" style="width: 32px;margin-left: 20px;" src="https://mutluresim.com/images/2023/04/10/KcF6i.png"><img onClick="window.open(\'https://twitter.com/youngron131_\', \'_blank\')" style="width: 32px;margin-left: 20px;" src="https://mutluresim.com/images/2023/04/10/KcDas.png"><img onClick="window.open(\'https://m.youtube.com/@ag8888\',\'_blank\')" style="width: 32px;margin-left: 20px;" src="https://mutluresim.com/images/2023/04/10/KcQjd.png"><img onClick="window.open(\'https://www.instagram.com/p/CqLvh94Sk8F/?igshid=YmMyMTA2M2Y=\', \'_blank\')" style="width: 32px;margin-left: 20px;" src="https://mutluresim.com/images/2023/04/10/KcBAL.png"></div></button></div>'),$(".TagTiles").remove(),!0===fe&&($("#app").css("overflow-x","hidden"),$(".App-content").css("min-height","auto"),$(".App-content").css("background","")),new re(".tagSwiper",{loop:!0,spaceBetween:fe?90:10,slidesPerView:fe?2:7,autoplay:{delay:3e3,disableOnInteraction:!1},modules:[re]}),function(){var e=document.getElementById("TronscanTextContainer");if(null===e){(e=document.createElement("div")).id="TronscanTextContainer",e.innerHTML="<div class='TronscanTextIcon'></div>知名博彩公司USDT/TRC公开链钱包额度",e.className="TronscanTextContainer",$("#swiperTagContainer").append(e);var t=document.createElement("div");t.className="swiper tronscanSwiper",$("#swiperTagContainer").append(t);var i=document.createElement("div");i.className="swiper-wrapper",t.appendChild(i);for(var s=0;s<de.length;s++){var n=de[s],r=(n.name(),parseInt(n.valueUsd())+" USD"),a="url("+n.img()+");",o=document.createElement("div");o.className="swiper-slide swiper-slide-tag",o.innerHTML="<div style='width:100px;height:130px;border-radius: 12px;background: "+a+";background-size: cover;background-position: center;background-repeat: no-repeat;word-break: break-all;'><div style='display:inline-block;position: absolute;top: 56px;height:20px;width:100px;background: rgba(255,255,255,0.5);'></div><div class='tronscanMask'><div style='display: flex;width: 90px;justify-content: center;font-weight: bold;color:#02F78E;font-size:10px;'><span>"+r+"</span></div></div></div>",i.appendChild(o)}new re(".tronscanSwiper",{loop:!0,spaceBetween:fe?80:10,slidesPerView:fe?4:7})}}()}}(),function(){$(".swiperTagContainer").css("height");var e=document.createElement("div");e.className="zhiboContainer",e.style.height=$(".swiperTagContainer").css("height");e.innerHTML="<div id='linksQueueRefresh' style='z-index: 1000;display:inline-block;scale:0.8;position: fixed;bottom: "+(fe?0:-6)+"px;'><div style='padding-top: 0px;padding-bottom: 0px;' class='selectTitle'><div class='switch-btns'><div class='btns-container'><button type='button' class='u-btn'><div id='refreshZhiBoButton' class='u-btn-text'>刷新直播间</div></button><div id='buttonSelectedBackground' class='selected-bg' style='left: 0px; top: 0px; opacity: 1;'></div></div></div></div></div><div class='zhiboSubContainer'><div id='linksQueuePrev' style='display:inline-block;scale:0.8'><div style='padding-top: 0px;padding-bottom: 0px;' class='selectTitle'><div class='switch-btns'><div class='btns-container'><button type='button' class='u-btn'><div style='color:#666' id='prevZhiBoButton' class='u-btn-text'>上个直播间</div></button><div id='buttonSelectedBackground' class='selected-bg' style='left: 0px; top: 0px; opacity: 1;'></div></div></div></div></div><div id='linksQueueNext' style='display:inline-block;scale:0.8'><div style='padding-top: 0px;padding-bottom: 0px;' class='selectTitle'><div class='switch-btns'><div class='btns-container'><button type='button' class='u-btn'><div id='nextZhiBoButton' class='u-btn-text'>切换直播间</div></button><div id='buttonSelectedBackground' class='selected-bg' style='left: 0px; top: 0px; opacity: 1;'></div></div></div></div></div></div><iframe id='zhiboIframe' name='contentOnly' class='zhiboIframe' src=''></iframe>",$("#content .TagsPage-content").prepend(e);var t=fe?"touchend":"click",i=document.getElementById("zhiboIframe");$("#linksQueueRefresh").on(t,function(){i.src="",setTimeout(function(){var e=pe[ue].attribute("links");i.src=e},100)}),$("#linksQueuePrev").on(t,function(){if(ue--,void 0!==pe[ue]){var e=pe[ue].attribute("links");i.src=e}$("#nextZhiBoButton").css("color",""),void 0===pe[ue-1]?$("#prevZhiBoButton").css("color","#666"):$("#prevZhiBoButton").css("color","")}),$("#linksQueueNext").on(t,function(){if(ue++,void 0!==pe[ue]){var e=pe[ue].attribute("links");i.src=e}$("#prevZhiBoButton").css("color",""),void 0===pe[ue+1]?$("#nextZhiBoButton").css("color","#666"):$("#nextZhiBoButton").css("color","")})}(),function(){$(".swiperTagContainer").css("height");var e=document.createElement("div");e.className="youxiContainer",e.style.height=$(".swiperTagContainer").css("height"),e.innerText=s().translator.trans("wusong8899-client1.forum.under-construction"),$("#content .TagsPage-content").prepend(e)}(),function(){$(".swiperTagContainer").css("height");var e=document.createElement("div");e.className="buttonCustomizationContainer",e.style.height=$(".swiperTagContainer").css("height"),e.innerHTML="<iframe id='customButtonIframe' name='contentOnly' class='customButtonIframe' src=''></iframe>",$("#content .TagsPage-content").prepend(e)}(),function(){$(".swiperTagContainer").css("height");var e=document.createElement("div");e.className="shangchengContainer",e.style.height=$(".swiperTagContainer").css("height"),e.innerText=s().translator.trans("wusong8899-client1.forum.under-construction"),$("#content .TagsPage-content").prepend(e)}(),function(){var e=document.getElementById("selectTitleContainer");if(null===e){(e=document.createElement("div")).id="selectTitleContainer",e.className="selectTitleContainer";for(var t="",i={},s=3,n=0;n<he.length;n++){var r=he[n],a=r.name(),o=r.icon(),l=(r.color(),r.url());s++,i[s]={url:l},t+='<button id="client1HeaderButton'+s+'" number="'+s+'" type="button" class="u-btn"><i class="'+o+'"></i><div class="u-btn-text">'+a+"</div></button>"}var d=document.createElement("div");d.className="selectTitle",d.innerHTML='<div class="switch-btns" style="max-width:'+$(".TagsPage-content").width()+'px"><div class="btns-container"><button id="client1HeaderButton0" type="button" class="u-btn" number="0"><i class="fas fa-paw"></i><div class="u-btn-text">论坛</div></button><button id="client1HeaderButton1" number="1" type="button" class="u-btn"><i class="fab fa-twitch"></i><div class="u-btn-text">直播</div></button><button id="client1HeaderButton2" number="2" type="button" class="u-btn"><i class="fas fa-dice"></i><div class="u-btn-text">游戏</div></button><button id="client1HeaderButton3" number="3" type="button" class="u-btn"><i class="fas fa-gifts"></i><div class="u-btn-text">商城</div></button>'+t+'<div id="buttonSelectedBackground" class="selected-bg" style="left: 0px; top: 0px; opacity: 1;"></div></div></div>',e.appendChild(d),$("#content .TagsPage-content").prepend(e);var c=fe?"touchend":"click",p=0,u={},m=fe?3:0;u[0]=0;for(var h=0;h<s;h++){var f=$("#client1HeaderButton"+h).outerWidth();1!==h&&2!==h&&(0===h&&$("#buttonSelectedBackground").width(f),u[h+1]=f+p-m,p+=f)}$(".u-btn").on(c,function(){var e=parseInt($(this).attr("number")),t=document.getElementById("zhiboIframe");if($(".App").css("min-height","100vh"),0===e)$(".swiperTagContainer").css("display",""),$(".zhiboContainer").css("display","none"),$(".youxiContainer").css("display","none"),$(".buttonCustomizationContainer").css("display","none"),$(".shangchengContainer").css("display","none"),$(".App").css("min-height","50vh"),t.src="";else if(1===e){$(".swiperTagContainer").css("display","none"),$(".zhiboContainer").css("display","inline-block"),$(".youxiContainer").css("display","none"),$(".buttonCustomizationContainer").css("display","none"),$(".shangchengContainer").css("display","none");var s=window.innerHeight-$("#app-navigation").outerHeight()-$(".selectTitleContainer").outerHeight()-$("#linksQueuePrev").outerHeight();if($("#zhiboIframe").css("height",s+"px"),pe[ue]){var n=pe[ue].attribute("links");t.src!==n&&(t.src=n)}}else if(2===e)$(".swiperTagContainer").css("display","none"),$(".zhiboContainer").css("display","none"),$(".youxiContainer").css("display","flex"),$(".buttonCustomizationContainer").css("display","none"),$(".shangchengContainer").css("display","none"),t.src="";else if(3===e)$(".swiperTagContainer").css("display","none"),$(".zhiboContainer").css("display","none"),$(".youxiContainer").css("display","none"),$(".buttonCustomizationContainer").css("display","none"),$(".shangchengContainer").css("display","flex"),t.src="";else{var r=i[e];if(r){var a=window.innerHeight-$("#app-navigation").outerHeight()-$(".selectTitleContainer").outerHeight()-$("#linksQueuePrev").outerHeight(),o=0,l="yes",d=$(".swiperTagContainer").css("height");5==e?(a=550,d=550):6==e?(a=d=380,l="no"):(7==e||8==e)&&(o=20),$(".buttonCustomizationContainer").css("padding-bottom",o+"px"),$("#customButtonIframe").css("padding-bottom",o+"px"),$("#customButtonIframe").attr("scrolling",l),$(".buttonCustomizationContainer").css("height",d+"px"),$("#customButtonIframe").css("height",a+"px");var c=document.getElementById("customButtonIframe");$(".swiperTagContainer").css("display","none"),$(".zhiboContainer").css("display","none"),$(".youxiContainer").css("display","none"),$(".buttonCustomizationContainer").css("display","inline-block"),$(".shangchengContainer").css("display","none"),c.src=r.url}}$("#buttonSelectedBackground").width($(this).outerWidth()),void 0!==u[e]&&$("#buttonSelectedBackground").css("left",u[e])})}}(),$(".item-MoneyLeaderboard").addClass("App-primaryControl"),$(".item-forum-checkin").parent().append($(".item-MoneyLeaderboard")),$(".item-MoneyLeaderboard").css("right","75px")),s().session.user||null===(e=document.getElementById("wusong8899Client1HeaderIcon"))&&((e=document.createElement("div")).id="wusong8899Client1HeaderIcon",e.style.display="inline-block",e.style.marginTop="8px",e.innerHTML='<img src="https://lg666.cc/assets/files/2023-01-18/1674049401-881154-test-16.png" style="height: 24px;" />',$("#app-navigation").find(".App-backControl").prepend(e)))},100)}},10)}(e))})})})(),module.exports={}})();
//# sourceMappingURL=forum.js.map