# Architecture Documentation

## Overview

This Flarum extension has been completely refactored from a monolithic structure to a modular, maintainable architecture. The extension provides header advertisement slideshow functionality with improved error handling, configuration management, and code organization.

## Project Structure

```
├── src/                          # PHP backend code
│   └── SettingsHelper.php        # Modular settings configuration
├── js/src/                       # JavaScript/TypeScript frontend code
│   ├── admin/                    # Admin interface modules
│   │   ├── index.js             # Main admin initializer
│   │   └── SettingsGenerator.js # Settings form generator
│   └── forum/                   # Forum frontend modules
│       ├── components/          # UI components
│       │   ├── SlideshowManager.ts  # Slideshow functionality
│       │   └── UIManager.ts         # UI component management
│       ├── services/            # Business logic services
│       │   └── DataLoader.ts        # Data loading service
│       ├── utils/               # Utility modules
│       │   ├── ConfigManager.ts     # Configuration management
│       │   ├── DOMUtils.ts          # DOM manipulation utilities
│       │   ├── ErrorHandler.ts      # Error handling and validation
│       │   └── MobileDetection.ts   # Mobile device detection
│       └── index.ts             # Main forum initializer
├── locale/                      # Translation files
│   ├── en.yml                   # English translations
│   └── zh-Hans.yml              # Chinese translations
└── less/                        # Stylesheets
    ├── admin.less
    └── forum.less
```

## Key Improvements

### 1. **Modular Architecture**
- **Before**: Single 594-line monolithic file
- **After**: Organized into focused modules with single responsibilities

### 2. **Error Handling**
- Comprehensive error logging and handling
- Graceful fallbacks for missing configurations
- Input validation and sanitization

### 3. **Configuration Management**
- Centralized configuration through `ConfigManager`
- Type-safe configuration access
- Validation and default values

### 4. **Code Reusability**
- Utility modules for common operations
- Singleton pattern for shared services
- Consistent API patterns

## Module Descriptions

### PHP Backend

#### `SettingsHelper.php`
Replaces 60+ lines of repetitive settings registration with a clean, loop-based approach:

```php
// Before: 60+ repetitive lines
(new Extend\Settings)->serializeToForum('Client1HeaderAdvLink1', '...')
(new Extend\Settings)->serializeToForum('Client1HeaderAdvImage1', '...')
// ... 58 more lines

// After: Clean, maintainable approach
return SettingsHelper::getExtensionConfig();
```

### Frontend Components

#### `SlideshowManager.ts`
Handles all slideshow-related functionality:
- Slideshow creation and initialization
- Swiper.js integration
- Mobile-responsive configuration
- DOM manipulation for slideshow elements

#### `UIManager.ts`
Manages UI components and layout:
- Category layout transformation
- Tag swiper creation
- Tronscan component integration
- Social media buttons

#### `DataLoader.ts`
Centralized data loading service:
- Async data fetching with error handling
- Singleton pattern for shared state
- Promise-based API
- Automatic retry mechanisms

### Utility Modules

#### `ConfigManager.ts`
Configuration management:
- Centralized configuration access
- Type-safe getters and setters
- Validation and defaults
- Translation key management

#### `ErrorHandler.ts`
Comprehensive error handling:
- Global error catching
- Contextual error logging
- Validation utilities
- Dependency checking

#### `MobileDetection.ts`
Mobile device detection:
- Accurate mobile detection
- Responsive configuration
- Event type determination

#### `DOMUtils.ts`
Safe DOM manipulation:
- Error-safe element creation
- Event listener management
- Style manipulation
- Element validation

## Configuration

### Settings Structure
The extension supports up to 30 advertisement slides, each with:
- Image URL (`Client1HeaderAdvImage{N}`)
- Link URL (`Client1HeaderAdvLink{N}`)
- Transition time (`Client1HeaderAdvTransitionTime`)

### Default Values
- Maximum slides: 30
- Default transition time: 5000ms
- Check interval: 10ms
- Data check interval: 100ms

## Error Handling Strategy

### 1. **Graceful Degradation**
- Missing configurations don't break the extension
- Fallback values for all critical settings
- Silent failures with logging

### 2. **Validation**
- URL validation for image and link inputs
- Slide number validation (1-30)
- Configuration completeness checks

### 3. **Logging**
- Contextual error messages
- Error log export functionality
- Development-friendly error reporting

## Performance Optimizations

### 1. **Lazy Loading**
- Components initialize only when needed
- Data loading on demand
- Singleton pattern prevents duplicate instances

### 2. **Memory Management**
- Error log size limits (50 entries max)
- Proper event listener cleanup
- DOM element lifecycle management

### 3. **Mobile Optimization**
- Device-specific configurations
- Touch event handling
- Responsive layouts

## Migration Guide

### From Old Architecture
1. **Settings**: Automatically migrated through `SettingsHelper`
2. **Functionality**: All existing features preserved
3. **Configuration**: No changes required for end users

### Breaking Changes
- Internal API completely changed
- Custom modifications will need updating
- Development workflow improved

## Development Workflow

### Building
```bash
cd js
pnpm install
pnpm run build
```

### Development
```bash
pnpm run dev  # Watch mode
pnpm run lint # Type checking
pnpm run format # Code formatting
```

### Testing
```bash
pnpm run test # Run tests (when implemented)
```

## Future Enhancements

### Planned Features
1. **Unit Tests**: Comprehensive test suite
2. **Integration Tests**: End-to-end testing
3. **Performance Monitoring**: Runtime performance tracking
4. **Advanced Configuration**: More customization options

### Extension Points
- Plugin system for custom components
- Theme integration hooks
- API for external integrations

## Troubleshooting

### Common Issues
1. **Slides not showing**: Check image URLs and configuration
2. **Mobile layout issues**: Verify mobile detection
3. **Performance problems**: Check error logs

### Debug Mode
Enable debug logging by setting browser console to verbose mode. All errors are logged with context.

### Support
- Check error logs in browser console
- Validate configuration through `ConfigManager`
- Use error export functionality for bug reports
