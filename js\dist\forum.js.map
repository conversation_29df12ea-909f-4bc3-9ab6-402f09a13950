{"version": 3, "file": "forum.js", "mappings": "MACA,IAAIA,EAAsB,CCA1BA,EAAyBC,IACxB,IAAIC,EAASD,GAAUA,EAAOE,WAC7B,IAAOF,EAAiB,QACxB,IAAM,EAEP,OADAD,EAAoBI,EAAEF,EAAQ,CAAEG,EAAGH,IAC5BA,GCLRF,EAAwB,CAACM,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXP,EAAoBS,EAAEF,EAAYC,KAASR,EAAoBS,EAAEH,EAASE,IAC5EE,OAAOC,eAAeL,EAASE,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,MCJ3ER,EAAwB,CAACc,EAAKC,IAAUL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,I,mBCAlF,MAAM,EAA+BI,OAAOC,KAAKC,OAAO,iBCAlD,EAA+BF,OAAOC,KAAKC,OAAO,a,aCAxD,MAAM,EAA+BF,OAAOC,KAAKC,OAAO,kC,aCYxD,SAASC,EAASR,GAChB,OAAe,OAARA,GAA+B,iBAARA,GAAoB,gBAAiBA,GAAOA,EAAIS,cAAgBb,MAChG,CACA,SAASc,EAAOC,EAAQC,QACP,IAAXD,IACFA,EAAS,CAAC,QAEA,IAARC,IACFA,EAAM,CAAC,GAET,MAAMC,EAAW,CAAC,YAAa,cAAe,aAC9CjB,OAAOkB,KAAKF,GAAKG,OAAOrB,GAAOmB,EAASG,QAAQtB,GAAO,GAAGuB,QAAQvB,SACrC,IAAhBiB,EAAOjB,GAAsBiB,EAAOjB,GAAOkB,EAAIlB,GAAcc,EAASI,EAAIlB,KAASc,EAASG,EAAOjB,KAASE,OAAOkB,KAAKF,EAAIlB,IAAMwB,OAAS,GACpJR,EAAOC,EAAOjB,GAAMkB,EAAIlB,KAG9B,CACA,MAAMyB,EAAc,CAClBC,KAAM,CAAC,EACP,gBAAAC,GAAoB,EACpB,mBAAAC,GAAuB,EACvBC,cAAe,CACb,IAAAC,GAAQ,EACRC,SAAU,IAEZC,cAAa,IACJ,KAETC,iBAAgB,IACP,GAETC,eAAc,IACL,KAETC,YAAW,KACF,CACL,SAAAC,GAAa,IAGjBC,cAAa,KACJ,CACLC,SAAU,GACVC,WAAY,GACZC,MAAO,CAAC,EACR,YAAAC,GAAgB,EAChBC,qBAAoB,IACX,KAIbC,gBAAe,KACN,CAAC,GAEVC,WAAU,IACD,KAETC,SAAU,CACRC,KAAM,GACNC,KAAM,GACNC,SAAU,GACVC,KAAM,GACNC,OAAQ,GACRC,SAAU,GACVC,SAAU,GACVC,OAAQ,KAGZ,SAAS,IACP,MAAMC,EAA0B,oBAAbC,SAA2BA,SAAW,CAAC,EAE1D,OADAvC,EAAOsC,EAAK7B,GACL6B,CACT,CACA,MAAME,EAAY,CAChBD,SAAU9B,EACVgC,UAAW,CACTC,UAAW,IAEbb,SAAU,CACRC,KAAM,GACNC,KAAM,GACNC,SAAU,GACVC,KAAM,GACNC,OAAQ,GACRC,SAAU,GACVC,SAAU,GACVC,OAAQ,IAEVM,QAAS,CACP,YAAAC,GAAgB,EAChB,SAAAC,GAAa,EACb,EAAAC,GAAM,EACN,IAAAC,GAAQ,GAEVC,YAAa,WACX,OAAOC,IACT,EACA,gBAAAtC,GAAoB,EACpB,mBAAAC,GAAuB,EACvBsC,iBAAgB,KACP,CACLC,iBAAgB,IACP,KAIb,KAAAC,GAAS,EACT,IAAAC,GAAQ,EACRC,OAAQ,CAAC,EACT,UAAAC,GAAc,EACd,YAAAC,GAAgB,EAChBC,WAAU,KACD,CAAC,GAEVC,sBAAsBC,GACM,oBAAfJ,YACTI,IACO,MAEFJ,WAAWI,EAAU,GAE9B,oBAAAC,CAAqBC,GACO,oBAAfN,YAGXC,aAAaK,EACf,GAEF,SAAS,IACP,MAAMC,EAAwB,oBAAXC,OAAyBA,OAAS,CAAC,EAEtD,OADA/D,EAAO8D,EAAKtB,GACLsB,CACT,CCvHA,SAASE,EAASL,EAAUM,GAI1B,YAHc,IAAVA,IACFA,EAAQ,GAEHV,WAAWI,EAAUM,EAC9B,CACA,SAASC,IACP,OAAOb,KAAKa,KACd,CAsDA,SAAS,EAASjF,GAChB,MAAoB,iBAANA,GAAwB,OAANA,GAAcA,EAAEc,aAAkE,WAAnDb,OAAOM,UAAU2E,SAASzE,KAAKT,GAAGmF,MAAM,GAAI,EAC7G,CACA,SAASC,EAAOC,GAEd,MAAsB,oBAAXP,aAAwD,IAAvBA,OAAOQ,YAC1CD,aAAgBC,YAElBD,IAA2B,IAAlBA,EAAKE,UAAoC,KAAlBF,EAAKE,SAC9C,CACA,SAAS,IACP,MAAMC,EAAKvF,OAAOwF,UAAUlE,QAAU,OAAImE,EAAYD,UAAU,IAC1DvE,EAAW,CAAC,YAAa,cAAe,aAC9C,IAAK,IAAIyE,EAAI,EAAGA,EAAIF,UAAUlE,OAAQoE,GAAK,EAAG,CAC5C,MAAMC,EAAaD,EAAI,GAAKF,UAAUlE,QAAUoE,OAAID,EAAYD,UAAUE,GAC1E,GAAIC,UAAoDR,EAAOQ,GAAa,CAC1E,MAAMC,EAAY5F,OAAOkB,KAAKlB,OAAO2F,IAAaxE,OAAOrB,GAAOmB,EAASG,QAAQtB,GAAO,GACxF,IAAK,IAAI+F,EAAY,EAAGC,EAAMF,EAAUtE,OAAQuE,EAAYC,EAAKD,GAAa,EAAG,CAC/E,MAAME,EAAUH,EAAUC,GACpBG,EAAOhG,OAAOiG,yBAAyBN,EAAYI,QAC5CN,IAATO,GAAsBA,EAAK9F,aACzB,EAASqF,EAAGQ,KAAa,EAASJ,EAAWI,IAC3CJ,EAAWI,GAASG,WACtBX,EAAGQ,GAAWJ,EAAWI,GAEzB,EAAOR,EAAGQ,GAAUJ,EAAWI,KAEvB,EAASR,EAAGQ,KAAa,EAASJ,EAAWI,KACvDR,EAAGQ,GAAW,CAAC,EACXJ,EAAWI,GAASG,WACtBX,EAAGQ,GAAWJ,EAAWI,GAEzB,EAAOR,EAAGQ,GAAUJ,EAAWI,KAGjCR,EAAGQ,GAAWJ,EAAWI,GAG/B,CACF,CACF,CACA,OAAOR,CACT,CACA,SAASY,EAAeC,EAAIC,EAASC,GACnCF,EAAG9D,MAAMiE,YAAYF,EAASC,EAChC,CACA,SAASE,EAAqBC,GAC5B,IAAI,OACFC,EAAM,eACNC,EAAc,KACdC,GACEH,EACJ,MAAM5B,EAAS,IACTgC,GAAiBH,EAAOI,UAC9B,IACIC,EADAC,EAAY,KAEhB,MAAMC,EAAWP,EAAOQ,OAAOC,MAC/BT,EAAOU,UAAU9E,MAAM+E,eAAiB,OACxCxC,EAAOH,qBAAqBgC,EAAOY,gBACnC,MAAMC,EAAMZ,EAAiBE,EAAgB,OAAS,OAChDW,EAAe,CAACC,EAAS1G,IACd,SAARwG,GAAkBE,GAAW1G,GAAkB,SAARwG,GAAkBE,GAAW1G,EAEvE2G,EAAU,KACdX,GAAO,IAAI5C,MAAOwD,UACA,OAAdX,IACFA,EAAYD,GAEd,MAAMa,EAAWC,KAAKC,IAAID,KAAKE,KAAKhB,EAAOC,GAAaC,EAAU,GAAI,GAChEe,EAAe,GAAMH,KAAKI,IAAIL,EAAWC,KAAKK,IAAM,EAC1D,IAAIC,EAAkBtB,EAAgBmB,GAAgBrB,EAAiBE,GAOvE,GANIW,EAAaW,EAAiBxB,KAChCwB,EAAkBxB,GAEpBD,EAAOU,UAAUgB,SAAS,CACxB,CAACxB,GAAOuB,IAENX,EAAaW,EAAiBxB,GAUhC,OATAD,EAAOU,UAAU9E,MAAM+F,SAAW,SAClC3B,EAAOU,UAAU9E,MAAM+E,eAAiB,GACxChD,WAAW,KACTqC,EAAOU,UAAU9E,MAAM+F,SAAW,GAClC3B,EAAOU,UAAUgB,SAAS,CACxB,CAACxB,GAAOuB,WAGZtD,EAAOH,qBAAqBgC,EAAOY,gBAGrCZ,EAAOY,eAAiBzC,EAAOL,sBAAsBkD,IAEvDA,GACF,CAIA,SAASY,EAAgBC,EAASC,QACf,IAAbA,IACFA,EAAW,IAEb,MAAM3D,EAAS,IACTzC,EAAW,IAAImG,EAAQnG,UAI7B,OAHIyC,EAAO4D,iBAAmBF,aAAmBE,iBAC/CrG,EAASsG,QAAQH,EAAQI,oBAEtBH,EAGEpG,EAASjB,OAAOiF,GAAMA,EAAGwC,QAAQJ,IAF/BpG,CAGX,CAwBA,SAASyG,EAAYC,GACnB,IAEE,YADAC,QAAQC,KAAKF,EAEf,CAAE,MAAOG,GAET,CACF,CACA,SAAS9G,EAAc+G,EAAKC,QACV,IAAZA,IACFA,EAAU,IAEZ,MAAM/C,EAAK/C,SAASlB,cAAc+G,GAElC,OADA9C,EAAGgD,UAAUC,OAAQC,MAAMC,QAAQJ,GAAWA,EAtOhD,SAAyBA,GAIvB,YAHgB,IAAZA,IACFA,EAAU,IAELA,EAAQK,OAAOC,MAAM,KAAKtI,OAAOuI,KAAOA,EAAEF,OACnD,CAiO0DG,CAAgBR,IACjE/C,CACT,CAqCA,SAASwD,EAAaxD,EAAI/F,GAExB,OADe,IACD2D,iBAAiBoC,EAAI,MAAMnC,iBAAiB5D,EAC5D,CACA,SAASwJ,EAAazD,GACpB,IACIV,EADAoE,EAAQ1D,EAEZ,GAAI0D,EAAO,CAGT,IAFApE,EAAI,EAEuC,QAAnCoE,EAAQA,EAAMC,kBACG,IAAnBD,EAAMxE,WAAgBI,GAAK,GAEjC,OAAOA,CACT,CAEF,CAwBA,SAASsE,EAAiB5D,EAAI6D,EAAMC,GAClC,MAAMrF,EAAS,IACf,OAAIqF,EACK9D,EAAY,UAAT6D,EAAmB,cAAgB,gBAAkBE,WAAWtF,EAAOb,iBAAiBoC,EAAI,MAAMnC,iBAA0B,UAATgG,EAAmB,eAAiB,eAAiBE,WAAWtF,EAAOb,iBAAiBoC,EAAI,MAAMnC,iBAA0B,UAATgG,EAAmB,cAAgB,kBAE9Q7D,EAAGgE,WACZ,CC1TA,IAAIC,EAgBAC,EAqDAC,EA5DJ,SAASC,IAIP,OAHKH,IACHA,EAVJ,WACE,MAAMxF,EAAS,IACTxB,EAAW,IACjB,MAAO,CACLoH,aAAcpH,EAASqH,iBAAmBrH,EAASqH,gBAAgBpI,OAAS,mBAAoBe,EAASqH,gBAAgBpI,MACzHqI,SAAU,iBAAkB9F,GAAUA,EAAO+F,eAAiBvH,aAAoBwB,EAAO+F,eAE7F,CAGcC,IAELR,CACT,CA6CA,SAASS,EAAUC,GAOjB,YANkB,IAAdA,IACFA,EAAY,CAAC,GAEVT,IACHA,EA/CJ,SAAoBU,GAClB,IAAI,UACFxH,QACY,IAAVwH,EAAmB,CAAC,EAAIA,EAC5B,MAAMX,EAAUG,IACV3F,EAAS,IACToG,EAAWpG,EAAOtB,UAAU0H,SAC5BC,EAAK1H,GAAaqB,EAAOtB,UAAUC,UACnC2H,EAAS,CACbC,KAAK,EACLC,SAAS,GAELC,EAAczG,EAAOT,OAAOmH,MAC5BC,EAAe3G,EAAOT,OAAOqH,OAC7BJ,EAAUH,EAAGQ,MAAM,+BACzB,IAAIC,EAAOT,EAAGQ,MAAM,wBACpB,MAAME,EAAOV,EAAGQ,MAAM,2BAChBG,GAAUF,GAAQT,EAAGQ,MAAM,8BAC3BI,EAAuB,UAAbb,EAChB,IAAIc,EAAqB,aAAbd,EAqBZ,OAjBKU,GAAQI,GAAS1B,EAAQM,OADV,CAAC,YAAa,YAAa,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,YACxGvJ,QAAQ,GAAGkK,KAAeE,MAAmB,IAC9FG,EAAOT,EAAGQ,MAAM,uBACXC,IAAMA,EAAO,CAAC,EAAG,EAAG,WACzBI,GAAQ,GAINV,IAAYS,IACdX,EAAOa,GAAK,UACZb,EAAOE,SAAU,IAEfM,GAAQE,GAAUD,KACpBT,EAAOa,GAAK,MACZb,EAAOC,KAAM,GAIRD,CACT,CAMmBc,CAAWlB,IAErBT,CACT,CA4BA,SAAS4B,IAIP,OAHK3B,IACHA,EA3BJ,WACE,MAAM1F,EAAS,IACTsG,EAASL,IACf,IAAIqB,GAAqB,EACzB,SAASC,IACP,MAAMlB,EAAKrG,EAAOtB,UAAUC,UAAU6I,cACtC,OAAOnB,EAAG9J,QAAQ,WAAa,GAAK8J,EAAG9J,QAAQ,UAAY,GAAK8J,EAAG9J,QAAQ,WAAa,CAC1F,CACA,GAAIgL,IAAY,CACd,MAAMlB,EAAKoB,OAAOzH,EAAOtB,UAAUC,WACnC,GAAI0H,EAAGqB,SAAS,YAAa,CAC3B,MAAOC,EAAOC,GAASvB,EAAGzB,MAAM,YAAY,GAAGA,MAAM,KAAK,GAAGA,MAAM,KAAKiD,IAAIC,GAAOC,OAAOD,IAC1FR,EAAqBK,EAAQ,IAAgB,KAAVA,GAAgBC,EAAQ,CAC7D,CACF,CACA,MAAMI,EAAY,+CAA+CC,KAAKjI,EAAOtB,UAAUC,WACjFuJ,EAAkBX,IAExB,MAAO,CACLA,SAAUD,GAAsBY,EAChCZ,qBACAa,UAJgBD,GAAmBF,GAAa1B,EAAOC,IAKvDyB,YAEJ,CAGcI,IAEL1C,CACT,CAiJA,IAAI2C,EAAgB,CAClB,EAAAC,CAAGC,EAAQC,EAASC,GAClB,MAAMC,EAAOxJ,KACb,IAAKwJ,EAAKC,iBAAmBD,EAAKE,UAAW,OAAOF,EACpD,GAAuB,mBAAZF,EAAwB,OAAOE,EAC1C,MAAMG,EAASJ,EAAW,UAAY,OAKtC,OAJAF,EAAO3D,MAAM,KAAKpI,QAAQsM,IACnBJ,EAAKC,gBAAgBG,KAAQJ,EAAKC,gBAAgBG,GAAS,IAChEJ,EAAKC,gBAAgBG,GAAOD,GAAQL,KAE/BE,CACT,EACA,IAAAK,CAAKR,EAAQC,EAASC,GACpB,MAAMC,EAAOxJ,KACb,IAAKwJ,EAAKC,iBAAmBD,EAAKE,UAAW,OAAOF,EACpD,GAAuB,mBAAZF,EAAwB,OAAOE,EAC1C,SAASM,IACPN,EAAKO,IAAIV,EAAQS,GACbA,EAAYE,uBACPF,EAAYE,eAErB,IAAK,IAAIC,EAAOxI,UAAUlE,OAAQ2M,EAAO,IAAI3E,MAAM0E,GAAOE,EAAO,EAAGA,EAAOF,EAAME,IAC/ED,EAAKC,GAAQ1I,UAAU0I,GAEzBb,EAAQc,MAAMZ,EAAMU,EACtB,CAEA,OADAJ,EAAYE,eAAiBV,EACtBE,EAAKJ,GAAGC,EAAQS,EAAaP,EACtC,EACA,KAAAc,CAAMf,EAASC,GACb,MAAMC,EAAOxJ,KACb,IAAKwJ,EAAKC,iBAAmBD,EAAKE,UAAW,OAAOF,EACpD,GAAuB,mBAAZF,EAAwB,OAAOE,EAC1C,MAAMG,EAASJ,EAAW,UAAY,OAItC,OAHIC,EAAKc,mBAAmBjN,QAAQiM,GAAW,GAC7CE,EAAKc,mBAAmBX,GAAQL,GAE3BE,CACT,EACA,MAAAe,CAAOjB,GACL,MAAME,EAAOxJ,KACb,IAAKwJ,EAAKC,iBAAmBD,EAAKE,UAAW,OAAOF,EACpD,IAAKA,EAAKc,mBAAoB,OAAOd,EACrC,MAAMgB,EAAQhB,EAAKc,mBAAmBjN,QAAQiM,GAI9C,OAHIkB,GAAS,GACXhB,EAAKc,mBAAmBG,OAAOD,EAAO,GAEjChB,CACT,EACA,GAAAO,CAAIV,EAAQC,GACV,MAAME,EAAOxJ,KACb,OAAKwJ,EAAKC,iBAAmBD,EAAKE,UAAkBF,EAC/CA,EAAKC,iBACVJ,EAAO3D,MAAM,KAAKpI,QAAQsM,SACD,IAAZN,EACTE,EAAKC,gBAAgBG,GAAS,GACrBJ,EAAKC,gBAAgBG,IAC9BJ,EAAKC,gBAAgBG,GAAOtM,QAAQ,CAACoN,EAAcF,MAC7CE,IAAiBpB,GAAWoB,EAAaV,gBAAkBU,EAAaV,iBAAmBV,IAC7FE,EAAKC,gBAAgBG,GAAOa,OAAOD,EAAO,OAK3ChB,GAZ2BA,CAapC,EACA,IAAAmB,GACE,MAAMnB,EAAOxJ,KACb,IAAKwJ,EAAKC,iBAAmBD,EAAKE,UAAW,OAAOF,EACpD,IAAKA,EAAKC,gBAAiB,OAAOD,EAClC,IAAIH,EACAuB,EACAC,EACJ,IAAK,IAAIC,EAAQrJ,UAAUlE,OAAQ2M,EAAO,IAAI3E,MAAMuF,GAAQC,EAAQ,EAAGA,EAAQD,EAAOC,IACpFb,EAAKa,GAAStJ,UAAUsJ,GAyB1B,MAvBuB,iBAAZb,EAAK,IAAmB3E,MAAMC,QAAQ0E,EAAK,KACpDb,EAASa,EAAK,GACdU,EAAOV,EAAK/I,MAAM,EAAG+I,EAAK3M,QAC1BsN,EAAUrB,IAEVH,EAASa,EAAK,GAAGb,OACjBuB,EAAOV,EAAK,GAAGU,KACfC,EAAUX,EAAK,GAAGW,SAAWrB,GAE/BoB,EAAKI,QAAQH,IACOtF,MAAMC,QAAQ6D,GAAUA,EAASA,EAAO3D,MAAM,MACtDpI,QAAQsM,IACdJ,EAAKc,oBAAsBd,EAAKc,mBAAmB/M,QACrDiM,EAAKc,mBAAmBhN,QAAQoN,IAC9BA,EAAaN,MAAMS,EAAS,CAACjB,KAAUgB,MAGvCpB,EAAKC,iBAAmBD,EAAKC,gBAAgBG,IAC/CJ,EAAKC,gBAAgBG,GAAOtM,QAAQoN,IAClCA,EAAaN,MAAMS,EAASD,OAI3BpB,CACT,GA6WF,MAAMyB,EAAuB,CAACC,EAASC,EAAWC,KAC5CD,IAAcD,EAAQ7F,UAAUgG,SAASD,GAC3CF,EAAQ7F,UAAUC,IAAI8F,IACZD,GAAaD,EAAQ7F,UAAUgG,SAASD,IAClDF,EAAQ7F,UAAUiG,OAAOF,IAgHvBG,EAAqB,CAACL,EAASC,EAAWC,KAC1CD,IAAcD,EAAQ7F,UAAUgG,SAASD,GAC3CF,EAAQ7F,UAAUC,IAAI8F,IACZD,GAAaD,EAAQ7F,UAAUgG,SAASD,IAClDF,EAAQ7F,UAAUiG,OAAOF,IA4DvBI,EAAuB,CAAC7I,EAAQ8I,KACpC,IAAK9I,GAAUA,EAAO+G,YAAc/G,EAAOQ,OAAQ,OACnD,MACM+H,EAAUO,EAAQC,QADI/I,EAAOgJ,UAAY,eAAiB,IAAIhJ,EAAOQ,OAAOyI,cAElF,GAAIV,EAAS,CACX,IAAIW,EAASX,EAAQnN,cAAc,IAAI4E,EAAOQ,OAAO2I,uBAChDD,GAAUlJ,EAAOgJ,YAChBT,EAAQa,WACVF,EAASX,EAAQa,WAAWhO,cAAc,IAAI4E,EAAOQ,OAAO2I,sBAG5DrL,sBAAsB,KAChByK,EAAQa,aACVF,EAASX,EAAQa,WAAWhO,cAAc,IAAI4E,EAAOQ,OAAO2I,sBACxDD,GAAQA,EAAOP,aAKvBO,GAAQA,EAAOP,QACrB,GAEIU,EAAS,CAACrJ,EAAQ6H,KACtB,IAAK7H,EAAOsJ,OAAOzB,GAAQ,OAC3B,MAAMiB,EAAU9I,EAAOsJ,OAAOzB,GAAOzM,cAAc,oBAC/C0N,GAASA,EAAQS,gBAAgB,YAEjCC,EAAUxJ,IACd,IAAKA,GAAUA,EAAO+G,YAAc/G,EAAOQ,OAAQ,OACnD,IAAIiJ,EAASzJ,EAAOQ,OAAOkJ,oBAC3B,MAAMtK,EAAMY,EAAOsJ,OAAO1O,OAC1B,IAAKwE,IAAQqK,GAAUA,EAAS,EAAG,OACnCA,EAAStI,KAAKE,IAAIoI,EAAQrK,GAC1B,MAAMuK,EAAgD,SAAhC3J,EAAOQ,OAAOmJ,cAA2B3J,EAAO4J,uBAAyBzI,KAAK0I,KAAK7J,EAAOQ,OAAOmJ,eACjHG,EAAc9J,EAAO8J,YAC3B,GAAI9J,EAAOQ,OAAOuJ,MAAQ/J,EAAOQ,OAAOuJ,KAAKC,KAAO,EAAG,CACrD,MAAMC,EAAeH,EACfI,EAAiB,CAACD,EAAeR,GASvC,OARAS,EAAelI,QAAQY,MAAMuH,KAAK,CAChCvP,OAAQ6O,IACPzD,IAAI,CAACoE,EAAGpL,IACFiL,EAAeN,EAAgB3K,SAExCgB,EAAOsJ,OAAO3O,QAAQ,CAAC4N,EAASvJ,KAC1BkL,EAAerE,SAAS0C,EAAQ8B,SAAShB,EAAOrJ,EAAQhB,IAGhE,CACA,MAAMsL,EAAuBR,EAAcH,EAAgB,EAC3D,GAAI3J,EAAOQ,OAAO+J,QAAUvK,EAAOQ,OAAOgK,KACxC,IAAK,IAAIxL,EAAI8K,EAAcL,EAAQzK,GAAKsL,EAAuBb,EAAQzK,GAAK,EAAG,CAC7E,MAAMyL,GAAazL,EAAII,EAAMA,GAAOA,GAChCqL,EAAYX,GAAeW,EAAYH,IAAsBjB,EAAOrJ,EAAQyK,EAClF,MAEA,IAAK,IAAIzL,EAAImC,KAAKC,IAAI0I,EAAcL,EAAQ,GAAIzK,GAAKmC,KAAKE,IAAIiJ,EAAuBb,EAAQrK,EAAM,GAAIJ,GAAK,EACtGA,IAAM8K,IAAgB9K,EAAIsL,GAAwBtL,EAAI8K,IACxDT,EAAOrJ,EAAQhB,IA4JvB,IAAI0L,EAAS,CACXC,WApvBF,WACE,MAAM3K,EAAS3C,KACf,IAAIwH,EACAE,EACJ,MAAMrF,EAAKM,EAAON,GAEhBmF,OADiC,IAAxB7E,EAAOQ,OAAOqE,OAAiD,OAAxB7E,EAAOQ,OAAOqE,MACtD7E,EAAOQ,OAAOqE,MAEdnF,EAAGkL,YAGX7F,OADkC,IAAzB/E,EAAOQ,OAAOuE,QAAmD,OAAzB/E,EAAOQ,OAAOuE,OACtD/E,EAAOQ,OAAOuE,OAEdrF,EAAGmL,aAEA,IAAVhG,GAAe7E,EAAO8K,gBAA6B,IAAX/F,GAAgB/E,EAAO+K,eAKnElG,EAAQA,EAAQmG,SAAS9H,EAAaxD,EAAI,iBAAmB,EAAG,IAAMsL,SAAS9H,EAAaxD,EAAI,kBAAoB,EAAG,IACvHqF,EAASA,EAASiG,SAAS9H,EAAaxD,EAAI,gBAAkB,EAAG,IAAMsL,SAAS9H,EAAaxD,EAAI,mBAAqB,EAAG,IACrHwG,OAAO+E,MAAMpG,KAAQA,EAAQ,GAC7BqB,OAAO+E,MAAMlG,KAASA,EAAS,GACnCzL,OAAO4R,OAAOlL,EAAQ,CACpB6E,QACAE,SACAxB,KAAMvD,EAAO8K,eAAiBjG,EAAQE,IAE1C,EAwtBEoG,aAttBF,WACE,MAAMnL,EAAS3C,KACf,SAAS+N,EAA0B1M,EAAM2M,GACvC,OAAO5H,WAAW/E,EAAKnB,iBAAiByC,EAAOsL,kBAAkBD,KAAW,EAC9E,CACA,MAAM7K,EAASR,EAAOQ,QAChB,UACJE,EAAS,SACT6K,EACAhI,KAAMiI,EACNC,aAAcC,EAAG,SACjBC,GACE3L,EACE4L,EAAY5L,EAAO6L,SAAWrL,EAAOqL,QAAQC,QAC7CC,EAAuBH,EAAY5L,EAAO6L,QAAQvC,OAAO1O,OAASoF,EAAOsJ,OAAO1O,OAChF0O,EAAS1H,EAAgB2J,EAAU,IAAIvL,EAAOQ,OAAOyI,4BACrD+C,EAAeJ,EAAY5L,EAAO6L,QAAQvC,OAAO1O,OAAS0O,EAAO1O,OACvE,IAAIqR,EAAW,GACf,MAAMC,EAAa,GACbC,EAAkB,GACxB,IAAIC,EAAe5L,EAAO6L,mBACE,mBAAjBD,IACTA,EAAe5L,EAAO6L,mBAAmBvS,KAAKkG,IAEhD,IAAIsM,EAAc9L,EAAO+L,kBACE,mBAAhBD,IACTA,EAAc9L,EAAO+L,kBAAkBzS,KAAKkG,IAE9C,MAAMwM,EAAyBxM,EAAOiM,SAASrR,OACzC6R,EAA2BzM,EAAOkM,WAAWtR,OACnD,IAAI8R,EAAelM,EAAOkM,aACtBC,GAAiBP,EACjBQ,EAAgB,EAChB/E,EAAQ,EACZ,QAA0B,IAAf2D,EACT,OAE0B,iBAAjBkB,GAA6BA,EAAahS,QAAQ,MAAQ,EACnEgS,EAAejJ,WAAWiJ,EAAaG,QAAQ,IAAK,KAAO,IAAMrB,EAChC,iBAAjBkB,IAChBA,EAAejJ,WAAWiJ,IAE5B1M,EAAO8M,aAAeJ,EAGtBpD,EAAO3O,QAAQ4N,IACTmD,EACFnD,EAAQ3M,MAAMmR,WAAa,GAE3BxE,EAAQ3M,MAAMoR,YAAc,GAE9BzE,EAAQ3M,MAAMqR,aAAe,GAC7B1E,EAAQ3M,MAAMsR,UAAY,KAIxB1M,EAAO2M,gBAAkB3M,EAAO4M,UAClC3N,EAAeiB,EAAW,kCAAmC,IAC7DjB,EAAeiB,EAAW,iCAAkC,KAE9D,MAAM2M,EAAc7M,EAAOuJ,MAAQvJ,EAAOuJ,KAAKC,KAAO,GAAKhK,EAAO+J,KAQlE,IAAIuD,EAPAD,EACFrN,EAAO+J,KAAKwD,WAAWjE,GACdtJ,EAAO+J,MAChB/J,EAAO+J,KAAKyD,cAKd,MAAMC,EAAgD,SAAzBjN,EAAOmJ,eAA4BnJ,EAAOkN,aAAepU,OAAOkB,KAAKgG,EAAOkN,aAAajT,OAAOrB,QACnE,IAA1CoH,EAAOkN,YAAYtU,GAAKuQ,eACrC/O,OAAS,EACZ,IAAK,IAAIoE,EAAI,EAAGA,EAAIgN,EAAchN,GAAK,EAAG,CAExC,IAAI2O,EAKJ,GANAL,EAAY,EAERhE,EAAOtK,KAAI2O,EAAQrE,EAAOtK,IAC1BqO,GACFrN,EAAO+J,KAAK6D,YAAY5O,EAAG2O,EAAOrE,IAEhCA,EAAOtK,IAAyC,SAAnCkE,EAAayK,EAAO,WAArC,CAEA,GAA6B,SAAzBnN,EAAOmJ,cAA0B,CAC/B8D,IACFnE,EAAOtK,GAAGpD,MAAMoE,EAAOsL,kBAAkB,UAAY,IAEvD,MAAMuC,EAAcvQ,iBAAiBqQ,GAC/BG,EAAmBH,EAAM/R,MAAMmS,UAC/BC,EAAyBL,EAAM/R,MAAMqS,gBAO3C,GANIH,IACFH,EAAM/R,MAAMmS,UAAY,QAEtBC,IACFL,EAAM/R,MAAMqS,gBAAkB,QAE5BzN,EAAO0N,aACTZ,EAAYtN,EAAO8K,eAAiBxH,EAAiBqK,EAAO,SAAS,GAAQrK,EAAiBqK,EAAO,UAAU,OAC1G,CAEL,MAAM9I,EAAQuG,EAA0ByC,EAAa,SAC/CM,EAAc/C,EAA0ByC,EAAa,gBACrDO,EAAehD,EAA0ByC,EAAa,iBACtDd,EAAa3B,EAA0ByC,EAAa,eACpDb,EAAc5B,EAA0ByC,EAAa,gBACrDQ,EAAYR,EAAYtQ,iBAAiB,cAC/C,GAAI8Q,GAA2B,eAAdA,EACff,EAAYzI,EAAQkI,EAAaC,MAC5B,CACL,MAAM,YACJpC,EAAW,YACXlH,GACEiK,EACJL,EAAYzI,EAAQsJ,EAAcC,EAAerB,EAAaC,GAAetJ,EAAckH,EAC7F,CACF,CACIkD,IACFH,EAAM/R,MAAMmS,UAAYD,GAEtBE,IACFL,EAAM/R,MAAMqS,gBAAkBD,GAE5BxN,EAAO0N,eAAcZ,EAAYnM,KAAKmN,MAAMhB,GAClD,MACEA,GAAa9B,GAAchL,EAAOmJ,cAAgB,GAAK+C,GAAgBlM,EAAOmJ,cAC1EnJ,EAAO0N,eAAcZ,EAAYnM,KAAKmN,MAAMhB,IAC5ChE,EAAOtK,KACTsK,EAAOtK,GAAGpD,MAAMoE,EAAOsL,kBAAkB,UAAY,GAAGgC,OAGxDhE,EAAOtK,KACTsK,EAAOtK,GAAGuP,gBAAkBjB,GAE9BnB,EAAgBnK,KAAKsL,GACjB9M,EAAO2M,gBACTR,EAAgBA,EAAgBW,EAAY,EAAIV,EAAgB,EAAIF,EAC9C,IAAlBE,GAA6B,IAAN5N,IAAS2N,EAAgBA,EAAgBnB,EAAa,EAAIkB,GAC3E,IAAN1N,IAAS2N,EAAgBA,EAAgBnB,EAAa,EAAIkB,GAC1DvL,KAAKqN,IAAI7B,GAAiB,OAAUA,EAAgB,GACpDnM,EAAO0N,eAAcvB,EAAgBxL,KAAKmN,MAAM3B,IAChD9E,EAAQrH,EAAOiO,iBAAmB,GAAGxC,EAASjK,KAAK2K,GACvDT,EAAWlK,KAAK2K,KAEZnM,EAAO0N,eAAcvB,EAAgBxL,KAAKmN,MAAM3B,KAC/C9E,EAAQ1G,KAAKE,IAAIrB,EAAOQ,OAAOkO,mBAAoB7G,IAAU7H,EAAOQ,OAAOiO,iBAAmB,GAAGxC,EAASjK,KAAK2K,GACpHT,EAAWlK,KAAK2K,GAChBA,EAAgBA,EAAgBW,EAAYZ,GAE9C1M,EAAO8M,aAAeQ,EAAYZ,EAClCE,EAAgBU,EAChBzF,GAAS,CArE2D,CAsEtE,CAaA,GAZA7H,EAAO8M,YAAc3L,KAAKC,IAAIpB,EAAO8M,YAAatB,GAAcc,EAC5DZ,GAAOC,IAA+B,UAAlBnL,EAAOmO,QAAwC,cAAlBnO,EAAOmO,UAC1DjO,EAAU9E,MAAMiJ,MAAQ,GAAG7E,EAAO8M,YAAcJ,OAE9ClM,EAAOoO,iBACTlO,EAAU9E,MAAMoE,EAAOsL,kBAAkB,UAAY,GAAGtL,EAAO8M,YAAcJ,OAE3EW,GACFrN,EAAO+J,KAAK8E,kBAAkBvB,EAAWrB,IAItCzL,EAAO2M,eAAgB,CAC1B,MAAM2B,EAAgB,GACtB,IAAK,IAAI9P,EAAI,EAAGA,EAAIiN,EAASrR,OAAQoE,GAAK,EAAG,CAC3C,IAAI+P,EAAiB9C,EAASjN,GAC1BwB,EAAO0N,eAAca,EAAiB5N,KAAKmN,MAAMS,IACjD9C,EAASjN,IAAMgB,EAAO8M,YAActB,GACtCsD,EAAc9M,KAAK+M,EAEvB,CACA9C,EAAW6C,EACP3N,KAAKmN,MAAMtO,EAAO8M,YAActB,GAAcrK,KAAKmN,MAAMrC,EAASA,EAASrR,OAAS,IAAM,GAC5FqR,EAASjK,KAAKhC,EAAO8M,YAActB,EAEvC,CACA,GAAII,GAAapL,EAAOgK,KAAM,CAC5B,MAAMjH,EAAO4I,EAAgB,GAAKO,EAClC,GAAIlM,EAAOiO,eAAiB,EAAG,CAC7B,MAAMO,EAAS7N,KAAK0I,MAAM7J,EAAO6L,QAAQoD,aAAejP,EAAO6L,QAAQqD,aAAe1O,EAAOiO,gBACvFU,EAAY5L,EAAO/C,EAAOiO,eAChC,IAAK,IAAIzP,EAAI,EAAGA,EAAIgQ,EAAQhQ,GAAK,EAC/BiN,EAASjK,KAAKiK,EAASA,EAASrR,OAAS,GAAKuU,EAElD,CACA,IAAK,IAAInQ,EAAI,EAAGA,EAAIgB,EAAO6L,QAAQoD,aAAejP,EAAO6L,QAAQqD,YAAalQ,GAAK,EACnD,IAA1BwB,EAAOiO,gBACTxC,EAASjK,KAAKiK,EAASA,EAASrR,OAAS,GAAK2I,GAEhD2I,EAAWlK,KAAKkK,EAAWA,EAAWtR,OAAS,GAAK2I,GACpDvD,EAAO8M,aAAevJ,CAE1B,CAEA,GADwB,IAApB0I,EAASrR,SAAcqR,EAAW,CAAC,IAClB,IAAjBS,EAAoB,CACtB,MAAMtT,EAAM4G,EAAO8K,gBAAkBY,EAAM,aAAe1L,EAAOsL,kBAAkB,eACnFhC,EAAO7O,OAAO,CAAC2P,EAAGgF,MACX5O,EAAO4M,UAAW5M,EAAOgK,OAC1B4E,IAAe9F,EAAO1O,OAAS,GAIlCD,QAAQ4N,IACTA,EAAQ3M,MAAMxC,GAAO,GAAGsT,OAE5B,CACA,GAAIlM,EAAO2M,gBAAkB3M,EAAO6O,qBAAsB,CACxD,IAAIC,EAAgB,EACpBnD,EAAgBxR,QAAQ4U,IACtBD,GAAiBC,GAAkB7C,GAAgB,KAErD4C,GAAiB5C,EACjB,MAAM8C,EAAUF,EAAgB9D,EAAa8D,EAAgB9D,EAAa,EAC1ES,EAAWA,EAASjG,IAAIyJ,GAClBA,GAAQ,GAAWrD,EACnBqD,EAAOD,EAAgBA,EAAUlD,EAC9BmD,EAEX,CACA,GAAIjP,EAAOkP,yBAA0B,CACnC,IAAIJ,EAAgB,EACpBnD,EAAgBxR,QAAQ4U,IACtBD,GAAiBC,GAAkB7C,GAAgB,KAErD4C,GAAiB5C,EACjB,MAAMiD,GAAcnP,EAAO6L,oBAAsB,IAAM7L,EAAO+L,mBAAqB,GACnF,GAAI+C,EAAgBK,EAAanE,EAAY,CAC3C,MAAMoE,GAAmBpE,EAAa8D,EAAgBK,GAAc,EACpE1D,EAAStR,QAAQ,CAAC8U,EAAMI,KACtB5D,EAAS4D,GAAaJ,EAAOG,IAE/B1D,EAAWvR,QAAQ,CAAC8U,EAAMI,KACxB3D,EAAW2D,GAAaJ,EAAOG,GAEnC,CACF,CAOA,GANAtW,OAAO4R,OAAOlL,EAAQ,CACpBsJ,SACA2C,WACAC,aACAC,oBAEE3L,EAAO2M,gBAAkB3M,EAAO4M,UAAY5M,EAAO6O,qBAAsB,CAC3E5P,EAAeiB,EAAW,mCAAuCuL,EAAS,GAAb,MAC7DxM,EAAeiB,EAAW,iCAAqCV,EAAOuD,KAAO,EAAI4I,EAAgBA,EAAgBvR,OAAS,GAAK,EAAnE,MAC5D,MAAMkV,GAAiB9P,EAAOiM,SAAS,GACjC8D,GAAmB/P,EAAOkM,WAAW,GAC3ClM,EAAOiM,SAAWjM,EAAOiM,SAASjG,IAAIgK,GAAKA,EAAIF,GAC/C9P,EAAOkM,WAAalM,EAAOkM,WAAWlG,IAAIgK,GAAKA,EAAID,EACrD,CAeA,GAdI/D,IAAiBD,GACnB/L,EAAOgI,KAAK,sBAEViE,EAASrR,SAAW4R,IAClBxM,EAAOQ,OAAOyP,eAAejQ,EAAOkQ,gBACxClQ,EAAOgI,KAAK,yBAEVkE,EAAWtR,SAAW6R,GACxBzM,EAAOgI,KAAK,0BAEVxH,EAAO2P,qBACTnQ,EAAOoQ,qBAETpQ,EAAOgI,KAAK,mBACP4D,GAAcpL,EAAO4M,SAA8B,UAAlB5M,EAAOmO,QAAwC,SAAlBnO,EAAOmO,QAAoB,CAC5F,MAAM0B,EAAsB,GAAG7P,EAAO8P,wCAChCC,EAA6BvQ,EAAON,GAAGgD,UAAUgG,SAAS2H,GAC5DrE,GAAgBxL,EAAOgQ,wBACpBD,GAA4BvQ,EAAON,GAAGgD,UAAUC,IAAI0N,GAChDE,GACTvQ,EAAON,GAAGgD,UAAUiG,OAAO0H,EAE/B,CACF,EAscEI,iBApcF,SAA0BhQ,GACxB,MAAMT,EAAS3C,KACTqT,EAAe,GACf9E,EAAY5L,EAAO6L,SAAW7L,EAAOQ,OAAOqL,QAAQC,QAC1D,IACI9M,EADA2R,EAAY,EAEK,iBAAVlQ,EACTT,EAAO4Q,cAAcnQ,IACF,IAAVA,GACTT,EAAO4Q,cAAc5Q,EAAOQ,OAAOC,OAErC,MAAMoQ,EAAkBhJ,GAClB+D,EACK5L,EAAOsJ,OAAOtJ,EAAO8Q,oBAAoBjJ,IAE3C7H,EAAOsJ,OAAOzB,GAGvB,GAAoC,SAAhC7H,EAAOQ,OAAOmJ,eAA4B3J,EAAOQ,OAAOmJ,cAAgB,EAC1E,GAAI3J,EAAOQ,OAAO2M,gBACfnN,EAAO+Q,eAAiB,IAAIpW,QAAQgT,IACnC+C,EAAa1O,KAAK2L,UAGpB,IAAK3O,EAAI,EAAGA,EAAImC,KAAK0I,KAAK7J,EAAOQ,OAAOmJ,eAAgB3K,GAAK,EAAG,CAC9D,MAAM6I,EAAQ7H,EAAO8J,YAAc9K,EACnC,GAAI6I,EAAQ7H,EAAOsJ,OAAO1O,SAAWgR,EAAW,MAChD8E,EAAa1O,KAAK6O,EAAgBhJ,GACpC,MAGF6I,EAAa1O,KAAK6O,EAAgB7Q,EAAO8J,cAI3C,IAAK9K,EAAI,EAAGA,EAAI0R,EAAa9V,OAAQoE,GAAK,EACxC,QAA+B,IAApB0R,EAAa1R,GAAoB,CAC1C,MAAM+F,EAAS2L,EAAa1R,GAAGgS,aAC/BL,EAAY5L,EAAS4L,EAAY5L,EAAS4L,CAC5C,EAIEA,GAA2B,IAAdA,KAAiB3Q,EAAOU,UAAU9E,MAAMmJ,OAAS,GAAG4L,MACvE,EAyZEP,mBAvZF,WACE,MAAMpQ,EAAS3C,KACTiM,EAAStJ,EAAOsJ,OAEhB2H,EAAcjR,EAAOgJ,UAAYhJ,EAAO8K,eAAiB9K,EAAOU,UAAUwQ,WAAalR,EAAOU,UAAUyQ,UAAY,EAC1H,IAAK,IAAInS,EAAI,EAAGA,EAAIsK,EAAO1O,OAAQoE,GAAK,EACtCsK,EAAOtK,GAAGoS,mBAAqBpR,EAAO8K,eAAiBxB,EAAOtK,GAAGkS,WAAa5H,EAAOtK,GAAGmS,WAAaF,EAAcjR,EAAOqR,uBAE9H,EAgZEC,qBAvYF,SAA8BlR,QACV,IAAdA,IACFA,EAAY/C,MAAQA,KAAK+C,WAAa,GAExC,MAAMJ,EAAS3C,KACTmD,EAASR,EAAOQ,QAChB,OACJ8I,EACAmC,aAAcC,EAAG,SACjBO,GACEjM,EACJ,GAAsB,IAAlBsJ,EAAO1O,OAAc,YACkB,IAAhC0O,EAAO,GAAG8H,mBAAmCpR,EAAOoQ,qBAC/D,IAAImB,GAAgBnR,EAChBsL,IAAK6F,EAAenR,GACxBJ,EAAOwR,qBAAuB,GAC9BxR,EAAO+Q,cAAgB,GACvB,IAAIrE,EAAelM,EAAOkM,aACE,iBAAjBA,GAA6BA,EAAahS,QAAQ,MAAQ,EACnEgS,EAAejJ,WAAWiJ,EAAaG,QAAQ,IAAK,KAAO,IAAM7M,EAAOuD,KACvC,iBAAjBmJ,IAChBA,EAAejJ,WAAWiJ,IAE5B,IAAK,IAAI1N,EAAI,EAAGA,EAAIsK,EAAO1O,OAAQoE,GAAK,EAAG,CACzC,MAAM2O,EAAQrE,EAAOtK,GACrB,IAAIyS,EAAc9D,EAAMyD,kBACpB5Q,EAAO4M,SAAW5M,EAAO2M,iBAC3BsE,GAAenI,EAAO,GAAG8H,mBAE3B,MAAMM,GAAiBH,GAAgB/Q,EAAO2M,eAAiBnN,EAAO2R,eAAiB,GAAKF,IAAgB9D,EAAMY,gBAAkB7B,GAC9HkF,GAAyBL,EAAetF,EAAS,IAAMzL,EAAO2M,eAAiBnN,EAAO2R,eAAiB,GAAKF,IAAgB9D,EAAMY,gBAAkB7B,GACpJmF,IAAgBN,EAAeE,GAC/BK,EAAaD,EAAc7R,EAAOmM,gBAAgBnN,GAClD+S,EAAiBF,GAAe,GAAKA,GAAe7R,EAAOuD,KAAOvD,EAAOmM,gBAAgBnN,GACzFgT,EAAYH,GAAe,GAAKA,EAAc7R,EAAOuD,KAAO,GAAKuO,EAAa,GAAKA,GAAc9R,EAAOuD,MAAQsO,GAAe,GAAKC,GAAc9R,EAAOuD,KAC3JyO,IACFhS,EAAO+Q,cAAc/O,KAAK2L,GAC1B3N,EAAOwR,qBAAqBxP,KAAKhD,IAEnCsJ,EAAqBqF,EAAOqE,EAAWxR,EAAOyR,mBAC9C3J,EAAqBqF,EAAOoE,EAAgBvR,EAAO0R,wBACnDvE,EAAMzM,SAAWwK,GAAOgG,EAAgBA,EACxC/D,EAAMwE,iBAAmBzG,GAAOkG,EAAwBA,CAC1D,CACF,EA4VEQ,eA1VF,SAAwBhS,GACtB,MAAMJ,EAAS3C,KACf,QAAyB,IAAd+C,EAA2B,CACpC,MAAMiS,EAAarS,EAAOyL,cAAgB,EAAI,EAE9CrL,EAAYJ,GAAUA,EAAOI,WAAaJ,EAAOI,UAAYiS,GAAc,CAC7E,CACA,MAAM7R,EAASR,EAAOQ,OAChB8R,EAAiBtS,EAAOuS,eAAiBvS,EAAO2R,eACtD,IAAI,SACFzQ,EAAQ,YACRsR,EAAW,MACXC,EAAK,aACLC,GACE1S,EACJ,MAAM2S,EAAeH,EACfI,EAASH,EACf,GAAuB,IAAnBH,EACFpR,EAAW,EACXsR,GAAc,EACdC,GAAQ,MACH,CACLvR,GAAYd,EAAYJ,EAAO2R,gBAAkBW,EACjD,MAAMO,EAAqB1R,KAAKqN,IAAIpO,EAAYJ,EAAO2R,gBAAkB,EACnEmB,EAAe3R,KAAKqN,IAAIpO,EAAYJ,EAAOuS,gBAAkB,EACnEC,EAAcK,GAAsB3R,GAAY,EAChDuR,EAAQK,GAAgB5R,GAAY,EAChC2R,IAAoB3R,EAAW,GAC/B4R,IAAc5R,EAAW,EAC/B,CACA,GAAIV,EAAOgK,KAAM,CACf,MAAMuI,EAAkB/S,EAAO8Q,oBAAoB,GAC7CkC,EAAiBhT,EAAO8Q,oBAAoB9Q,EAAOsJ,OAAO1O,OAAS,GACnEqY,EAAsBjT,EAAOkM,WAAW6G,GACxCG,EAAqBlT,EAAOkM,WAAW8G,GACvCG,EAAenT,EAAOkM,WAAWlM,EAAOkM,WAAWtR,OAAS,GAC5DwY,EAAejS,KAAKqN,IAAIpO,GAE5BsS,EADEU,GAAgBH,GACFG,EAAeH,GAAuBE,GAEtCC,EAAeD,EAAeD,GAAsBC,EAElET,EAAe,IAAGA,GAAgB,EACxC,CACApZ,OAAO4R,OAAOlL,EAAQ,CACpBkB,WACAwR,eACAF,cACAC,WAEEjS,EAAO2P,qBAAuB3P,EAAO2M,gBAAkB3M,EAAO6S,aAAYrT,EAAOsR,qBAAqBlR,GACtGoS,IAAgBG,GAClB3S,EAAOgI,KAAK,yBAEVyK,IAAUG,GACZ5S,EAAOgI,KAAK,oBAEV2K,IAAiBH,GAAeI,IAAWH,IAC7CzS,EAAOgI,KAAK,YAEdhI,EAAOgI,KAAK,WAAY9G,EAC1B,EA8REoS,oBArRF,WACE,MAAMtT,EAAS3C,MACT,OACJiM,EAAM,OACN9I,EAAM,SACN+K,EAAQ,YACRzB,GACE9J,EACE4L,EAAY5L,EAAO6L,SAAWrL,EAAOqL,QAAQC,QAC7CuB,EAAcrN,EAAO+J,MAAQvJ,EAAOuJ,MAAQvJ,EAAOuJ,KAAKC,KAAO,EAC/DuJ,EAAmBzR,GAChBF,EAAgB2J,EAAU,IAAI/K,EAAOyI,aAAanH,kBAAyBA,KAAY,GAEhG,IAAI0R,EACAC,EACAC,EACJ,GAAI9H,EACF,GAAIpL,EAAOgK,KAAM,CACf,IAAI4E,EAAatF,EAAc9J,EAAO6L,QAAQoD,aAC1CG,EAAa,IAAGA,EAAapP,EAAO6L,QAAQvC,OAAO1O,OAASwU,GAC5DA,GAAcpP,EAAO6L,QAAQvC,OAAO1O,SAAQwU,GAAcpP,EAAO6L,QAAQvC,OAAO1O,QACpF4Y,EAAcD,EAAiB,6BAA6BnE,MAC9D,MACEoE,EAAcD,EAAiB,6BAA6BzJ,YAG1DuD,GACFmG,EAAclK,EAAOqK,KAAKpL,GAAWA,EAAQ8B,SAAWP,GACxD4J,EAAYpK,EAAOqK,KAAKpL,GAAWA,EAAQ8B,SAAWP,EAAc,GACpE2J,EAAYnK,EAAOqK,KAAKpL,GAAWA,EAAQ8B,SAAWP,EAAc,IAEpE0J,EAAclK,EAAOQ,GAGrB0J,IACGnG,IAEHqG,EDrmBN,SAAwBhU,EAAIoC,GAC1B,MAAM8R,EAAU,GAChB,KAAOlU,EAAGmU,oBAAoB,CAC5B,MAAMC,EAAOpU,EAAGmU,mBACZ/R,EACEgS,EAAK5R,QAAQJ,IAAW8R,EAAQ5R,KAAK8R,GACpCF,EAAQ5R,KAAK8R,GACpBpU,EAAKoU,CACP,CACA,OAAOF,CACT,CC2lBkBG,CAAeP,EAAa,IAAIhT,EAAOyI,4BAA4B,GAC3EzI,EAAOgK,OAASkJ,IAClBA,EAAYpK,EAAO,IAIrBmK,EDtnBN,SAAwB/T,EAAIoC,GAC1B,MAAMkS,EAAU,GAChB,KAAOtU,EAAGuU,wBAAwB,CAChC,MAAMC,EAAOxU,EAAGuU,uBACZnS,EACEoS,EAAKhS,QAAQJ,IAAWkS,EAAQhS,KAAKkS,GACpCF,EAAQhS,KAAKkS,GACpBxU,EAAKwU,CACP,CACA,OAAOF,CACT,CC4mBkBG,CAAeX,EAAa,IAAIhT,EAAOyI,4BAA4B,GAC3EzI,EAAOgK,MAAuB,KAAdiJ,IAClBA,EAAYnK,EAAOA,EAAO1O,OAAS,MAIzC0O,EAAO3O,QAAQ4N,IACbK,EAAmBL,EAASA,IAAYiL,EAAahT,EAAO4T,kBAC5DxL,EAAmBL,EAASA,IAAYmL,EAAWlT,EAAO6T,gBAC1DzL,EAAmBL,EAASA,IAAYkL,EAAWjT,EAAO8T,kBAE5DtU,EAAOuU,mBACT,EA+NEC,kBAtIF,SAA2BC,GACzB,MAAMzU,EAAS3C,KACT+C,EAAYJ,EAAOyL,aAAezL,EAAOI,WAAaJ,EAAOI,WAC7D,SACJ6L,EAAQ,OACRzL,EACAsJ,YAAa4K,EACbjK,UAAWkK,EACX9E,UAAW+E,GACT5U,EACJ,IACI6P,EADA/F,EAAc2K,EAElB,MAAMI,EAAsBC,IAC1B,IAAIrK,EAAYqK,EAAS9U,EAAO6L,QAAQoD,aAOxC,OANIxE,EAAY,IACdA,EAAYzK,EAAO6L,QAAQvC,OAAO1O,OAAS6P,GAEzCA,GAAazK,EAAO6L,QAAQvC,OAAO1O,SACrC6P,GAAazK,EAAO6L,QAAQvC,OAAO1O,QAE9B6P,GAKT,QAH2B,IAAhBX,IACTA,EA/CJ,SAAmC9J,GACjC,MAAM,WACJkM,EAAU,OACV1L,GACER,EACEI,EAAYJ,EAAOyL,aAAezL,EAAOI,WAAaJ,EAAOI,UACnE,IAAI0J,EACJ,IAAK,IAAI9K,EAAI,EAAGA,EAAIkN,EAAWtR,OAAQoE,GAAK,OACT,IAAtBkN,EAAWlN,EAAI,GACpBoB,GAAa8L,EAAWlN,IAAMoB,EAAY8L,EAAWlN,EAAI,IAAMkN,EAAWlN,EAAI,GAAKkN,EAAWlN,IAAM,EACtG8K,EAAc9K,EACLoB,GAAa8L,EAAWlN,IAAMoB,EAAY8L,EAAWlN,EAAI,KAClE8K,EAAc9K,EAAI,GAEXoB,GAAa8L,EAAWlN,KACjC8K,EAAc9K,GAOlB,OAHIwB,EAAOuU,sBACLjL,EAAc,QAA4B,IAAhBA,KAA6BA,EAAc,GAEpEA,CACT,CAwBkBkL,CAA0BhV,IAEtCiM,EAASvR,QAAQ0F,IAAc,EACjCyP,EAAY5D,EAASvR,QAAQ0F,OACxB,CACL,MAAM6U,EAAO9T,KAAKE,IAAIb,EAAOkO,mBAAoB5E,GACjD+F,EAAYoF,EAAO9T,KAAKmN,OAAOxE,EAAcmL,GAAQzU,EAAOiO,eAC9D,CAEA,GADIoB,GAAa5D,EAASrR,SAAQiV,EAAY5D,EAASrR,OAAS,GAC5DkP,IAAgB4K,IAAkB1U,EAAOQ,OAAOgK,KAKlD,YAJIqF,IAAc+E,IAChB5U,EAAO6P,UAAYA,EACnB7P,EAAOgI,KAAK,qBAIhB,GAAI8B,IAAgB4K,GAAiB1U,EAAOQ,OAAOgK,MAAQxK,EAAO6L,SAAW7L,EAAOQ,OAAOqL,QAAQC,QAEjG,YADA9L,EAAOyK,UAAYoK,EAAoB/K,IAGzC,MAAMuD,EAAcrN,EAAO+J,MAAQvJ,EAAOuJ,MAAQvJ,EAAOuJ,KAAKC,KAAO,EAGrE,IAAIS,EACJ,GAAIzK,EAAO6L,SAAWrL,EAAOqL,QAAQC,SAAWtL,EAAOgK,KACrDC,EAAYoK,EAAoB/K,QAC3B,GAAIuD,EAAa,CACtB,MAAM6H,EAAqBlV,EAAOsJ,OAAOqK,KAAKpL,GAAWA,EAAQ8B,SAAWP,GAC5E,IAAIqL,EAAmBnK,SAASkK,EAAmBE,aAAa,2BAA4B,IACxFlP,OAAO+E,MAAMkK,KACfA,EAAmBhU,KAAKC,IAAIpB,EAAOsJ,OAAO5O,QAAQwa,GAAqB,IAEzEzK,EAAYtJ,KAAKmN,MAAM6G,EAAmB3U,EAAOuJ,KAAKC,KACxD,MAAO,GAAIhK,EAAOsJ,OAAOQ,GAAc,CACrC,MAAMsF,EAAapP,EAAOsJ,OAAOQ,GAAasL,aAAa,2BAEzD3K,EADE2E,EACUpE,SAASoE,EAAY,IAErBtF,CAEhB,MACEW,EAAYX,EAEdxQ,OAAO4R,OAAOlL,EAAQ,CACpB4U,oBACA/E,YACA8E,oBACAlK,YACAiK,gBACA5K,gBAEE9J,EAAOqV,aACT7L,EAAQxJ,GAEVA,EAAOgI,KAAK,qBACZhI,EAAOgI,KAAK,oBACRhI,EAAOqV,aAAerV,EAAOQ,OAAO8U,sBAClCX,IAAsBlK,GACxBzK,EAAOgI,KAAK,mBAEdhI,EAAOgI,KAAK,eAEhB,EAkDEuN,mBAhDF,SAA4B7V,EAAI8V,GAC9B,MAAMxV,EAAS3C,KACTmD,EAASR,EAAOQ,OACtB,IAAImN,EAAQjO,EAAGqJ,QAAQ,IAAIvI,EAAOyI,6BAC7B0E,GAAS3N,EAAOgJ,WAAawM,GAAQA,EAAK5a,OAAS,GAAK4a,EAAK3P,SAASnG,IACzE,IAAI8V,EAAKhX,MAAMgX,EAAK9a,QAAQgF,GAAM,EAAG8V,EAAK5a,SAASD,QAAQ8a,KACpD9H,GAAS8H,EAAOvT,SAAWuT,EAAOvT,QAAQ,IAAI1B,EAAOyI,8BACxD0E,EAAQ8H,KAId,IACIrG,EADAsG,GAAa,EAEjB,GAAI/H,EACF,IAAK,IAAI3O,EAAI,EAAGA,EAAIgB,EAAOsJ,OAAO1O,OAAQoE,GAAK,EAC7C,GAAIgB,EAAOsJ,OAAOtK,KAAO2O,EAAO,CAC9B+H,GAAa,EACbtG,EAAapQ,EACb,KACF,CAGJ,IAAI2O,IAAS+H,EAUX,OAFA1V,EAAO2V,kBAAe5W,OACtBiB,EAAO4V,kBAAe7W,GARtBiB,EAAO2V,aAAehI,EAClB3N,EAAO6L,SAAW7L,EAAOQ,OAAOqL,QAAQC,QAC1C9L,EAAO4V,aAAe5K,SAAS2C,EAAMyH,aAAa,2BAA4B,IAE9EpV,EAAO4V,aAAexG,EAOtB5O,EAAOqV,0BAA+C9W,IAAxBiB,EAAO4V,cAA8B5V,EAAO4V,eAAiB5V,EAAO8J,aACpG9J,EAAO6V,qBAEX,GA+KIzV,EAAY,CACd0V,aAlKF,SAA4BC,QACb,IAATA,IACFA,EAAO1Y,KAAKyN,eAAiB,IAAM,KAErC,MACM,OACJtK,EACAiL,aAAcC,EAAG,UACjBtL,EAAS,UACTM,GALarD,KAOf,GAAImD,EAAOwV,iBACT,OAAOtK,GAAOtL,EAAYA,EAE5B,GAAII,EAAO4M,QACT,OAAOhN,EAET,IAAI6V,EDhkCN,SAAsBvW,EAAIqW,QACX,IAATA,IACFA,EAAO,KAET,MAAM5X,EAAS,IACf,IAAI+X,EACAC,EACAC,EACJ,MAAMC,EAtBR,SAA0B3W,GACxB,MAAMvB,EAAS,IACf,IAAIvC,EAUJ,OATIuC,EAAOb,mBACT1B,EAAQuC,EAAOb,iBAAiBoC,EAAI,QAEjC9D,GAAS8D,EAAG4W,eACf1a,EAAQ8D,EAAG4W,cAER1a,IACHA,EAAQ8D,EAAG9D,OAENA,CACT,CASmB,CAAiB8D,GA6BlC,OA5BIvB,EAAOoY,iBACTJ,EAAeE,EAAStI,WAAasI,EAASpI,gBAC1CkI,EAAapT,MAAM,KAAKnI,OAAS,IACnCub,EAAeA,EAAapT,MAAM,MAAMiD,IAAI/M,GAAKA,EAAE4T,QAAQ,IAAK,MAAM2J,KAAK,OAI7EJ,EAAkB,IAAIjY,EAAOoY,gBAAiC,SAAjBJ,EAA0B,GAAKA,KAE5EC,EAAkBC,EAASI,cAAgBJ,EAASK,YAAcL,EAASM,aAAeN,EAASO,aAAeP,EAAStI,WAAasI,EAAS9Y,iBAAiB,aAAasP,QAAQ,aAAc,sBACrMqJ,EAASE,EAAgB7X,WAAWwE,MAAM,MAE/B,MAATgT,IAE0BI,EAAxBhY,EAAOoY,gBAAgCH,EAAgBS,IAEhC,KAAlBX,EAAOtb,OAA8B6I,WAAWyS,EAAO,KAE5CzS,WAAWyS,EAAO,KAE3B,MAATH,IAE0BI,EAAxBhY,EAAOoY,gBAAgCH,EAAgBU,IAEhC,KAAlBZ,EAAOtb,OAA8B6I,WAAWyS,EAAO,KAE5CzS,WAAWyS,EAAO,KAEjCC,GAAgB,CACzB,CC0hCyBL,CAAapV,EAAWqV,GAG/C,OAFAE,GAde5Y,KAcYgU,wBACvB3F,IAAKuK,GAAoBA,GACtBA,GAAoB,CAC7B,EA8IEc,aA5IF,SAAsB3W,EAAW4W,GAC/B,MAAMhX,EAAS3C,MAEboO,aAAcC,EAAG,OACjBlL,EAAM,UACNE,EAAS,SACTQ,GACElB,EACJ,IA0BIiX,EA1BAC,EAAI,EACJC,EAAI,EAEJnX,EAAO8K,eACToM,EAAIxL,GAAOtL,EAAYA,EAEvB+W,EAAI/W,EAEFI,EAAO0N,eACTgJ,EAAI/V,KAAKmN,MAAM4I,GACfC,EAAIhW,KAAKmN,MAAM6I,IAEjBnX,EAAOoX,kBAAoBpX,EAAOI,UAClCJ,EAAOI,UAAYJ,EAAO8K,eAAiBoM,EAAIC,EAC3C3W,EAAO4M,QACT1M,EAAUV,EAAO8K,eAAiB,aAAe,aAAe9K,EAAO8K,gBAAkBoM,GAAKC,EACpF3W,EAAOwV,mBACbhW,EAAO8K,eACToM,GAAKlX,EAAOqR,wBAEZ8F,GAAKnX,EAAOqR,wBAEd3Q,EAAU9E,MAAMmS,UAAY,eAAemJ,QAAQC,aAKrD,MAAM7E,EAAiBtS,EAAOuS,eAAiBvS,EAAO2R,eAEpDsF,EADqB,IAAnB3E,EACY,GAEClS,EAAYJ,EAAO2R,gBAAkBW,EAElD2E,IAAgB/V,GAClBlB,EAAOoS,eAAehS,GAExBJ,EAAOgI,KAAK,eAAgBhI,EAAOI,UAAW4W,EAChD,EAgGErF,aA9FF,WACE,OAAQtU,KAAK4O,SAAS,EACxB,EA6FEsG,aA3FF,WACE,OAAQlV,KAAK4O,SAAS5O,KAAK4O,SAASrR,OAAS,EAC/C,EA0FEyc,YAxFF,SAAqBjX,EAAWK,EAAO6W,EAAcC,EAAiBC,QAClD,IAAdpX,IACFA,EAAY,QAEA,IAAVK,IACFA,EAAQpD,KAAKmD,OAAOC,YAED,IAAjB6W,IACFA,GAAe,QAEO,IAApBC,IACFA,GAAkB,GAEpB,MAAMvX,EAAS3C,MACT,OACJmD,EAAM,UACNE,GACEV,EACJ,GAAIA,EAAOyX,WAAajX,EAAOkX,+BAC7B,OAAO,EAET,MAAM/F,EAAe3R,EAAO2R,eACtBY,EAAevS,EAAOuS,eAC5B,IAAIoF,EAKJ,GAJiDA,EAA7CJ,GAAmBnX,EAAYuR,EAA6BA,EAAsB4F,GAAmBnX,EAAYmS,EAA6BA,EAAiCnS,EAGnLJ,EAAOoS,eAAeuF,GAClBnX,EAAO4M,QAAS,CAClB,MAAMwK,EAAM5X,EAAO8K,eACnB,GAAc,IAAVrK,EACFC,EAAUkX,EAAM,aAAe,cAAgBD,MAC1C,CACL,IAAK3X,EAAO2D,QAAQI,aAMlB,OALAjE,EAAqB,CACnBE,SACAC,gBAAiB0X,EACjBzX,KAAM0X,EAAM,OAAS,SAEhB,EAETlX,EAAUgB,SAAS,CACjB,CAACkW,EAAM,OAAS,QAASD,EACzBE,SAAU,UAEd,CACA,OAAO,CACT,CAiCA,OAhCc,IAAVpX,GACFT,EAAO4Q,cAAc,GACrB5Q,EAAO+W,aAAaY,GAChBL,IACFtX,EAAOgI,KAAK,wBAAyBvH,EAAO+W,GAC5CxX,EAAOgI,KAAK,oBAGdhI,EAAO4Q,cAAcnQ,GACrBT,EAAO+W,aAAaY,GAChBL,IACFtX,EAAOgI,KAAK,wBAAyBvH,EAAO+W,GAC5CxX,EAAOgI,KAAK,oBAEThI,EAAOyX,YACVzX,EAAOyX,WAAY,EACdzX,EAAO8X,oCACV9X,EAAO8X,kCAAoC,SAAuBC,GAC3D/X,IAAUA,EAAO+G,WAClBgR,EAAE1d,SAAWgD,OACjB2C,EAAOU,UAAU1F,oBAAoB,gBAAiBgF,EAAO8X,mCAC7D9X,EAAO8X,kCAAoC,YACpC9X,EAAO8X,kCACd9X,EAAOyX,WAAY,EACfH,GACFtX,EAAOgI,KAAK,iBAEhB,GAEFhI,EAAOU,UAAU3F,iBAAiB,gBAAiBiF,EAAO8X,sCAGvD,CACT,GAmBA,SAASE,EAAejY,GACtB,IAAI,OACFC,EAAM,aACNsX,EAAY,UACZW,EAAS,KACTC,GACEnY,EACJ,MAAM,YACJ+J,EAAW,cACX4K,GACE1U,EACJ,IAAIa,EAAMoX,EACLpX,IAC8BA,EAA7BiJ,EAAc4K,EAAqB,OAAgB5K,EAAc4K,EAAqB,OAAkB,SAE9G1U,EAAOgI,KAAK,aAAakQ,KACrBZ,GAAwB,UAARzW,EAClBb,EAAOgI,KAAK,uBAAuBkQ,KAC1BZ,GAAgBxN,IAAgB4K,IACzC1U,EAAOgI,KAAK,wBAAwBkQ,KACxB,SAARrX,EACFb,EAAOgI,KAAK,sBAAsBkQ,KAElClY,EAAOgI,KAAK,sBAAsBkQ,KAGxC,CAudA,IAAIvK,EAAQ,CACVwK,QAzaF,SAAiBtQ,EAAOpH,EAAO6W,EAAcE,EAAUY,QACvC,IAAVvQ,IACFA,EAAQ,QAEW,IAAjByP,IACFA,GAAe,GAEI,iBAAVzP,IACTA,EAAQmD,SAASnD,EAAO,KAE1B,MAAM7H,EAAS3C,KACf,IAAI+R,EAAavH,EACbuH,EAAa,IAAGA,EAAa,GACjC,MAAM,OACJ5O,EAAM,SACNyL,EAAQ,WACRC,EAAU,cACVwI,EAAa,YACb5K,EACA2B,aAAcC,EAAG,UACjBhL,EAAS,QACToL,GACE9L,EACJ,IAAK8L,IAAY0L,IAAaY,GAAWpY,EAAO+G,WAAa/G,EAAOyX,WAAajX,EAAOkX,+BACtF,OAAO,OAEY,IAAVjX,IACTA,EAAQT,EAAOQ,OAAOC,OAExB,MAAMwU,EAAO9T,KAAKE,IAAIrB,EAAOQ,OAAOkO,mBAAoBU,GACxD,IAAIS,EAAYoF,EAAO9T,KAAKmN,OAAOc,EAAa6F,GAAQjV,EAAOQ,OAAOiO,gBAClEoB,GAAa5D,EAASrR,SAAQiV,EAAY5D,EAASrR,OAAS,GAChE,MAAMwF,GAAa6L,EAAS4D,GAE5B,GAAIrP,EAAOuU,oBACT,IAAK,IAAI/V,EAAI,EAAGA,EAAIkN,EAAWtR,OAAQoE,GAAK,EAAG,CAC7C,MAAMqZ,GAAuBlX,KAAKmN,MAAkB,IAAZlO,GAClCkY,EAAiBnX,KAAKmN,MAAsB,IAAhBpC,EAAWlN,IACvCuZ,EAAqBpX,KAAKmN,MAA0B,IAApBpC,EAAWlN,EAAI,SACpB,IAAtBkN,EAAWlN,EAAI,GACpBqZ,GAAuBC,GAAkBD,EAAsBE,GAAsBA,EAAqBD,GAAkB,EAC9HlJ,EAAapQ,EACJqZ,GAAuBC,GAAkBD,EAAsBE,IACxEnJ,EAAapQ,EAAI,GAEVqZ,GAAuBC,IAChClJ,EAAapQ,EAEjB,CAGF,GAAIgB,EAAOqV,aAAejG,IAAetF,EAAa,CACpD,IAAK9J,EAAOwY,iBAAmB9M,EAAMtL,EAAYJ,EAAOI,WAAaA,EAAYJ,EAAO2R,eAAiBvR,EAAYJ,EAAOI,WAAaA,EAAYJ,EAAO2R,gBAC1J,OAAO,EAET,IAAK3R,EAAOyY,gBAAkBrY,EAAYJ,EAAOI,WAAaA,EAAYJ,EAAOuS,iBAC1EzI,GAAe,KAAOsF,EACzB,OAAO,CAGb,CAOA,IAAI6I,EANA7I,KAAgBsF,GAAiB,IAAM4C,GACzCtX,EAAOgI,KAAK,0BAIdhI,EAAOoS,eAAehS,GAEQ6X,EAA1B7I,EAAatF,EAAyB,OAAgBsF,EAAatF,EAAyB,OAAwB,QAGxH,MAAM8B,EAAY5L,EAAO6L,SAAW7L,EAAOQ,OAAOqL,QAAQC,QAG1D,KAFyBF,IAAawM,KAEZ1M,IAAQtL,IAAcJ,EAAOI,YAAcsL,GAAOtL,IAAcJ,EAAOI,WAc/F,OAbAJ,EAAOwU,kBAAkBpF,GAErB5O,EAAO6S,YACTrT,EAAOyQ,mBAETzQ,EAAOsT,sBACe,UAAlB9S,EAAOmO,QACT3O,EAAO+W,aAAa3W,GAEJ,UAAd6X,IACFjY,EAAO0Y,gBAAgBpB,EAAcW,GACrCjY,EAAO2Y,cAAcrB,EAAcW,KAE9B,EAET,GAAIzX,EAAO4M,QAAS,CAClB,MAAMwK,EAAM5X,EAAO8K,eACb8N,EAAIlN,EAAMtL,GAAaA,EAC7B,GAAc,IAAVK,EACEmL,IACF5L,EAAOU,UAAU9E,MAAM+E,eAAiB,OACxCX,EAAO6Y,mBAAoB,GAEzBjN,IAAc5L,EAAO8Y,2BAA6B9Y,EAAOQ,OAAOuY,aAAe,GACjF/Y,EAAO8Y,2BAA4B,EACnChb,sBAAsB,KACpB4C,EAAUkX,EAAM,aAAe,aAAegB,KAGhDlY,EAAUkX,EAAM,aAAe,aAAegB,EAE5ChN,GACF9N,sBAAsB,KACpBkC,EAAOU,UAAU9E,MAAM+E,eAAiB,GACxCX,EAAO6Y,mBAAoB,QAG1B,CACL,IAAK7Y,EAAO2D,QAAQI,aAMlB,OALAjE,EAAqB,CACnBE,SACAC,eAAgB2Y,EAChB1Y,KAAM0X,EAAM,OAAS,SAEhB,EAETlX,EAAUgB,SAAS,CACjB,CAACkW,EAAM,OAAS,OAAQgB,EACxBf,SAAU,UAEd,CACA,OAAO,CACT,CACA,MACMnS,EADUF,IACSE,SA0BzB,OAzBIkG,IAAcwM,GAAW1S,GAAY1F,EAAOgJ,WAC9ChJ,EAAO6L,QAAQnB,QAAO,GAAO,EAAO0E,GAEtCpP,EAAO4Q,cAAcnQ,GACrBT,EAAO+W,aAAa3W,GACpBJ,EAAOwU,kBAAkBpF,GACzBpP,EAAOsT,sBACPtT,EAAOgI,KAAK,wBAAyBvH,EAAO+W,GAC5CxX,EAAO0Y,gBAAgBpB,EAAcW,GACvB,IAAVxX,EACFT,EAAO2Y,cAAcrB,EAAcW,GACzBjY,EAAOyX,YACjBzX,EAAOyX,WAAY,EACdzX,EAAOgZ,gCACVhZ,EAAOgZ,8BAAgC,SAAuBjB,GACvD/X,IAAUA,EAAO+G,WAClBgR,EAAE1d,SAAWgD,OACjB2C,EAAOU,UAAU1F,oBAAoB,gBAAiBgF,EAAOgZ,+BAC7DhZ,EAAOgZ,8BAAgC,YAChChZ,EAAOgZ,8BACdhZ,EAAO2Y,cAAcrB,EAAcW,GACrC,GAEFjY,EAAOU,UAAU3F,iBAAiB,gBAAiBiF,EAAOgZ,iCAErD,CACT,EA8QEC,YA5QF,SAAqBpR,EAAOpH,EAAO6W,EAAcE,QACjC,IAAV3P,IACFA,EAAQ,QAEW,IAAjByP,IACFA,GAAe,GAEI,iBAAVzP,IAETA,EADsBmD,SAASnD,EAAO,KAGxC,MAAM7H,EAAS3C,KACf,GAAI2C,EAAO+G,UAAW,YACD,IAAVtG,IACTA,EAAQT,EAAOQ,OAAOC,OAExB,MAAM4M,EAAcrN,EAAO+J,MAAQ/J,EAAOQ,OAAOuJ,MAAQ/J,EAAOQ,OAAOuJ,KAAKC,KAAO,EACnF,IAAIkP,EAAWrR,EACf,GAAI7H,EAAOQ,OAAOgK,KAChB,GAAIxK,EAAO6L,SAAW7L,EAAOQ,OAAOqL,QAAQC,QAE1CoN,GAAsBlZ,EAAO6L,QAAQoD,iBAChC,CACL,IAAIkK,EACJ,GAAI9L,EAAa,CACf,MAAM+B,EAAa8J,EAAWlZ,EAAOQ,OAAOuJ,KAAKC,KACjDmP,EAAmBnZ,EAAOsJ,OAAOqK,KAAKpL,GAA6D,EAAlDA,EAAQ6M,aAAa,6BAAmChG,GAAY/E,MACvH,MACE8O,EAAmBnZ,EAAO8Q,oBAAoBoI,GAEhD,MAAME,EAAO/L,EAAclM,KAAK0I,KAAK7J,EAAOsJ,OAAO1O,OAASoF,EAAOQ,OAAOuJ,KAAKC,MAAQhK,EAAOsJ,OAAO1O,QAC/F,eACJuS,GACEnN,EAAOQ,OACX,IAAImJ,EAAgB3J,EAAOQ,OAAOmJ,cACZ,SAAlBA,EACFA,EAAgB3J,EAAO4J,wBAEvBD,EAAgBxI,KAAK0I,KAAKpG,WAAWzD,EAAOQ,OAAOmJ,cAAe,KAC9DwD,GAAkBxD,EAAgB,GAAM,IAC1CA,GAAgC,IAGpC,IAAI0P,EAAcD,EAAOD,EAAmBxP,EAO5C,GANIwD,IACFkM,EAAcA,GAAeF,EAAmBhY,KAAK0I,KAAKF,EAAgB,IAExE6N,GAAYrK,GAAkD,SAAhCnN,EAAOQ,OAAOmJ,gBAA6B0D,IAC3EgM,GAAc,GAEZA,EAAa,CACf,MAAMpB,EAAY9K,EAAiBgM,EAAmBnZ,EAAO8J,YAAc,OAAS,OAASqP,EAAmBnZ,EAAO8J,YAAc,EAAI9J,EAAOQ,OAAOmJ,cAAgB,OAAS,OAChL3J,EAAOsZ,QAAQ,CACbrB,YACAE,SAAS,EACThD,iBAAgC,SAAd8C,EAAuBkB,EAAmB,EAAIA,EAAmBC,EAAO,EAC1FG,eAA8B,SAAdtB,EAAuBjY,EAAOyK,eAAY1L,GAE9D,CACA,GAAIsO,EAAa,CACf,MAAM+B,EAAa8J,EAAWlZ,EAAOQ,OAAOuJ,KAAKC,KACjDkP,EAAWlZ,EAAOsJ,OAAOqK,KAAKpL,GAA6D,EAAlDA,EAAQ6M,aAAa,6BAAmChG,GAAY/E,MAC/G,MACE6O,EAAWlZ,EAAO8Q,oBAAoBoI,EAE1C,CAKF,OAHApb,sBAAsB,KACpBkC,EAAOmY,QAAQe,EAAUzY,EAAO6W,EAAcE,KAEzCxX,CACT,EAsMEwZ,UAnMF,SAAmB/Y,EAAO6W,EAAcE,QACjB,IAAjBF,IACFA,GAAe,GAEjB,MAAMtX,EAAS3C,MACT,QACJyO,EAAO,OACPtL,EAAM,UACNiX,GACEzX,EACJ,IAAK8L,GAAW9L,EAAO+G,UAAW,OAAO/G,OACpB,IAAVS,IACTA,EAAQT,EAAOQ,OAAOC,OAExB,IAAIgZ,EAAWjZ,EAAOiO,eACO,SAAzBjO,EAAOmJ,eAAsD,IAA1BnJ,EAAOiO,gBAAwBjO,EAAOkZ,qBAC3ED,EAAWtY,KAAKC,IAAIpB,EAAO4J,qBAAqB,WAAW,GAAO,IAEpE,MAAM+P,EAAY3Z,EAAO8J,YAActJ,EAAOkO,mBAAqB,EAAI+K,EACjE7N,EAAY5L,EAAO6L,SAAWrL,EAAOqL,QAAQC,QACnD,GAAItL,EAAOgK,KAAM,CACf,GAAIiN,IAAc7L,GAAapL,EAAOoZ,oBAAqB,OAAO,EAMlE,GALA5Z,EAAOsZ,QAAQ,CACbrB,UAAW,SAGbjY,EAAO6Z,YAAc7Z,EAAOU,UAAUoZ,WAClC9Z,EAAO8J,cAAgB9J,EAAOsJ,OAAO1O,OAAS,GAAK4F,EAAO4M,QAI5D,OAHAtP,sBAAsB,KACpBkC,EAAOmY,QAAQnY,EAAO8J,YAAc6P,EAAWlZ,EAAO6W,EAAcE,MAE/D,CAEX,CACA,OAAIhX,EAAO+J,QAAUvK,EAAOyS,MACnBzS,EAAOmY,QAAQ,EAAG1X,EAAO6W,EAAcE,GAEzCxX,EAAOmY,QAAQnY,EAAO8J,YAAc6P,EAAWlZ,EAAO6W,EAAcE,EAC7E,EA8JEuC,UA3JF,SAAmBtZ,EAAO6W,EAAcE,QACjB,IAAjBF,IACFA,GAAe,GAEjB,MAAMtX,EAAS3C,MACT,OACJmD,EAAM,SACNyL,EAAQ,WACRC,EAAU,aACVT,EAAY,QACZK,EAAO,UACP2L,GACEzX,EACJ,IAAK8L,GAAW9L,EAAO+G,UAAW,OAAO/G,OACpB,IAAVS,IACTA,EAAQT,EAAOQ,OAAOC,OAExB,MAAMmL,EAAY5L,EAAO6L,SAAWrL,EAAOqL,QAAQC,QACnD,GAAItL,EAAOgK,KAAM,CACf,GAAIiN,IAAc7L,GAAapL,EAAOoZ,oBAAqB,OAAO,EAClE5Z,EAAOsZ,QAAQ,CACbrB,UAAW,SAGbjY,EAAO6Z,YAAc7Z,EAAOU,UAAUoZ,UACxC,CAEA,SAASE,EAAUC,GACjB,OAAIA,EAAM,GAAW9Y,KAAKmN,MAAMnN,KAAKqN,IAAIyL,IAClC9Y,KAAKmN,MAAM2L,EACpB,CACA,MAAM5B,EAAsB2B,EALVvO,EAAezL,EAAOI,WAAaJ,EAAOI,WAMtD8Z,EAAqBjO,EAASjG,IAAIiU,GAAOD,EAAUC,IACnDE,EAAa3Z,EAAO4Z,UAAY5Z,EAAO4Z,SAAStO,QACtD,IAAIuO,EAAWpO,EAASiO,EAAmBxf,QAAQ2d,GAAuB,GAC1E,QAAwB,IAAbgC,IAA6B7Z,EAAO4M,SAAW+M,GAAa,CACrE,IAAIG,EACJrO,EAAStR,QAAQ,CAAC8U,EAAMI,KAClBwI,GAAuB5I,IAEzB6K,EAAgBzK,UAGS,IAAlByK,IACTD,EAAWF,EAAalO,EAASqO,GAAiBrO,EAASqO,EAAgB,EAAIA,EAAgB,EAAIA,GAEvG,CACA,IAAIC,EAAY,EAShB,QARwB,IAAbF,IACTE,EAAYrO,EAAWxR,QAAQ2f,GAC3BE,EAAY,IAAGA,EAAYva,EAAO8J,YAAc,GACvB,SAAzBtJ,EAAOmJ,eAAsD,IAA1BnJ,EAAOiO,gBAAwBjO,EAAOkZ,qBAC3Ea,EAAYA,EAAYva,EAAO4J,qBAAqB,YAAY,GAAQ,EACxE2Q,EAAYpZ,KAAKC,IAAImZ,EAAW,KAGhC/Z,EAAO+J,QAAUvK,EAAOwS,YAAa,CACvC,MAAMgI,EAAYxa,EAAOQ,OAAOqL,SAAW7L,EAAOQ,OAAOqL,QAAQC,SAAW9L,EAAO6L,QAAU7L,EAAO6L,QAAQvC,OAAO1O,OAAS,EAAIoF,EAAOsJ,OAAO1O,OAAS,EACvJ,OAAOoF,EAAOmY,QAAQqC,EAAW/Z,EAAO6W,EAAcE,EACxD,CAAO,OAAIhX,EAAOgK,MAA+B,IAAvBxK,EAAO8J,aAAqBtJ,EAAO4M,SAC3DtP,sBAAsB,KACpBkC,EAAOmY,QAAQoC,EAAW9Z,EAAO6W,EAAcE,MAE1C,GAEFxX,EAAOmY,QAAQoC,EAAW9Z,EAAO6W,EAAcE,EACxD,EA0FEiD,WAvFF,SAAoBha,EAAO6W,EAAcE,QAClB,IAAjBF,IACFA,GAAe,GAEjB,MAAMtX,EAAS3C,KACf,IAAI2C,EAAO+G,UAIX,YAHqB,IAAVtG,IACTA,EAAQT,EAAOQ,OAAOC,OAEjBT,EAAOmY,QAAQnY,EAAO8J,YAAarJ,EAAO6W,EAAcE,EACjE,EA8EEkD,eA3EF,SAAwBja,EAAO6W,EAAcE,EAAUmD,QAChC,IAAjBrD,IACFA,GAAe,QAEC,IAAdqD,IACFA,EAAY,IAEd,MAAM3a,EAAS3C,KACf,GAAI2C,EAAO+G,UAAW,YACD,IAAVtG,IACTA,EAAQT,EAAOQ,OAAOC,OAExB,IAAIoH,EAAQ7H,EAAO8J,YACnB,MAAMmL,EAAO9T,KAAKE,IAAIrB,EAAOQ,OAAOkO,mBAAoB7G,GAClDgI,EAAYoF,EAAO9T,KAAKmN,OAAOzG,EAAQoN,GAAQjV,EAAOQ,OAAOiO,gBAC7DrO,EAAYJ,EAAOyL,aAAezL,EAAOI,WAAaJ,EAAOI,UACnE,GAAIA,GAAaJ,EAAOiM,SAAS4D,GAAY,CAG3C,MAAM+K,EAAc5a,EAAOiM,SAAS4D,GAEhCzP,EAAYwa,GADC5a,EAAOiM,SAAS4D,EAAY,GACH+K,GAAeD,IACvD9S,GAAS7H,EAAOQ,OAAOiO,eAE3B,KAAO,CAGL,MAAM4L,EAAWra,EAAOiM,SAAS4D,EAAY,GAEzCzP,EAAYia,IADIra,EAAOiM,SAAS4D,GACOwK,GAAYM,IACrD9S,GAAS7H,EAAOQ,OAAOiO,eAE3B,CAGA,OAFA5G,EAAQ1G,KAAKC,IAAIyG,EAAO,GACxBA,EAAQ1G,KAAKE,IAAIwG,EAAO7H,EAAOkM,WAAWtR,OAAS,GAC5CoF,EAAOmY,QAAQtQ,EAAOpH,EAAO6W,EAAcE,EACpD,EAwCE3B,oBAtCF,WACE,MAAM7V,EAAS3C,KACf,GAAI2C,EAAO+G,UAAW,OACtB,MAAM,OACJvG,EAAM,SACN+K,GACEvL,EACE2J,EAAyC,SAAzBnJ,EAAOmJ,cAA2B3J,EAAO4J,uBAAyBpJ,EAAOmJ,cAC/F,IACIc,EADAoQ,EAAe7a,EAAO8a,sBAAsB9a,EAAO4V,cAEvD,MAAMmF,EAAgB/a,EAAOgJ,UAAY,eAAiB,IAAIxI,EAAOyI,aAC/D+R,EAAShb,EAAO+J,MAAQ/J,EAAOQ,OAAOuJ,MAAQ/J,EAAOQ,OAAOuJ,KAAKC,KAAO,EAC9E,GAAIxJ,EAAOgK,KAAM,CACf,GAAIxK,EAAOyX,UAAW,OACtBhN,EAAYO,SAAShL,EAAO2V,aAAaP,aAAa,2BAA4B,IAC9E5U,EAAO2M,eACTnN,EAAOiZ,YAAYxO,GACVoQ,GAAgBG,GAAUhb,EAAOsJ,OAAO1O,OAAS+O,GAAiB,GAAK3J,EAAOQ,OAAOuJ,KAAKC,KAAO,GAAKhK,EAAOsJ,OAAO1O,OAAS+O,IACtI3J,EAAOsZ,UACPuB,EAAe7a,EAAOib,cAAcrZ,EAAgB2J,EAAU,GAAGwP,8BAA0CtQ,OAAe,IAC1HrM,EAAS,KACP4B,EAAOmY,QAAQ0C,MAGjB7a,EAAOmY,QAAQ0C,EAEnB,MACE7a,EAAOmY,QAAQ0C,EAEnB,GAgUIrQ,EAAO,CACT0Q,WArTF,SAAoB3B,EAAgBnB,GAClC,MAAMpY,EAAS3C,MACT,OACJmD,EAAM,SACN+K,GACEvL,EACJ,IAAKQ,EAAOgK,MAAQxK,EAAO6L,SAAW7L,EAAOQ,OAAOqL,QAAQC,QAAS,OACrE,MAAMyB,EAAa,KACF3L,EAAgB2J,EAAU,IAAI/K,EAAOyI,4BAC7CtO,QAAQ,CAAC+E,EAAImI,KAClBnI,EAAG7D,aAAa,0BAA2BgM,MAazCwF,EAAcrN,EAAO+J,MAAQvJ,EAAOuJ,MAAQvJ,EAAOuJ,KAAKC,KAAO,EACjExJ,EAAO2a,qBAAuB3a,EAAOiO,eAAiB,GAAKpB,IAXtC,MACvB,MAAM/D,EAAS1H,EAAgB2J,EAAU,IAAI/K,EAAO4a,mBACpD9R,EAAO3O,QAAQ+E,IACbA,EAAGiJ,WAEDW,EAAO1O,OAAS,IAClBoF,EAAOqb,eACPrb,EAAOmL,iBAKTmQ,GAEF,MAAM7M,EAAiBjO,EAAOiO,gBAAkBpB,EAAc7M,EAAOuJ,KAAKC,KAAO,GAC3EuR,EAAkBvb,EAAOsJ,OAAO1O,OAAS6T,IAAmB,EAC5D+M,EAAiBnO,GAAerN,EAAOsJ,OAAO1O,OAAS4F,EAAOuJ,KAAKC,OAAS,EAC5EyR,EAAiBC,IACrB,IAAK,IAAI1c,EAAI,EAAGA,EAAI0c,EAAgB1c,GAAK,EAAG,CAC1C,MAAMuJ,EAAUvI,EAAOgJ,UAAYvN,EAAc,eAAgB,CAAC+E,EAAO4a,kBAAoB3f,EAAc,MAAO,CAAC+E,EAAOyI,WAAYzI,EAAO4a,kBAC7Ipb,EAAOuL,SAASoQ,OAAOpT,EACzB,GAEEgT,GACE/a,EAAO2a,oBAETM,EADoBhN,EAAiBzO,EAAOsJ,OAAO1O,OAAS6T,GAE5DzO,EAAOqb,eACPrb,EAAOmL,gBAEPhJ,EAAY,mLAEdoL,KACSiO,GACLhb,EAAO2a,oBAETM,EADoBjb,EAAOuJ,KAAKC,KAAOhK,EAAOsJ,OAAO1O,OAAS4F,EAAOuJ,KAAKC,MAE1EhK,EAAOqb,eACPrb,EAAOmL,gBAEPhJ,EAAY,8KAEdoL,KAEAA,IAEFvN,EAAOsZ,QAAQ,CACbC,iBACAtB,UAAWzX,EAAO2M,oBAAiBpO,EAAY,OAC/CqZ,WAEJ,EAsPEkB,QApPF,SAAiBhV,GACf,IAAI,eACFiV,EAAc,QACdpB,GAAU,EAAI,UACdF,EAAS,aACTlB,EAAY,iBACZ5B,EAAgB,QAChBiD,EAAO,aACPpB,EAAY,aACZ4E,QACY,IAAVtX,EAAmB,CAAC,EAAIA,EAC5B,MAAMtE,EAAS3C,KACf,IAAK2C,EAAOQ,OAAOgK,KAAM,OACzBxK,EAAOgI,KAAK,iBACZ,MAAM,OACJsB,EAAM,eACNmP,EAAc,eACdD,EAAc,SACdjN,EAAQ,OACR/K,GACER,GACE,eACJmN,EAAc,aACd4L,GACEvY,EAGJ,GAFAR,EAAOyY,gBAAiB,EACxBzY,EAAOwY,gBAAiB,EACpBxY,EAAO6L,SAAWrL,EAAOqL,QAAQC,QAanC,OAZIqM,IACG3X,EAAO2M,gBAAuC,IAArBnN,EAAO6P,UAE1BrP,EAAO2M,gBAAkBnN,EAAO6P,UAAYrP,EAAOmJ,cAC5D3J,EAAOmY,QAAQnY,EAAO6L,QAAQvC,OAAO1O,OAASoF,EAAO6P,UAAW,GAAG,GAAO,GACjE7P,EAAO6P,YAAc7P,EAAOiM,SAASrR,OAAS,GACvDoF,EAAOmY,QAAQnY,EAAO6L,QAAQoD,aAAc,GAAG,GAAO,GAJtDjP,EAAOmY,QAAQnY,EAAO6L,QAAQvC,OAAO1O,OAAQ,GAAG,GAAO,IAO3DoF,EAAOyY,eAAiBA,EACxBzY,EAAOwY,eAAiBA,OACxBxY,EAAOgI,KAAK,WAGd,IAAI2B,EAAgBnJ,EAAOmJ,cACL,SAAlBA,EACFA,EAAgB3J,EAAO4J,wBAEvBD,EAAgBxI,KAAK0I,KAAKpG,WAAWjD,EAAOmJ,cAAe,KACvDwD,GAAkBxD,EAAgB,GAAM,IAC1CA,GAAgC,IAGpC,MAAM8E,EAAiBjO,EAAOkZ,mBAAqB/P,EAAgBnJ,EAAOiO,eAC1E,IAAIoN,EAAe1O,EAAiBhM,KAAKC,IAAIqN,EAAgBtN,KAAK0I,KAAKF,EAAgB,IAAM8E,EACzFoN,EAAepN,IAAmB,IACpCoN,GAAgBpN,EAAiBoN,EAAepN,GAElDoN,GAAgBrb,EAAOsb,qBACvB9b,EAAO6b,aAAeA,EACtB,MAAMxO,EAAcrN,EAAO+J,MAAQvJ,EAAOuJ,MAAQvJ,EAAOuJ,KAAKC,KAAO,EACjEV,EAAO1O,OAAS+O,EAAgBkS,GAAyC,UAAzB7b,EAAOQ,OAAOmO,QAAsBrF,EAAO1O,OAAS+O,EAA+B,EAAfkS,EACtH1Z,EAAY,4OACHkL,GAAoC,QAArB7M,EAAOuJ,KAAKgS,MACpC5Z,EAAY,2EAEd,MAAM6Z,EAAuB,GACvBC,EAAsB,GACtB7C,EAAO/L,EAAclM,KAAK0I,KAAKP,EAAO1O,OAAS4F,EAAOuJ,KAAKC,MAAQV,EAAO1O,OAC1EshB,EAAoB9D,GAAWgB,EAAOL,EAAepP,IAAkBwD,EAC7E,IAAIrD,EAAcoS,EAAoBnD,EAAe/Y,EAAO8J,iBAC5B,IAArBqL,EACTA,EAAmBnV,EAAOib,cAAc3R,EAAOqK,KAAKjU,GAAMA,EAAGgD,UAAUgG,SAASlI,EAAO4T,oBAEvFtK,EAAcqL,EAEhB,MAAMgH,EAAuB,SAAdlE,IAAyBA,EAClCmE,EAAuB,SAAdnE,IAAyBA,EACxC,IAAIoE,EAAkB,EAClBC,EAAiB,EACrB,MACMC,GADiBlP,EAAc/D,EAAO6L,GAAkB9K,OAAS8K,IACrBhI,QAA0C,IAAjB4J,GAAgCpN,EAAgB,EAAI,GAAM,GAErI,GAAI4S,EAA0BV,EAAc,CAC1CQ,EAAkBlb,KAAKC,IAAIya,EAAeU,EAAyB9N,GACnE,IAAK,IAAIzP,EAAI,EAAGA,EAAI6c,EAAeU,EAAyBvd,GAAK,EAAG,CAClE,MAAM6I,EAAQ7I,EAAImC,KAAKmN,MAAMtP,EAAIoa,GAAQA,EACzC,GAAI/L,EAAa,CACf,MAAMmP,EAAoBpD,EAAOvR,EAAQ,EACzC,IAAK,IAAI7I,EAAIsK,EAAO1O,OAAS,EAAGoE,GAAK,EAAGA,GAAK,EACvCsK,EAAOtK,GAAGqL,SAAWmS,GAAmBR,EAAqBha,KAAKhD,EAK1E,MACEgd,EAAqBha,KAAKoX,EAAOvR,EAAQ,EAE7C,CACF,MAAO,GAAI0U,EAA0B5S,EAAgByP,EAAOyC,EAAc,CACxES,EAAiBnb,KAAKC,IAAImb,GAA2BnD,EAAsB,EAAfyC,GAAmBpN,GAC3EyN,IACFI,EAAiBnb,KAAKC,IAAIkb,EAAgB3S,EAAgByP,EAAOL,EAAe,IAElF,IAAK,IAAI/Z,EAAI,EAAGA,EAAIsd,EAAgBtd,GAAK,EAAG,CAC1C,MAAM6I,EAAQ7I,EAAImC,KAAKmN,MAAMtP,EAAIoa,GAAQA,EACrC/L,EACF/D,EAAO3O,QAAQ,CAACgT,EAAOyB,KACjBzB,EAAMtD,SAAWxC,GAAOoU,EAAoBja,KAAKoN,KAGvD6M,EAAoBja,KAAK6F,EAE7B,CACF,CAsCA,GArCA7H,EAAOyc,qBAAsB,EAC7B3e,sBAAsB,KACpBkC,EAAOyc,qBAAsB,IAEF,UAAzBzc,EAAOQ,OAAOmO,QAAsBrF,EAAO1O,OAAS+O,EAA+B,EAAfkS,IAClEI,EAAoBpW,SAASsP,IAC/B8G,EAAoBnU,OAAOmU,EAAoBvhB,QAAQya,GAAmB,GAExE6G,EAAqBnW,SAASsP,IAChC6G,EAAqBlU,OAAOkU,EAAqBthB,QAAQya,GAAmB,IAG5EiH,GACFJ,EAAqBrhB,QAAQkN,IAC3ByB,EAAOzB,GAAO6U,mBAAoB,EAClCnR,EAASoR,QAAQrT,EAAOzB,IACxByB,EAAOzB,GAAO6U,mBAAoB,IAGlCP,GACFF,EAAoBthB,QAAQkN,IAC1ByB,EAAOzB,GAAO6U,mBAAoB,EAClCnR,EAASoQ,OAAOrS,EAAOzB,IACvByB,EAAOzB,GAAO6U,mBAAoB,IAGtC1c,EAAOqb,eACsB,SAAzB7a,EAAOmJ,cACT3J,EAAOmL,eACEkC,IAAgB2O,EAAqBphB,OAAS,GAAKwhB,GAAUH,EAAoBrhB,OAAS,GAAKuhB,IACxGnc,EAAOsJ,OAAO3O,QAAQ,CAACgT,EAAOyB,KAC5BpP,EAAO+J,KAAK6D,YAAYwB,EAAYzB,EAAO3N,EAAOsJ,UAGlD9I,EAAO2P,qBACTnQ,EAAOoQ,qBAEL+H,EACF,GAAI6D,EAAqBphB,OAAS,GAAKwhB,GACrC,QAA8B,IAAnB7C,EAAgC,CACzC,MAAMqD,EAAwB5c,EAAOkM,WAAWpC,GAE1C+S,EADoB7c,EAAOkM,WAAWpC,EAAcuS,GACzBO,EAC7BhB,EACF5b,EAAO+W,aAAa/W,EAAOI,UAAYyc,IAEvC7c,EAAOmY,QAAQrO,EAAc3I,KAAK0I,KAAKwS,GAAkB,GAAG,GAAO,GAC/DtF,IACF/W,EAAO8c,gBAAgBC,eAAiB/c,EAAO8c,gBAAgBC,eAAiBF,EAChF7c,EAAO8c,gBAAgB7G,iBAAmBjW,EAAO8c,gBAAgB7G,iBAAmB4G,GAG1F,MACE,GAAI9F,EAAc,CAChB,MAAMiG,EAAQ3P,EAAc2O,EAAqBphB,OAAS4F,EAAOuJ,KAAKC,KAAOgS,EAAqBphB,OAClGoF,EAAOmY,QAAQnY,EAAO8J,YAAckT,EAAO,GAAG,GAAO,GACrDhd,EAAO8c,gBAAgB7G,iBAAmBjW,EAAOI,SACnD,OAEG,GAAI6b,EAAoBrhB,OAAS,GAAKuhB,EAC3C,QAA8B,IAAnB5C,EAAgC,CACzC,MAAMqD,EAAwB5c,EAAOkM,WAAWpC,GAE1C+S,EADoB7c,EAAOkM,WAAWpC,EAAcwS,GACzBM,EAC7BhB,EACF5b,EAAO+W,aAAa/W,EAAOI,UAAYyc,IAEvC7c,EAAOmY,QAAQrO,EAAcwS,EAAgB,GAAG,GAAO,GACnDvF,IACF/W,EAAO8c,gBAAgBC,eAAiB/c,EAAO8c,gBAAgBC,eAAiBF,EAChF7c,EAAO8c,gBAAgB7G,iBAAmBjW,EAAO8c,gBAAgB7G,iBAAmB4G,GAG1F,KAAO,CACL,MAAMG,EAAQ3P,EAAc4O,EAAoBrhB,OAAS4F,EAAOuJ,KAAKC,KAAOiS,EAAoBrhB,OAChGoF,EAAOmY,QAAQnY,EAAO8J,YAAckT,EAAO,GAAG,GAAO,EACvD,CAKJ,GAFAhd,EAAOyY,eAAiBA,EACxBzY,EAAOwY,eAAiBA,EACpBxY,EAAOid,YAAcjd,EAAOid,WAAWC,UAAYlG,EAAc,CACnE,MAAMmG,EAAa,CACjB5D,iBACAtB,YACAlB,eACA5B,mBACA6B,cAAc,GAEZpU,MAAMC,QAAQ7C,EAAOid,WAAWC,SAClCld,EAAOid,WAAWC,QAAQviB,QAAQqI,KAC3BA,EAAE+D,WAAa/D,EAAExC,OAAOgK,MAAMxH,EAAEsW,QAAQ,IACxC6D,EACHhF,QAASnV,EAAExC,OAAOmJ,gBAAkBnJ,EAAOmJ,eAAgBwO,MAGtDnY,EAAOid,WAAWC,mBAAmBld,EAAO7F,aAAe6F,EAAOid,WAAWC,QAAQ1c,OAAOgK,MACrGxK,EAAOid,WAAWC,QAAQ5D,QAAQ,IAC7B6D,EACHhF,QAASnY,EAAOid,WAAWC,QAAQ1c,OAAOmJ,gBAAkBnJ,EAAOmJ,eAAgBwO,GAGzF,CACAnY,EAAOgI,KAAK,UACd,EA4BEoV,YA1BF,WACE,MAAMpd,EAAS3C,MACT,OACJmD,EAAM,SACN+K,GACEvL,EACJ,IAAKQ,EAAOgK,OAASe,GAAYvL,EAAO6L,SAAW7L,EAAOQ,OAAOqL,QAAQC,QAAS,OAClF9L,EAAOqb,eACP,MAAMgC,EAAiB,GACvBrd,EAAOsJ,OAAO3O,QAAQ4N,IACpB,MAAMV,OAA4C,IAA7BU,EAAQ+U,iBAAqF,EAAlD/U,EAAQ6M,aAAa,2BAAiC7M,EAAQ+U,iBAC9HD,EAAexV,GAASU,IAE1BvI,EAAOsJ,OAAO3O,QAAQ4N,IACpBA,EAAQgB,gBAAgB,6BAE1B8T,EAAe1iB,QAAQ4N,IACrBgD,EAASoQ,OAAOpT,KAElBvI,EAAOqb,eACPrb,EAAOmY,QAAQnY,EAAOyK,UAAW,EACnC,GA6DA,SAAS8S,EAAiBvd,EAAQiH,EAAOuW,GACvC,MAAMrf,EAAS,KACT,OACJqC,GACER,EACEyd,EAAqBjd,EAAOid,mBAC5BC,EAAqBld,EAAOkd,mBAClC,OAAID,KAAuBD,GAAUE,GAAsBF,GAAUrf,EAAOwf,WAAaD,IAC5D,YAAvBD,IACFxW,EAAM2W,kBACC,EAKb,CACA,SAASC,EAAa5W,GACpB,MAAMjH,EAAS3C,KACTV,EAAW,IACjB,IAAIob,EAAI9Q,EACJ8Q,EAAE+F,gBAAe/F,EAAIA,EAAE+F,eAC3B,MAAM7V,EAAOjI,EAAO8c,gBACpB,GAAe,gBAAX/E,EAAEgG,KAAwB,CAC5B,GAAuB,OAAnB9V,EAAK+V,WAAsB/V,EAAK+V,YAAcjG,EAAEiG,UAClD,OAEF/V,EAAK+V,UAAYjG,EAAEiG,SACrB,KAAsB,eAAXjG,EAAEgG,MAAoD,IAA3BhG,EAAEkG,cAAcrjB,SACpDqN,EAAKiW,QAAUnG,EAAEkG,cAAc,GAAGE,YAEpC,GAAe,eAAXpG,EAAEgG,KAGJ,YADAR,EAAiBvd,EAAQ+X,EAAGA,EAAEkG,cAAc,GAAGG,OAGjD,MAAM,OACJ5d,EAAM,QACN6d,EAAO,QACPvS,GACE9L,EACJ,IAAK8L,EAAS,OACd,IAAKtL,EAAO8d,eAAmC,UAAlBvG,EAAEwG,YAAyB,OACxD,GAAIve,EAAOyX,WAAajX,EAAOkX,+BAC7B,QAEG1X,EAAOyX,WAAajX,EAAO4M,SAAW5M,EAAOgK,MAChDxK,EAAOsZ,UAET,IAAIkF,EAAWzG,EAAE1d,OACjB,GAAiC,YAA7BmG,EAAOie,oBD59Db,SAA0B/e,EAAIgf,GAC5B,MAAMvgB,EAAS,IACf,IAAIwgB,EAAUD,EAAOhW,SAAShJ,GAQ9B,OAPKif,GAAWxgB,EAAO4D,iBAAmB2c,aAAkB3c,kBAE1D4c,EADiB,IAAID,EAAOzc,oBACT4D,SAASnG,GACvBif,IACHA,EAlBN,SAA8Bjf,EAAIkf,GAEhC,MAAMC,EAAgB,CAACD,GACvB,KAAOC,EAAcjkB,OAAS,GAAG,CAC/B,MAAMkkB,EAAiBD,EAAc7B,QACrC,GAAItd,IAAOof,EACT,OAAO,EAETD,EAAc7c,QAAQ8c,EAAepjB,YAAcojB,EAAe1V,WAAa0V,EAAe1V,WAAW1N,SAAW,MAASojB,EAAe7c,iBAAmB6c,EAAe7c,mBAAqB,GACrM,CACF,CAQgB8c,CAAqBrf,EAAIgf,KAGhCC,CACT,CCk9DSK,CAAiBR,EAAUxe,EAAOU,WAAY,OAErD,GAAI,UAAWqX,GAAiB,IAAZA,EAAEkH,MAAa,OACnC,GAAI,WAAYlH,GAAKA,EAAEmH,OAAS,EAAG,OACnC,GAAIjX,EAAKkX,WAAalX,EAAKmX,QAAS,OAGpC,MAAMC,IAAyB7e,EAAO8e,gBAA4C,KAA1B9e,EAAO8e,eAEzDC,EAAYxH,EAAEyH,aAAezH,EAAEyH,eAAiBzH,EAAEvC,KACpD6J,GAAwBtH,EAAE1d,QAAU0d,EAAE1d,OAAO+O,YAAcmW,IAC7Df,EAAWe,EAAU,IAEvB,MAAME,EAAoBjf,EAAOif,kBAAoBjf,EAAOif,kBAAoB,IAAIjf,EAAO8e,iBACrFI,KAAoB3H,EAAE1d,SAAU0d,EAAE1d,OAAO+O,YAG/C,GAAI5I,EAAOmf,YAAcD,EAlF3B,SAAwB5d,EAAU8d,GAahC,YAZa,IAATA,IACFA,EAAOviB,MAET,SAASwiB,EAAcngB,GACrB,IAAKA,GAAMA,IAAO,KAAiBA,IAAO,IAAa,OAAO,KAC1DA,EAAGogB,eAAcpgB,EAAKA,EAAGogB,cAC7B,MAAMC,EAAQrgB,EAAGqJ,QAAQjH,GACzB,OAAKie,GAAUrgB,EAAGsgB,YAGXD,GAASF,EAAcngB,EAAGsgB,cAAc7jB,MAFtC,IAGX,CACO0jB,CAAcD,EACvB,CAoE4CK,CAAeR,EAAmBjB,GAAYA,EAASzV,QAAQ0W,IAEvG,YADAzf,EAAOkgB,YAAa,GAGtB,GAAI1f,EAAO2f,eACJ3B,EAASzV,QAAQvI,EAAO2f,cAAe,OAE9C9B,EAAQ+B,SAAWrI,EAAEqG,MACrBC,EAAQgC,SAAWtI,EAAEuI,MACrB,MAAM9C,EAASa,EAAQ+B,SACjBG,EAASlC,EAAQgC,SAIvB,IAAK9C,EAAiBvd,EAAQ+X,EAAGyF,GAC/B,OAEFlkB,OAAO4R,OAAOjD,EAAM,CAClBkX,WAAW,EACXC,SAAS,EACToB,qBAAqB,EACrBC,iBAAa1hB,EACb2hB,iBAAa3hB,IAEfsf,EAAQb,OAASA,EACjBa,EAAQkC,OAASA,EACjBtY,EAAK0Y,eAAiBriB,IACtB0B,EAAOkgB,YAAa,EACpBlgB,EAAO2K,aACP3K,EAAO4gB,oBAAiB7hB,EACpByB,EAAOma,UAAY,IAAG1S,EAAK4Y,oBAAqB,GACpD,IAAIjD,GAAiB,EACjBY,EAAStc,QAAQ+F,EAAK6Y,qBACxBlD,GAAiB,EACS,WAAtBY,EAASrjB,WACX8M,EAAKkX,WAAY,IAGjBxiB,EAAS1B,eAAiB0B,EAAS1B,cAAciH,QAAQ+F,EAAK6Y,oBAAsBnkB,EAAS1B,gBAAkBujB,IAA+B,UAAlBzG,EAAEwG,aAA6C,UAAlBxG,EAAEwG,cAA4BC,EAAStc,QAAQ+F,EAAK6Y,qBAC/MnkB,EAAS1B,cAAcC,OAEzB,MAAM6lB,EAAuBnD,GAAkB5d,EAAOghB,gBAAkBxgB,EAAOygB,0BAC1EzgB,EAAO0gB,gCAAiCH,GAA0BvC,EAAS2C,mBAC9EpJ,EAAE6F,iBAEApd,EAAO4Z,UAAY5Z,EAAO4Z,SAAStO,SAAW9L,EAAOoa,UAAYpa,EAAOyX,YAAcjX,EAAO4M,SAC/FpN,EAAOoa,SAASyD,eAElB7d,EAAOgI,KAAK,aAAc+P,EAC5B,CAEA,SAASqJ,EAAYna,GACnB,MAAMtK,EAAW,IACXqD,EAAS3C,KACT4K,EAAOjI,EAAO8c,iBACd,OACJtc,EAAM,QACN6d,EACA5S,aAAcC,EAAG,QACjBI,GACE9L,EACJ,IAAK8L,EAAS,OACd,IAAKtL,EAAO8d,eAAuC,UAAtBrX,EAAMsX,YAAyB,OAC5D,IAOI8C,EAPAtJ,EAAI9Q,EAER,GADI8Q,EAAE+F,gBAAe/F,EAAIA,EAAE+F,eACZ,gBAAX/F,EAAEgG,KAAwB,CAC5B,GAAqB,OAAjB9V,EAAKiW,QAAkB,OAE3B,GADWnG,EAAEiG,YACF/V,EAAK+V,UAAW,MAC7B,CAEA,GAAe,cAAXjG,EAAEgG,MAEJ,GADAsD,EAAc,IAAItJ,EAAEuJ,gBAAgB3N,KAAKiF,GAAKA,EAAEuF,aAAelW,EAAKiW,UAC/DmD,GAAeA,EAAYlD,aAAelW,EAAKiW,QAAS,YAE7DmD,EAActJ,EAEhB,IAAK9P,EAAKkX,UAIR,YAHIlX,EAAKyY,aAAezY,EAAKwY,aAC3BzgB,EAAOgI,KAAK,oBAAqB+P,IAIrC,MAAMqG,EAAQiD,EAAYjD,MACpBkC,EAAQe,EAAYf,MAC1B,GAAIvI,EAAEwJ,wBAGJ,OAFAlD,EAAQb,OAASY,OACjBC,EAAQkC,OAASD,GAGnB,IAAKtgB,EAAOghB,eAaV,OAZKjJ,EAAE1d,OAAO6H,QAAQ+F,EAAK6Y,qBACzB9gB,EAAOkgB,YAAa,QAElBjY,EAAKkX,YACP7lB,OAAO4R,OAAOmT,EAAS,CACrBb,OAAQY,EACRmC,OAAQD,EACRF,SAAUhC,EACViC,SAAUC,IAEZrY,EAAK0Y,eAAiBriB,MAI1B,GAAIkC,EAAOghB,sBAAwBhhB,EAAOgK,KACxC,GAAIxK,EAAO+K,cAET,GAAIuV,EAAQjC,EAAQkC,QAAUvgB,EAAOI,WAAaJ,EAAOuS,gBAAkB+N,EAAQjC,EAAQkC,QAAUvgB,EAAOI,WAAaJ,EAAO2R,eAG9H,OAFA1J,EAAKkX,WAAY,OACjBlX,EAAKmX,SAAU,OAGZ,IAAI1T,IAAQ0S,EAAQC,EAAQb,SAAWxd,EAAOI,WAAaJ,EAAOuS,gBAAkB6L,EAAQC,EAAQb,SAAWxd,EAAOI,WAAaJ,EAAO2R,gBAC/I,OACK,IAAKjG,IAAQ0S,EAAQC,EAAQb,QAAUxd,EAAOI,WAAaJ,EAAOuS,gBAAkB6L,EAAQC,EAAQb,QAAUxd,EAAOI,WAAaJ,EAAO2R,gBAC9I,MACF,CAKF,GAHIhV,EAAS1B,eAAiB0B,EAAS1B,cAAciH,QAAQ+F,EAAK6Y,oBAAsBnkB,EAAS1B,gBAAkB8c,EAAE1d,QAA4B,UAAlB0d,EAAEwG,aAC/H5hB,EAAS1B,cAAcC,OAErByB,EAAS1B,eACP8c,EAAE1d,SAAWsC,EAAS1B,eAAiB8c,EAAE1d,OAAO6H,QAAQ+F,EAAK6Y,mBAG/D,OAFA7Y,EAAKmX,SAAU,OACfpf,EAAOkgB,YAAa,GAIpBjY,EAAKuY,qBACPxgB,EAAOgI,KAAK,YAAa+P,GAE3BsG,EAAQoD,UAAYpD,EAAQ+B,SAC5B/B,EAAQqD,UAAYrD,EAAQgC,SAC5BhC,EAAQ+B,SAAWhC,EACnBC,EAAQgC,SAAWC,EACnB,MAAMqB,EAAQtD,EAAQ+B,SAAW/B,EAAQb,OACnCoE,EAAQvD,EAAQgC,SAAWhC,EAAQkC,OACzC,GAAIvgB,EAAOQ,OAAOma,WAAaxZ,KAAK0gB,KAAKF,GAAS,EAAIC,GAAS,GAAK5hB,EAAOQ,OAAOma,UAAW,OAC7F,QAAgC,IAArB1S,EAAKwY,YAA6B,CAC3C,IAAIqB,EACA9hB,EAAO8K,gBAAkBuT,EAAQgC,WAAahC,EAAQkC,QAAUvgB,EAAO+K,cAAgBsT,EAAQ+B,WAAa/B,EAAQb,OACtHvV,EAAKwY,aAAc,EAGfkB,EAAQA,EAAQC,EAAQA,GAAS,KACnCE,EAA4D,IAA/C3gB,KAAK4gB,MAAM5gB,KAAKqN,IAAIoT,GAAQzgB,KAAKqN,IAAImT,IAAgBxgB,KAAKK,GACvEyG,EAAKwY,YAAczgB,EAAO8K,eAAiBgX,EAAathB,EAAOshB,WAAa,GAAKA,EAAathB,EAAOshB,WAG3G,CASA,GARI7Z,EAAKwY,aACPzgB,EAAOgI,KAAK,oBAAqB+P,QAEH,IAArB9P,EAAKyY,cACVrC,EAAQ+B,WAAa/B,EAAQb,QAAUa,EAAQgC,WAAahC,EAAQkC,SACtEtY,EAAKyY,aAAc,IAGnBzY,EAAKwY,aAA0B,cAAX1I,EAAEgG,MAAwB9V,EAAK+Z,gCAErD,YADA/Z,EAAKkX,WAAY,GAGnB,IAAKlX,EAAKyY,YACR,OAEF1gB,EAAOkgB,YAAa,GACf1f,EAAO4M,SAAW2K,EAAEkK,YACvBlK,EAAE6F,iBAEApd,EAAO0hB,2BAA6B1hB,EAAO2hB,QAC7CpK,EAAEqK,kBAEJ,IAAIvF,EAAO7c,EAAO8K,eAAiB6W,EAAQC,EACvCS,EAAcriB,EAAO8K,eAAiBuT,EAAQ+B,SAAW/B,EAAQoD,UAAYpD,EAAQgC,SAAWhC,EAAQqD,UACxGlhB,EAAO8hB,iBACTzF,EAAO1b,KAAKqN,IAAIqO,IAASnR,EAAM,GAAK,GACpC2W,EAAclhB,KAAKqN,IAAI6T,IAAgB3W,EAAM,GAAK,IAEpD2S,EAAQxB,KAAOA,EACfA,GAAQrc,EAAO+hB,WACX7W,IACFmR,GAAQA,EACRwF,GAAeA,GAEjB,MAAMG,EAAuBxiB,EAAOyiB,iBACpCziB,EAAO4gB,eAAiB/D,EAAO,EAAI,OAAS,OAC5C7c,EAAOyiB,iBAAmBJ,EAAc,EAAI,OAAS,OACrD,MAAMK,EAAS1iB,EAAOQ,OAAOgK,OAAShK,EAAO4M,QACvCuV,EAA2C,SAA5B3iB,EAAOyiB,kBAA+BziB,EAAOwY,gBAA8C,SAA5BxY,EAAOyiB,kBAA+BziB,EAAOyY,eACjI,IAAKxQ,EAAKmX,QAAS,CAQjB,GAPIsD,GAAUC,GACZ3iB,EAAOsZ,QAAQ,CACbrB,UAAWjY,EAAO4gB,iBAGtB3Y,EAAK8U,eAAiB/c,EAAO8V,eAC7B9V,EAAO4Q,cAAc,GACjB5Q,EAAOyX,UAAW,CACpB,MAAMmL,EAAM,IAAIzkB,OAAOf,YAAY,gBAAiB,CAClDylB,SAAS,EACTZ,YAAY,EACZa,OAAQ,CACNC,mBAAmB,KAGvB/iB,EAAOU,UAAUsiB,cAAcJ,EACjC,CACA3a,EAAKgb,qBAAsB,GAEvBziB,EAAO0iB,aAAyC,IAA1BljB,EAAOwY,iBAAqD,IAA1BxY,EAAOyY,gBACjEzY,EAAOmjB,eAAc,GAEvBnjB,EAAOgI,KAAK,kBAAmB+P,EACjC,CAGA,IADA,IAAIta,MAAOwD,WACmB,IAA1BT,EAAO4iB,gBAA4Bnb,EAAKmX,SAAWnX,EAAK4Y,oBAAsB2B,IAAyBxiB,EAAOyiB,kBAAoBC,GAAUC,GAAgBxhB,KAAKqN,IAAIqO,IAAS,EAUhL,OATAvjB,OAAO4R,OAAOmT,EAAS,CACrBb,OAAQY,EACRmC,OAAQD,EACRF,SAAUhC,EACViC,SAAUC,EACVvD,eAAgB9U,EAAKgO,mBAEvBhO,EAAKob,eAAgB,OACrBpb,EAAK8U,eAAiB9U,EAAKgO,kBAG7BjW,EAAOgI,KAAK,aAAc+P,GAC1B9P,EAAKmX,SAAU,EACfnX,EAAKgO,iBAAmB4G,EAAO5U,EAAK8U,eACpC,IAAIuG,GAAsB,EACtBC,EAAkB/iB,EAAO+iB,gBAiD7B,GAhDI/iB,EAAOghB,sBACT+B,EAAkB,GAEhB1G,EAAO,GACL6F,GAAUC,GAA8B1a,EAAK4Y,oBAAsB5Y,EAAKgO,kBAAoBzV,EAAO2M,eAAiBnN,EAAO2R,eAAiB3R,EAAOmM,gBAAgBnM,EAAO8J,YAAc,IAA+B,SAAzBtJ,EAAOmJ,eAA4B3J,EAAOsJ,OAAO1O,OAAS4F,EAAOmJ,eAAiB,EAAI3J,EAAOmM,gBAAgBnM,EAAO8J,YAAc,GAAK9J,EAAOQ,OAAOkM,aAAe,GAAK1M,EAAOQ,OAAOkM,aAAe1M,EAAO2R,iBAC7Y3R,EAAOsZ,QAAQ,CACbrB,UAAW,OACXlB,cAAc,EACd5B,iBAAkB,IAGlBlN,EAAKgO,iBAAmBjW,EAAO2R,iBACjC2R,GAAsB,EAClB9iB,EAAOgjB,aACTvb,EAAKgO,iBAAmBjW,EAAO2R,eAAiB,IAAM3R,EAAO2R,eAAiB1J,EAAK8U,eAAiBF,IAAS0G,KAGxG1G,EAAO,IACZ6F,GAAUC,GAA8B1a,EAAK4Y,oBAAsB5Y,EAAKgO,kBAAoBzV,EAAO2M,eAAiBnN,EAAOuS,eAAiBvS,EAAOmM,gBAAgBnM,EAAOmM,gBAAgBvR,OAAS,GAAKoF,EAAOQ,OAAOkM,cAAyC,SAAzBlM,EAAOmJ,eAA4B3J,EAAOsJ,OAAO1O,OAAS4F,EAAOmJ,eAAiB,EAAI3J,EAAOmM,gBAAgBnM,EAAOmM,gBAAgBvR,OAAS,GAAKoF,EAAOQ,OAAOkM,aAAe,GAAK1M,EAAOuS,iBACnavS,EAAOsZ,QAAQ,CACbrB,UAAW,OACXlB,cAAc,EACd5B,iBAAkBnV,EAAOsJ,OAAO1O,QAAmC,SAAzB4F,EAAOmJ,cAA2B3J,EAAO4J,uBAAyBzI,KAAK0I,KAAKpG,WAAWjD,EAAOmJ,cAAe,QAGvJ1B,EAAKgO,iBAAmBjW,EAAOuS,iBACjC+Q,GAAsB,EAClB9iB,EAAOgjB,aACTvb,EAAKgO,iBAAmBjW,EAAOuS,eAAiB,GAAKvS,EAAOuS,eAAiBtK,EAAK8U,eAAiBF,IAAS0G,KAI9GD,IACFvL,EAAEwJ,yBAA0B,IAIzBvhB,EAAOwY,gBAA4C,SAA1BxY,EAAO4gB,gBAA6B3Y,EAAKgO,iBAAmBhO,EAAK8U,iBAC7F9U,EAAKgO,iBAAmBhO,EAAK8U,iBAE1B/c,EAAOyY,gBAA4C,SAA1BzY,EAAO4gB,gBAA6B3Y,EAAKgO,iBAAmBhO,EAAK8U,iBAC7F9U,EAAKgO,iBAAmBhO,EAAK8U,gBAE1B/c,EAAOyY,gBAAmBzY,EAAOwY,iBACpCvQ,EAAKgO,iBAAmBhO,EAAK8U,gBAI3Bvc,EAAOma,UAAY,EAAG,CACxB,KAAIxZ,KAAKqN,IAAIqO,GAAQrc,EAAOma,WAAa1S,EAAK4Y,oBAW5C,YADA5Y,EAAKgO,iBAAmBhO,EAAK8U,gBAT7B,IAAK9U,EAAK4Y,mBAMR,OALA5Y,EAAK4Y,oBAAqB,EAC1BxC,EAAQb,OAASa,EAAQ+B,SACzB/B,EAAQkC,OAASlC,EAAQgC,SACzBpY,EAAKgO,iBAAmBhO,EAAK8U,oBAC7BsB,EAAQxB,KAAO7c,EAAO8K,eAAiBuT,EAAQ+B,SAAW/B,EAAQb,OAASa,EAAQgC,SAAWhC,EAAQkC,OAO5G,CACK/f,EAAOijB,eAAgBjjB,EAAO4M,WAG/B5M,EAAO4Z,UAAY5Z,EAAO4Z,SAAStO,SAAW9L,EAAOoa,UAAY5Z,EAAO2P,uBAC1EnQ,EAAOwU,oBACPxU,EAAOsT,uBAEL9S,EAAO4Z,UAAY5Z,EAAO4Z,SAAStO,SAAW9L,EAAOoa,UACvDpa,EAAOoa,SAASgH,cAGlBphB,EAAOoS,eAAenK,EAAKgO,kBAE3BjW,EAAO+W,aAAa9O,EAAKgO,kBAC3B,CAEA,SAASyN,EAAWzc,GAClB,MAAMjH,EAAS3C,KACT4K,EAAOjI,EAAO8c,gBACpB,IAEIuE,EAFAtJ,EAAI9Q,EAIR,GAHI8Q,EAAE+F,gBAAe/F,EAAIA,EAAE+F,eAEK,aAAX/F,EAAEgG,MAAkC,gBAAXhG,EAAEgG,MAO9C,GADAsD,EAAc,IAAItJ,EAAEuJ,gBAAgB3N,KAAKiF,GAAKA,EAAEuF,aAAelW,EAAKiW,UAC/DmD,GAAeA,EAAYlD,aAAelW,EAAKiW,QAAS,WAN5C,CACjB,GAAqB,OAAjBjW,EAAKiW,QAAkB,OAC3B,GAAInG,EAAEiG,YAAc/V,EAAK+V,UAAW,OACpCqD,EAActJ,CAChB,CAIA,GAAI,CAAC,gBAAiB,aAAc,eAAgB,eAAelS,SAASkS,EAAEgG,SAC5D,CAAC,gBAAiB,eAAelY,SAASkS,EAAEgG,QAAU/d,EAAO6D,QAAQ6B,WAAY1F,EAAO6D,QAAQsC,WAE9G,OAGJ8B,EAAK+V,UAAY,KACjB/V,EAAKiW,QAAU,KACf,MAAM,OACJ1d,EAAM,QACN6d,EACA5S,aAAcC,EAAG,WACjBQ,EAAU,QACVJ,GACE9L,EACJ,IAAK8L,EAAS,OACd,IAAKtL,EAAO8d,eAAmC,UAAlBvG,EAAEwG,YAAyB,OAKxD,GAJItW,EAAKuY,qBACPxgB,EAAOgI,KAAK,WAAY+P,GAE1B9P,EAAKuY,qBAAsB,GACtBvY,EAAKkX,UAMR,OALIlX,EAAKmX,SAAW5e,EAAO0iB,YACzBljB,EAAOmjB,eAAc,GAEvBlb,EAAKmX,SAAU,OACfnX,EAAKyY,aAAc,GAKjBlgB,EAAO0iB,YAAcjb,EAAKmX,SAAWnX,EAAKkX,aAAwC,IAA1Bnf,EAAOwY,iBAAqD,IAA1BxY,EAAOyY,iBACnGzY,EAAOmjB,eAAc,GAIvB,MAAMQ,EAAerlB,IACfslB,EAAWD,EAAe1b,EAAK0Y,eAGrC,GAAI3gB,EAAOkgB,WAAY,CACrB,MAAM2D,EAAW9L,EAAEvC,MAAQuC,EAAEyH,cAAgBzH,EAAEyH,eAC/Cxf,EAAOuV,mBAAmBsO,GAAYA,EAAS,IAAM9L,EAAE1d,OAAQwpB,GAC/D7jB,EAAOgI,KAAK,YAAa+P,GACrB6L,EAAW,KAAOD,EAAe1b,EAAK6b,cAAgB,KACxD9jB,EAAOgI,KAAK,wBAAyB+P,EAEzC,CAKA,GAJA9P,EAAK6b,cAAgBxlB,IACrBF,EAAS,KACF4B,EAAO+G,YAAW/G,EAAOkgB,YAAa,MAExCjY,EAAKkX,YAAclX,EAAKmX,UAAYpf,EAAO4gB,gBAAmC,IAAjBvC,EAAQxB,OAAe5U,EAAKob,eAAiBpb,EAAKgO,mBAAqBhO,EAAK8U,iBAAmB9U,EAAKob,cAIpK,OAHApb,EAAKkX,WAAY,EACjBlX,EAAKmX,SAAU,OACfnX,EAAKyY,aAAc,GAMrB,IAAIqD,EAMJ,GATA9b,EAAKkX,WAAY,EACjBlX,EAAKmX,SAAU,EACfnX,EAAKyY,aAAc,EAGjBqD,EADEvjB,EAAOijB,aACI/X,EAAM1L,EAAOI,WAAaJ,EAAOI,WAEhC6H,EAAKgO,iBAEjBzV,EAAO4M,QACT,OAEF,GAAI5M,EAAO4Z,UAAY5Z,EAAO4Z,SAAStO,QAIrC,YAHA9L,EAAOoa,SAASsJ,WAAW,CACzBK,eAMJ,MAAMC,EAAcD,IAAe/jB,EAAOuS,iBAAmBvS,EAAOQ,OAAOgK,KAC3E,IAAIyZ,EAAY,EACZ9U,EAAYnP,EAAOmM,gBAAgB,GACvC,IAAK,IAAInN,EAAI,EAAGA,EAAIkN,EAAWtR,OAAQoE,GAAKA,EAAIwB,EAAOkO,mBAAqB,EAAIlO,EAAOiO,eAAgB,CACrG,MAAMkL,EAAY3a,EAAIwB,EAAOkO,mBAAqB,EAAI,EAAIlO,EAAOiO,oBACxB,IAA9BvC,EAAWlN,EAAI2a,IACpBqK,GAAeD,GAAc7X,EAAWlN,IAAM+kB,EAAa7X,EAAWlN,EAAI2a,MAC5EsK,EAAYjlB,EACZmQ,EAAYjD,EAAWlN,EAAI2a,GAAazN,EAAWlN,KAE5CglB,GAAeD,GAAc7X,EAAWlN,MACjDilB,EAAYjlB,EACZmQ,EAAYjD,EAAWA,EAAWtR,OAAS,GAAKsR,EAAWA,EAAWtR,OAAS,GAEnF,CACA,IAAIspB,EAAmB,KACnBC,EAAkB,KAClB3jB,EAAO+J,SACLvK,EAAOwS,YACT2R,EAAkB3jB,EAAOqL,SAAWrL,EAAOqL,QAAQC,SAAW9L,EAAO6L,QAAU7L,EAAO6L,QAAQvC,OAAO1O,OAAS,EAAIoF,EAAOsJ,OAAO1O,OAAS,EAChIoF,EAAOyS,QAChByR,EAAmB,IAIvB,MAAME,GAASL,EAAa7X,EAAW+X,IAAc9U,EAC/CwK,EAAYsK,EAAYzjB,EAAOkO,mBAAqB,EAAI,EAAIlO,EAAOiO,eACzE,GAAImV,EAAWpjB,EAAO6jB,aAAc,CAElC,IAAK7jB,EAAO8jB,WAEV,YADAtkB,EAAOmY,QAAQnY,EAAO8J,aAGM,SAA1B9J,EAAO4gB,iBACLwD,GAAS5jB,EAAO+jB,gBAAiBvkB,EAAOmY,QAAQ3X,EAAO+J,QAAUvK,EAAOyS,MAAQyR,EAAmBD,EAAYtK,GAAgB3Z,EAAOmY,QAAQ8L,IAEtH,SAA1BjkB,EAAO4gB,iBACLwD,EAAQ,EAAI5jB,EAAO+jB,gBACrBvkB,EAAOmY,QAAQ8L,EAAYtK,GACE,OAApBwK,GAA4BC,EAAQ,GAAKjjB,KAAKqN,IAAI4V,GAAS5jB,EAAO+jB,gBAC3EvkB,EAAOmY,QAAQgM,GAEfnkB,EAAOmY,QAAQ8L,GAGrB,KAAO,CAEL,IAAKzjB,EAAOgkB,YAEV,YADAxkB,EAAOmY,QAAQnY,EAAO8J,cAGE9J,EAAOykB,YAAe1M,EAAE1d,SAAW2F,EAAOykB,WAAWC,QAAU3M,EAAE1d,SAAW2F,EAAOykB,WAAWE,QAExF,SAA1B3kB,EAAO4gB,gBACT5gB,EAAOmY,QAA6B,OAArB+L,EAA4BA,EAAmBD,EAAYtK,GAE9C,SAA1B3Z,EAAO4gB,gBACT5gB,EAAOmY,QAA4B,OAApBgM,EAA2BA,EAAkBF,IAErDlM,EAAE1d,SAAW2F,EAAOykB,WAAWC,OACxC1kB,EAAOmY,QAAQ8L,EAAYtK,GAE3B3Z,EAAOmY,QAAQ8L,EAEnB,CACF,CAEA,SAASW,IACP,MAAM5kB,EAAS3C,MACT,OACJmD,EAAM,GACNd,GACEM,EACJ,GAAIN,GAAyB,IAAnBA,EAAGgE,YAAmB,OAG5BlD,EAAOkN,aACT1N,EAAO6kB,gBAIT,MAAM,eACJrM,EAAc,eACdC,EAAc,SACdxM,GACEjM,EACE4L,EAAY5L,EAAO6L,SAAW7L,EAAOQ,OAAOqL,QAAQC,QAG1D9L,EAAOwY,gBAAiB,EACxBxY,EAAOyY,gBAAiB,EACxBzY,EAAO2K,aACP3K,EAAOmL,eACPnL,EAAOsT,sBACP,MAAMwR,EAAgBlZ,GAAapL,EAAOgK,OACZ,SAAzBhK,EAAOmJ,eAA4BnJ,EAAOmJ,cAAgB,KAAM3J,EAAOyS,OAAUzS,EAAOwS,aAAgBxS,EAAOQ,OAAO2M,gBAAmB2X,EAGxI9kB,EAAOQ,OAAOgK,OAASoB,EACzB5L,EAAOiZ,YAAYjZ,EAAOyK,UAAW,GAAG,GAAO,GAE/CzK,EAAOmY,QAAQnY,EAAO8J,YAAa,GAAG,GAAO,GAL/C9J,EAAOmY,QAAQnY,EAAOsJ,OAAO1O,OAAS,EAAG,GAAG,GAAO,GAQjDoF,EAAO+kB,UAAY/kB,EAAO+kB,SAASC,SAAWhlB,EAAO+kB,SAASE,SAChErnB,aAAaoC,EAAO+kB,SAASG,eAC7BllB,EAAO+kB,SAASG,cAAgBvnB,WAAW,KACrCqC,EAAO+kB,UAAY/kB,EAAO+kB,SAASC,SAAWhlB,EAAO+kB,SAASE,QAChEjlB,EAAO+kB,SAASI,UAEjB,MAGLnlB,EAAOyY,eAAiBA,EACxBzY,EAAOwY,eAAiBA,EACpBxY,EAAOQ,OAAOyP,eAAiBhE,IAAajM,EAAOiM,UACrDjM,EAAOkQ,eAEX,CAEA,SAASkV,EAAQrN,GACf,MAAM/X,EAAS3C,KACV2C,EAAO8L,UACP9L,EAAOkgB,aACNlgB,EAAOQ,OAAO6kB,eAAetN,EAAE6F,iBAC/B5d,EAAOQ,OAAO8kB,0BAA4BtlB,EAAOyX,YACnDM,EAAEqK,kBACFrK,EAAEwN,6BAGR,CAEA,SAASC,IACP,MAAMxlB,EAAS3C,MACT,UACJqD,EAAS,aACT+K,EAAY,QACZK,GACE9L,EACJ,IAAK8L,EAAS,OAWd,IAAImL,EAVJjX,EAAOoX,kBAAoBpX,EAAOI,UAC9BJ,EAAO8K,eACT9K,EAAOI,WAAaM,EAAU+kB,WAE9BzlB,EAAOI,WAAaM,EAAUglB,UAGP,IAArB1lB,EAAOI,YAAiBJ,EAAOI,UAAY,GAC/CJ,EAAOwU,oBACPxU,EAAOsT,sBAEP,MAAMhB,EAAiBtS,EAAOuS,eAAiBvS,EAAO2R,eAEpDsF,EADqB,IAAnB3E,EACY,GAECtS,EAAOI,UAAYJ,EAAO2R,gBAAkBW,EAEzD2E,IAAgBjX,EAAOkB,UACzBlB,EAAOoS,eAAe3G,GAAgBzL,EAAOI,UAAYJ,EAAOI,WAElEJ,EAAOgI,KAAK,eAAgBhI,EAAOI,WAAW,EAChD,CAEA,SAASulB,EAAO5N,GACd,MAAM/X,EAAS3C,KACfwL,EAAqB7I,EAAQ+X,EAAE1d,QAC3B2F,EAAOQ,OAAO4M,SAA2C,SAAhCpN,EAAOQ,OAAOmJ,gBAA6B3J,EAAOQ,OAAO6S,YAGtFrT,EAAO0K,QACT,CAEA,SAASkb,IACP,MAAM5lB,EAAS3C,KACX2C,EAAO6lB,gCACX7lB,EAAO6lB,+BAAgC,EACnC7lB,EAAOQ,OAAOghB,sBAChBxhB,EAAON,GAAG9D,MAAMkqB,YAAc,QAElC,CAEA,MAAMpf,EAAS,CAAC1G,EAAQgH,KACtB,MAAMrK,EAAW,KACX,OACJ6D,EAAM,GACNd,EAAE,UACFgB,EAAS,OACT+D,GACEzE,EACE+lB,IAAYvlB,EAAO2hB,OACnB6D,EAAuB,OAAXhf,EAAkB,mBAAqB,sBACnDif,EAAejf,EAChBtH,GAAoB,iBAAPA,IAGlB/C,EAASqpB,GAAW,aAAchmB,EAAO4lB,qBAAsB,CAC7DM,SAAS,EACTH,YAEFrmB,EAAGsmB,GAAW,aAAchmB,EAAO6d,aAAc,CAC/CqI,SAAS,IAEXxmB,EAAGsmB,GAAW,cAAehmB,EAAO6d,aAAc,CAChDqI,SAAS,IAEXvpB,EAASqpB,GAAW,YAAahmB,EAAOohB,YAAa,CACnD8E,SAAS,EACTH,YAEFppB,EAASqpB,GAAW,cAAehmB,EAAOohB,YAAa,CACrD8E,SAAS,EACTH,YAEFppB,EAASqpB,GAAW,WAAYhmB,EAAO0jB,WAAY,CACjDwC,SAAS,IAEXvpB,EAASqpB,GAAW,YAAahmB,EAAO0jB,WAAY,CAClDwC,SAAS,IAEXvpB,EAASqpB,GAAW,gBAAiBhmB,EAAO0jB,WAAY,CACtDwC,SAAS,IAEXvpB,EAASqpB,GAAW,cAAehmB,EAAO0jB,WAAY,CACpDwC,SAAS,IAEXvpB,EAASqpB,GAAW,aAAchmB,EAAO0jB,WAAY,CACnDwC,SAAS,IAEXvpB,EAASqpB,GAAW,eAAgBhmB,EAAO0jB,WAAY,CACrDwC,SAAS,IAEXvpB,EAASqpB,GAAW,cAAehmB,EAAO0jB,WAAY,CACpDwC,SAAS,KAIP1lB,EAAO6kB,eAAiB7kB,EAAO8kB,2BACjC5lB,EAAGsmB,GAAW,QAAShmB,EAAOolB,SAAS,GAErC5kB,EAAO4M,SACT1M,EAAUslB,GAAW,SAAUhmB,EAAOwlB,UAIpChlB,EAAO2lB,qBACTnmB,EAAOimB,GAAcxhB,EAAOC,KAAOD,EAAOE,QAAU,0CAA4C,wBAAyBigB,GAAU,GAEnI5kB,EAAOimB,GAAc,iBAAkBrB,GAAU,GAInDllB,EAAGsmB,GAAW,OAAQhmB,EAAO2lB,OAAQ,CACnCI,SAAS,MA4BPK,EAAgB,CAACpmB,EAAQQ,IACtBR,EAAO+J,MAAQvJ,EAAOuJ,MAAQvJ,EAAOuJ,KAAKC,KAAO,EAiI1D,IAAI0D,GAAc,CAChBmX,cAhIF,WACE,MAAM7kB,EAAS3C,MACT,UACJoN,EAAS,YACT4K,EAAW,OACX7U,EAAM,GACNd,GACEM,EACE0N,EAAclN,EAAOkN,YAC3B,IAAKA,GAAeA,GAAmD,IAApCpU,OAAOkB,KAAKkT,GAAa9S,OAAc,OAC1E,MAAM+B,EAAW,IAGX0pB,EAA6C,WAA3B7lB,EAAO6lB,iBAAiC7lB,EAAO6lB,gBAA2C,YAAzB7lB,EAAO6lB,gBAC1FC,EAAsB,CAAC,SAAU,aAAazgB,SAASrF,EAAO6lB,mBAAqB7lB,EAAO6lB,gBAAkBrmB,EAAON,GAAK/C,EAASvB,cAAcoF,EAAO6lB,iBACtJE,EAAavmB,EAAOwmB,cAAc9Y,EAAa2Y,EAAiBC,GACtE,IAAKC,GAAcvmB,EAAOymB,oBAAsBF,EAAY,OAC5D,MACMG,GADuBH,KAAc7Y,EAAcA,EAAY6Y,QAAcxnB,IAClCiB,EAAO2mB,eAClDC,EAAcR,EAAcpmB,EAAQQ,GACpCqmB,EAAaT,EAAcpmB,EAAQ0mB,GACnCI,EAAgB9mB,EAAOQ,OAAO0iB,WAC9B6D,EAAeL,EAAiBxD,WAChC8D,EAAaxmB,EAAOsL,QACtB8a,IAAgBC,GAClBnnB,EAAGgD,UAAUiG,OAAO,GAAGnI,EAAO8P,6BAA8B,GAAG9P,EAAO8P,qCACtEtQ,EAAOinB,yBACGL,GAAeC,IACzBnnB,EAAGgD,UAAUC,IAAI,GAAGnC,EAAO8P,+BACvBoW,EAAiB3c,KAAKgS,MAAuC,WAA/B2K,EAAiB3c,KAAKgS,OAAsB2K,EAAiB3c,KAAKgS,MAA6B,WAArBvb,EAAOuJ,KAAKgS,OACtHrc,EAAGgD,UAAUC,IAAI,GAAGnC,EAAO8P,qCAE7BtQ,EAAOinB,wBAELH,IAAkBC,EACpB/mB,EAAOknB,mBACGJ,GAAiBC,GAC3B/mB,EAAOmjB,gBAIT,CAAC,aAAc,aAAc,aAAaxoB,QAAQhB,IAChD,QAAsC,IAA3B+sB,EAAiB/sB,GAAuB,OACnD,MAAMwtB,EAAmB3mB,EAAO7G,IAAS6G,EAAO7G,GAAMmS,QAChDsb,EAAkBV,EAAiB/sB,IAAS+sB,EAAiB/sB,GAAMmS,QACrEqb,IAAqBC,GACvBpnB,EAAOrG,GAAM0tB,WAEVF,GAAoBC,GACvBpnB,EAAOrG,GAAM2tB,WAGjB,MAAMC,EAAmBb,EAAiBzO,WAAayO,EAAiBzO,YAAczX,EAAOyX,UACvFuP,EAAchnB,EAAOgK,OAASkc,EAAiB/c,gBAAkBnJ,EAAOmJ,eAAiB4d,GACzFE,EAAUjnB,EAAOgK,KACnB+c,GAAoBlS,GACtBrV,EAAO0nB,kBAET,EAAO1nB,EAAOQ,OAAQkmB,GACtB,MAAMiB,EAAY3nB,EAAOQ,OAAOsL,QAC1B8b,EAAU5nB,EAAOQ,OAAOgK,KAC9BlR,OAAO4R,OAAOlL,EAAQ,CACpBghB,eAAgBhhB,EAAOQ,OAAOwgB,eAC9BxI,eAAgBxY,EAAOQ,OAAOgY,eAC9BC,eAAgBzY,EAAOQ,OAAOiY,iBAE5BuO,IAAeW,EACjB3nB,EAAOqnB,WACGL,GAAcW,GACxB3nB,EAAOsnB,SAETtnB,EAAOymB,kBAAoBF,EAC3BvmB,EAAOgI,KAAK,oBAAqB0e,GAC7BrR,IACEmS,GACFxnB,EAAOod,cACPpd,EAAOkb,WAAWzQ,GAClBzK,EAAOmL,iBACGsc,GAAWG,GACrB5nB,EAAOkb,WAAWzQ,GAClBzK,EAAOmL,gBACEsc,IAAYG,GACrB5nB,EAAOod,eAGXpd,EAAOgI,KAAK,aAAc0e,EAC5B,EA2CEF,cAzCF,SAAuB9Y,EAAakS,EAAMiI,GAIxC,QAHa,IAATjI,IACFA,EAAO,WAEJlS,GAAwB,cAATkS,IAAyBiI,EAAa,OAC1D,IAAItB,GAAa,EACjB,MAAMpoB,EAAS,IACT2pB,EAAyB,WAATlI,EAAoBzhB,EAAO4pB,YAAcF,EAAYhd,aACrEmd,EAAS1uB,OAAOkB,KAAKkT,GAAa1H,IAAIiiB,IAC1C,GAAqB,iBAAVA,GAA6C,IAAvBA,EAAMvtB,QAAQ,KAAY,CACzD,MAAMwtB,EAAWzkB,WAAWwkB,EAAME,OAAO,IAEzC,MAAO,CACLC,MAFYN,EAAgBI,EAG5BD,QAEJ,CACA,MAAO,CACLG,MAAOH,EACPA,WAGJD,EAAOK,KAAK,CAACpvB,EAAGqvB,IAAMtd,SAAS/R,EAAEmvB,MAAO,IAAMpd,SAASsd,EAAEF,MAAO,KAChE,IAAK,IAAIppB,EAAI,EAAGA,EAAIgpB,EAAOptB,OAAQoE,GAAK,EAAG,CACzC,MAAM,MACJipB,EAAK,MACLG,GACEJ,EAAOhpB,GACE,WAAT4gB,EACEzhB,EAAON,WAAW,eAAeuqB,QAAYlmB,UAC/CqkB,EAAa0B,GAENG,GAASP,EAAYjd,cAC9B2b,EAAa0B,EAEjB,CACA,OAAO1B,GAAc,KACvB,GA2GIgC,GAAW,CACbC,MAAM,EACNvQ,UAAW,aACXqK,gBAAgB,EAChBmG,sBAAuB,mBACvBhK,kBAAmB,UACnB1F,aAAc,EACdtY,MAAO,IACP2M,SAAS,EACT+Y,sBAAsB,EACtBuC,gBAAgB,EAChBvG,QAAQ,EACRwG,gBAAgB,EAChBC,aAAc,SACd9c,SAAS,EACTgV,kBAAmB,wDAEnBjc,MAAO,KACPE,OAAQ,KAER2S,gCAAgC,EAEhC5a,UAAW,KACX+rB,IAAK,KAELpL,oBAAoB,EACpBC,mBAAoB,GAEpBrK,YAAY,EAEZzE,gBAAgB,EAEhBoH,kBAAkB,EAElBrH,OAAQ,QAIRjB,iBAAa3O,EACbsnB,gBAAiB,SAEjB3Z,aAAc,EACd/C,cAAe,EACf8E,eAAgB,EAChBC,mBAAoB,EACpBgL,oBAAoB,EACpBvM,gBAAgB,EAChBkC,sBAAsB,EACtBhD,mBAAoB,EAEpBE,kBAAmB,EAEnBwI,qBAAqB,EACrBrF,0BAA0B,EAE1BO,eAAe,EAEf/B,cAAc,EAEdqU,WAAY,EACZT,WAAY,GACZxD,eAAe,EACfkG,aAAa,EACbF,YAAY,EACZC,gBAAiB,GACjBF,aAAc,IACdZ,cAAc,EACdzC,gBAAgB,EAChBrG,UAAW,EACXuH,0BAA0B,EAC1BjB,0BAA0B,EAC1BC,+BAA+B,EAC/BM,qBAAqB,EAErBsH,mBAAmB,EAEnBtF,YAAY,EACZD,gBAAiB,IAEjBpT,qBAAqB,EAErB+S,YAAY,EAEZmC,eAAe,EACfC,0BAA0B,EAC1BzP,qBAAqB,EAErBrL,MAAM,EACN2Q,oBAAoB,EACpBW,qBAAsB,EACtBlC,qBAAqB,EAErBrP,QAAQ,EAERkO,gBAAgB,EAChBD,gBAAgB,EAChB2H,aAAc,KAEdR,WAAW,EACXL,eAAgB,oBAChBG,kBAAmB,KAEnBsJ,kBAAkB,EAClBvY,wBAAyB,GAEzBF,uBAAwB,UAExBrH,WAAY,eACZmS,gBAAiB,qBACjBhH,iBAAkB,sBAClBnC,kBAAmB,uBACnBC,uBAAwB,6BACxBmC,eAAgB,oBAChBC,eAAgB,oBAChB0U,aAAc,iBACd7f,mBAAoB,wBACpBO,oBAAqB,EAErB4L,oBAAoB,EAEpB2T,cAAc,GAGhB,SAASC,GAAmB1oB,EAAQ2oB,GAClC,OAAO,SAAsBzvB,QACf,IAARA,IACFA,EAAM,CAAC,GAET,MAAM0vB,EAAkB9vB,OAAOkB,KAAKd,GAAK,GACnC2vB,EAAe3vB,EAAI0vB,GACG,iBAAjBC,GAA8C,OAAjBA,IAIR,IAA5B7oB,EAAO4oB,KACT5oB,EAAO4oB,GAAmB,CACxBtd,SAAS,IAGW,eAApBsd,GAAoC5oB,EAAO4oB,IAAoB5oB,EAAO4oB,GAAiBtd,UAAYtL,EAAO4oB,GAAiBzE,SAAWnkB,EAAO4oB,GAAiB1E,SAChKlkB,EAAO4oB,GAAiBE,MAAO,GAE7B,CAAC,aAAc,aAAa5uB,QAAQ0uB,IAAoB,GAAK5oB,EAAO4oB,IAAoB5oB,EAAO4oB,GAAiBtd,UAAYtL,EAAO4oB,GAAiB1pB,KACtJc,EAAO4oB,GAAiBE,MAAO,GAE3BF,KAAmB5oB,GAAU,YAAa6oB,GAIT,iBAA5B7oB,EAAO4oB,IAAmC,YAAa5oB,EAAO4oB,KACvE5oB,EAAO4oB,GAAiBtd,SAAU,GAE/BtL,EAAO4oB,KAAkB5oB,EAAO4oB,GAAmB,CACtDtd,SAAS,IAEX,EAAOqd,EAAkBzvB,IATvB,EAAOyvB,EAAkBzvB,IAfzB,EAAOyvB,EAAkBzvB,EAyB7B,CACF,CAGA,MAAM6vB,GAAa,CACjB/iB,gBACAkE,SACAtK,YACAopB,WAv6De,CACf5Y,cA7EF,SAAuBrQ,EAAUyW,GAC/B,MAAMhX,EAAS3C,KACV2C,EAAOQ,OAAO4M,UACjBpN,EAAOU,UAAU9E,MAAM6tB,mBAAqB,GAAGlpB,MAC/CP,EAAOU,UAAU9E,MAAM8tB,gBAA+B,IAAbnpB,EAAiB,MAAQ,IAEpEP,EAAOgI,KAAK,gBAAiBzH,EAAUyW,EACzC,EAuEE0B,gBAzCF,SAAyBpB,EAAcW,QAChB,IAAjBX,IACFA,GAAe,GAEjB,MAAMtX,EAAS3C,MACT,OACJmD,GACER,EACAQ,EAAO4M,UACP5M,EAAO6S,YACTrT,EAAOyQ,mBAETuH,EAAe,CACbhY,SACAsX,eACAW,YACAC,KAAM,UAEV,EAwBES,cAtBF,SAAuBrB,EAAcW,QACd,IAAjBX,IACFA,GAAe,GAEjB,MAAMtX,EAAS3C,MACT,OACJmD,GACER,EACJA,EAAOyX,WAAY,EACfjX,EAAO4M,UACXpN,EAAO4Q,cAAc,GACrBoH,EAAe,CACbhY,SACAsX,eACAW,YACAC,KAAM,QAEV,GA06DEvK,QACAnD,OACA0Y,WAxpCe,CACfC,cAjCF,SAAuBwG,GACrB,MAAM3pB,EAAS3C,KACf,IAAK2C,EAAOQ,OAAO8d,eAAiBte,EAAOQ,OAAOyP,eAAiBjQ,EAAO4pB,UAAY5pB,EAAOQ,OAAO4M,QAAS,OAC7G,MAAM1N,EAAyC,cAApCM,EAAOQ,OAAOie,kBAAoCze,EAAON,GAAKM,EAAOU,UAC5EV,EAAOgJ,YACThJ,EAAOyc,qBAAsB,GAE/B/c,EAAG9D,MAAMiuB,OAAS,OAClBnqB,EAAG9D,MAAMiuB,OAASF,EAAS,WAAa,OACpC3pB,EAAOgJ,WACTlL,sBAAsB,KACpBkC,EAAOyc,qBAAsB,GAGnC,EAoBEyK,gBAlBF,WACE,MAAMlnB,EAAS3C,KACX2C,EAAOQ,OAAOyP,eAAiBjQ,EAAO4pB,UAAY5pB,EAAOQ,OAAO4M,UAGhEpN,EAAOgJ,YACThJ,EAAOyc,qBAAsB,GAE/Bzc,EAA2C,cAApCA,EAAOQ,OAAOie,kBAAoC,KAAO,aAAa7iB,MAAMiuB,OAAS,GACxF7pB,EAAOgJ,WACTlL,sBAAsB,KACpBkC,EAAOyc,qBAAsB,IAGnC,GA2pCE/V,OAxZa,CACbojB,aArBF,WACE,MAAM9pB,EAAS3C,MACT,OACJmD,GACER,EACJA,EAAO6d,aAAeA,EAAakM,KAAK/pB,GACxCA,EAAOohB,YAAcA,EAAY2I,KAAK/pB,GACtCA,EAAO0jB,WAAaA,EAAWqG,KAAK/pB,GACpCA,EAAO4lB,qBAAuBA,EAAqBmE,KAAK/pB,GACpDQ,EAAO4M,UACTpN,EAAOwlB,SAAWA,EAASuE,KAAK/pB,IAElCA,EAAOolB,QAAUA,EAAQ2E,KAAK/pB,GAC9BA,EAAO2lB,OAASA,EAAOoE,KAAK/pB,GAC5B0G,EAAO1G,EAAQ,KACjB,EAOEgqB,aANF,WAEEtjB,EADerJ,KACA,MACjB,GA0ZEqQ,eACAwC,cA9KoB,CACpBA,cA9BF,WACE,MAAMlQ,EAAS3C,MAEbusB,SAAUK,EAAS,OACnBzpB,GACER,GACE,mBACJqM,GACE7L,EACJ,GAAI6L,EAAoB,CACtB,MAAM2G,EAAiBhT,EAAOsJ,OAAO1O,OAAS,EACxCsvB,EAAqBlqB,EAAOkM,WAAW8G,GAAkBhT,EAAOmM,gBAAgB6G,GAAuC,EAArB3G,EACxGrM,EAAO4pB,SAAW5pB,EAAOuD,KAAO2mB,CAClC,MACElqB,EAAO4pB,SAAsC,IAA3B5pB,EAAOiM,SAASrR,QAEN,IAA1B4F,EAAOgY,iBACTxY,EAAOwY,gBAAkBxY,EAAO4pB,WAEJ,IAA1BppB,EAAOiY,iBACTzY,EAAOyY,gBAAkBzY,EAAO4pB,UAE9BK,GAAaA,IAAcjqB,EAAO4pB,WACpC5pB,EAAOyS,OAAQ,GAEbwX,IAAcjqB,EAAO4pB,UACvB5pB,EAAOgI,KAAKhI,EAAO4pB,SAAW,OAAS,SAE3C,GAgLEnnB,QAjNY,CACZ0nB,WAhDF,WACE,MAAMnqB,EAAS3C,MACT,WACJ+sB,EAAU,OACV5pB,EAAM,IACNkL,EAAG,GACHhM,EAAE,OACF+E,GACEzE,EAEEqqB,EAzBR,SAAwBC,EAASC,GAC/B,MAAMC,EAAgB,GAYtB,OAXAF,EAAQ3vB,QAAQ8vB,IACM,iBAATA,EACTnxB,OAAOkB,KAAKiwB,GAAM9vB,QAAQyvB,IACpBK,EAAKL,IACPI,EAAcxoB,KAAKuoB,EAASH,KAGP,iBAATK,GAChBD,EAAcxoB,KAAKuoB,EAASE,KAGzBD,CACT,CAWmBE,CAAe,CAAC,cAAelqB,EAAOyX,UAAW,CAChE,YAAajY,EAAOQ,OAAO4Z,UAAY5Z,EAAO4Z,SAAStO,SACtD,CACD,WAActL,EAAO6S,YACpB,CACD,IAAO3H,GACN,CACD,KAAQlL,EAAOuJ,MAAQvJ,EAAOuJ,KAAKC,KAAO,GACzC,CACD,cAAexJ,EAAOuJ,MAAQvJ,EAAOuJ,KAAKC,KAAO,GAA0B,WAArBxJ,EAAOuJ,KAAKgS,MACjE,CACD,QAAWtX,EAAOE,SACjB,CACD,IAAOF,EAAOC,KACb,CACD,WAAYlE,EAAO4M,SAClB,CACD,SAAY5M,EAAO4M,SAAW5M,EAAO2M,gBACpC,CACD,iBAAkB3M,EAAO2P,sBACvB3P,EAAO8P,wBACX8Z,EAAWpoB,QAAQqoB,GACnB3qB,EAAGgD,UAAUC,OAAOynB,GACpBpqB,EAAOinB,sBACT,EAeE0D,cAbF,WACE,MACM,GACJjrB,EAAE,WACF0qB,GAHa/sB,KAKVqC,GAAoB,iBAAPA,IAClBA,EAAGgD,UAAUiG,UAAUyhB,GANR/sB,KAOR4pB,uBACT,IAqNM2D,GAAmB,CAAC,EAC1B,MAAMC,GACJ,WAAA1wB,GACE,IAAIuF,EACAc,EACJ,IAAK,IAAI8G,EAAOxI,UAAUlE,OAAQ2M,EAAO,IAAI3E,MAAM0E,GAAOE,EAAO,EAAGA,EAAOF,EAAME,IAC/ED,EAAKC,GAAQ1I,UAAU0I,GAEL,IAAhBD,EAAK3M,QAAgB2M,EAAK,GAAGpN,aAAwE,WAAzDb,OAAOM,UAAU2E,SAASzE,KAAKyN,EAAK,IAAI/I,MAAM,GAAI,GAChGgC,EAAS+G,EAAK,IAEb7H,EAAIc,GAAU+G,EAEZ/G,IAAQA,EAAS,CAAC,GACvBA,EAAS,EAAO,CAAC,EAAGA,GAChBd,IAAOc,EAAOd,KAAIc,EAAOd,GAAKA,GAClC,MAAM/C,EAAW,IACjB,GAAI6D,EAAOd,IAA2B,iBAAdc,EAAOd,IAAmB/C,EAAStB,iBAAiBmF,EAAOd,IAAI9E,OAAS,EAAG,CACjG,MAAMkwB,EAAU,GAQhB,OAPAnuB,EAAStB,iBAAiBmF,EAAOd,IAAI/E,QAAQktB,IAC3C,MAAMkD,EAAY,EAAO,CAAC,EAAGvqB,EAAQ,CACnCd,GAAImoB,IAENiD,EAAQ9oB,KAAK,IAAI6oB,GAAOE,MAGnBD,CACT,CAGA,MAAM9qB,EAAS3C,KACf2C,EAAOR,YAAa,EACpBQ,EAAO2D,QAAUG,IACjB9D,EAAOyE,OAASL,EAAU,CACxBtH,UAAW0D,EAAO1D,YAEpBkD,EAAO6D,QAAU2B,IACjBxF,EAAO8G,gBAAkB,CAAC,EAC1B9G,EAAO2H,mBAAqB,GAC5B3H,EAAOgrB,QAAU,IAAIhrB,EAAOirB,aACxBzqB,EAAOwqB,SAAWpoB,MAAMC,QAAQrC,EAAOwqB,UACzChrB,EAAOgrB,QAAQhpB,QAAQxB,EAAOwqB,SAEhC,MAAM7B,EAAmB,CAAC,EAC1BnpB,EAAOgrB,QAAQrwB,QAAQuwB,IACrBA,EAAI,CACF1qB,SACAR,SACAmrB,aAAcjC,GAAmB1oB,EAAQ2oB,GACzC1iB,GAAIzG,EAAOyG,GAAGsjB,KAAK/pB,GACnBkH,KAAMlH,EAAOkH,KAAK6iB,KAAK/pB,GACvBoH,IAAKpH,EAAOoH,IAAI2iB,KAAK/pB,GACrBgI,KAAMhI,EAAOgI,KAAK+hB,KAAK/pB,OAK3B,MAAMorB,EAAe,EAAO,CAAC,EAAG7C,GAAUY,GAqG1C,OAlGAnpB,EAAOQ,OAAS,EAAO,CAAC,EAAG4qB,EAAcR,GAAkBpqB,GAC3DR,EAAO2mB,eAAiB,EAAO,CAAC,EAAG3mB,EAAOQ,QAC1CR,EAAOqrB,aAAe,EAAO,CAAC,EAAG7qB,GAG7BR,EAAOQ,QAAUR,EAAOQ,OAAOiG,IACjCnN,OAAOkB,KAAKwF,EAAOQ,OAAOiG,IAAI9L,QAAQ2wB,IACpCtrB,EAAOyG,GAAG6kB,EAAWtrB,EAAOQ,OAAOiG,GAAG6kB,MAGtCtrB,EAAOQ,QAAUR,EAAOQ,OAAOkH,OACjC1H,EAAO0H,MAAM1H,EAAOQ,OAAOkH,OAI7BpO,OAAO4R,OAAOlL,EAAQ,CACpB8L,QAAS9L,EAAOQ,OAAOsL,QACvBpM,KAEA0qB,WAAY,GAEZ9gB,OAAQ,GACR4C,WAAY,GACZD,SAAU,GACVE,gBAAiB,GAEjBrB,aAAY,IACyB,eAA5B9K,EAAOQ,OAAOyX,UAEvBlN,WAAU,IAC2B,aAA5B/K,EAAOQ,OAAOyX,UAGvBnO,YAAa,EACbW,UAAW,EAEX+H,aAAa,EACbC,OAAO,EAEPrS,UAAW,EACXgX,kBAAmB,EACnBlW,SAAU,EACVqqB,SAAU,EACV9T,WAAW,EACX,qBAAApG,GAGE,OAAOlQ,KAAKqqB,MAAMnuB,KAAK+C,UAAY,GAAK,IAAM,GAAK,EACrD,EAEAoY,eAAgBxY,EAAOQ,OAAOgY,eAC9BC,eAAgBzY,EAAOQ,OAAOiY,eAE9BqE,gBAAiB,CACfqC,eAAWpgB,EACXqgB,aAASrgB,EACTyhB,yBAAqBzhB,EACrB4hB,oBAAgB5hB,EAChB0hB,iBAAa1hB,EACbkX,sBAAkBlX,EAClBge,oBAAgBhe,EAChB8hB,wBAAoB9hB,EAEpB+hB,kBAAmB9gB,EAAOQ,OAAOsgB,kBAEjCgD,cAAe,EACf2H,kBAAc1sB,EAEd2sB,WAAY,GACZzI,yBAAqBlkB,EACrB2hB,iBAAa3hB,EACbif,UAAW,KACXE,QAAS,MAGXgC,YAAY,EAEZc,eAAgBhhB,EAAOQ,OAAOwgB,eAC9B3C,QAAS,CACPb,OAAQ,EACR+C,OAAQ,EACRH,SAAU,EACVC,SAAU,EACVxD,KAAM,GAGR8O,aAAc,GACdC,aAAc,IAEhB5rB,EAAOgI,KAAK,WAGRhI,EAAOQ,OAAOgoB,MAChBxoB,EAAOwoB,OAKFxoB,CACT,CACA,iBAAAsL,CAAkBugB,GAChB,OAAIxuB,KAAKyN,eACA+gB,EAGF,CACL,MAAS,SACT,aAAc,cACd,iBAAkB,eAClB,cAAe,aACf,eAAgB,gBAChB,eAAgB,cAChB,gBAAiB,iBACjB,YAAe,gBACfA,EACJ,CACA,aAAA5Q,CAAc1S,GACZ,MAAM,SACJgD,EAAQ,OACR/K,GACEnD,KAEE0V,EAAkB5P,EADTvB,EAAgB2J,EAAU,IAAI/K,EAAOyI,4BACR,IAC5C,OAAO9F,EAAaoF,GAAWwK,CACjC,CACA,mBAAAjC,CAAoBjJ,GAClB,OAAOxK,KAAK4d,cAAc5d,KAAKiM,OAAOqK,KAAKpL,GAA6D,EAAlDA,EAAQ6M,aAAa,6BAAmCvN,GAChH,CACA,qBAAAiT,CAAsBjT,GAQpB,OAPIxK,KAAK0M,MAAQ1M,KAAKmD,OAAOuJ,MAAQ1M,KAAKmD,OAAOuJ,KAAKC,KAAO,IAC7B,WAA1B3M,KAAKmD,OAAOuJ,KAAKgS,KACnBlU,EAAQ1G,KAAKmN,MAAMzG,EAAQxK,KAAKmD,OAAOuJ,KAAKC,MACT,QAA1B3M,KAAKmD,OAAOuJ,KAAKgS,OAC1BlU,GAAgB1G,KAAK0I,KAAKxM,KAAKiM,OAAO1O,OAASyC,KAAKmD,OAAOuJ,KAAKC,QAG7DnC,CACT,CACA,YAAAwT,GACE,MACM,SACJ9P,EAAQ,OACR/K,GAHanD,UAKRiM,OAAS1H,EAAgB2J,EAAU,IAAI/K,EAAOyI,2BACvD,CACA,MAAAqe,GACE,MAAMtnB,EAAS3C,KACX2C,EAAO8L,UACX9L,EAAO8L,SAAU,EACb9L,EAAOQ,OAAO0iB,YAChBljB,EAAOmjB,gBAETnjB,EAAOgI,KAAK,UACd,CACA,OAAAqf,GACE,MAAMrnB,EAAS3C,KACV2C,EAAO8L,UACZ9L,EAAO8L,SAAU,EACb9L,EAAOQ,OAAO0iB,YAChBljB,EAAOknB,kBAETlnB,EAAOgI,KAAK,WACd,CACA,WAAA8jB,CAAY5qB,EAAUT,GACpB,MAAMT,EAAS3C,KACf6D,EAAWC,KAAKE,IAAIF,KAAKC,IAAIF,EAAU,GAAI,GAC3C,MAAMG,EAAMrB,EAAO2R,eAEb5Q,GADMf,EAAOuS,eACIlR,GAAOH,EAAWG,EACzCrB,EAAOqX,YAAYtW,OAA0B,IAAVN,EAAwB,EAAIA,GAC/DT,EAAOwU,oBACPxU,EAAOsT,qBACT,CACA,oBAAA2T,GACE,MAAMjnB,EAAS3C,KACf,IAAK2C,EAAOQ,OAAOyoB,eAAiBjpB,EAAON,GAAI,OAC/C,MAAMqsB,EAAM/rB,EAAON,GAAG+I,UAAU1F,MAAM,KAAKtI,OAAOgO,GACT,IAAhCA,EAAU/N,QAAQ,WAA+E,IAA5D+N,EAAU/N,QAAQsF,EAAOQ,OAAO8P,yBAE9EtQ,EAAOgI,KAAK,oBAAqB+jB,EAAIvV,KAAK,KAC5C,CACA,eAAAwV,CAAgBzjB,GACd,MAAMvI,EAAS3C,KACf,OAAI2C,EAAO+G,UAAkB,GACtBwB,EAAQE,UAAU1F,MAAM,KAAKtI,OAAOgO,GACI,IAAtCA,EAAU/N,QAAQ,iBAAyE,IAAhD+N,EAAU/N,QAAQsF,EAAOQ,OAAOyI,aACjFuN,KAAK,IACV,CACA,iBAAAjC,GACE,MAAMvU,EAAS3C,KACf,IAAK2C,EAAOQ,OAAOyoB,eAAiBjpB,EAAON,GAAI,OAC/C,MAAMusB,EAAU,GAChBjsB,EAAOsJ,OAAO3O,QAAQ4N,IACpB,MAAM6hB,EAAapqB,EAAOgsB,gBAAgBzjB,GAC1C0jB,EAAQjqB,KAAK,CACXuG,UACA6hB,eAEFpqB,EAAOgI,KAAK,cAAeO,EAAS6hB,KAEtCpqB,EAAOgI,KAAK,gBAAiBikB,EAC/B,CACA,oBAAAriB,CAAqBsiB,EAAMC,QACZ,IAATD,IACFA,EAAO,gBAEK,IAAVC,IACFA,GAAQ,GAEV,MACM,OACJ3rB,EAAM,OACN8I,EAAM,WACN4C,EAAU,gBACVC,EACA5I,KAAMiI,EAAU,YAChB1B,GAPazM,KASf,IAAI+uB,EAAM,EACV,GAAoC,iBAAzB5rB,EAAOmJ,cAA4B,OAAOnJ,EAAOmJ,cAC5D,GAAInJ,EAAO2M,eAAgB,CACzB,IACIkf,EADA/e,EAAYhE,EAAOQ,GAAe3I,KAAK0I,KAAKP,EAAOQ,GAAayE,iBAAmB,EAEvF,IAAK,IAAIvP,EAAI8K,EAAc,EAAG9K,EAAIsK,EAAO1O,OAAQoE,GAAK,EAChDsK,EAAOtK,KAAOqtB,IAChB/e,GAAanM,KAAK0I,KAAKP,EAAOtK,GAAGuP,iBACjC6d,GAAO,EACH9e,EAAY9B,IAAY6gB,GAAY,IAG5C,IAAK,IAAIrtB,EAAI8K,EAAc,EAAG9K,GAAK,EAAGA,GAAK,EACrCsK,EAAOtK,KAAOqtB,IAChB/e,GAAahE,EAAOtK,GAAGuP,gBACvB6d,GAAO,EACH9e,EAAY9B,IAAY6gB,GAAY,GAG9C,MAEE,GAAa,YAATH,EACF,IAAK,IAAIltB,EAAI8K,EAAc,EAAG9K,EAAIsK,EAAO1O,OAAQoE,GAAK,GAChCmtB,EAAQjgB,EAAWlN,GAAKmN,EAAgBnN,GAAKkN,EAAWpC,GAAe0B,EAAaU,EAAWlN,GAAKkN,EAAWpC,GAAe0B,KAEhJ4gB,GAAO,QAKX,IAAK,IAAIptB,EAAI8K,EAAc,EAAG9K,GAAK,EAAGA,GAAK,EACrBkN,EAAWpC,GAAeoC,EAAWlN,GAAKwM,IAE5D4gB,GAAO,GAKf,OAAOA,CACT,CACA,MAAA1hB,GACE,MAAM1K,EAAS3C,KACf,IAAK2C,GAAUA,EAAO+G,UAAW,OACjC,MAAM,SACJkF,EAAQ,OACRzL,GACER,EAcJ,SAAS+W,IACP,MAAMuV,EAAiBtsB,EAAOyL,cAAmC,EAApBzL,EAAOI,UAAiBJ,EAAOI,UACtEuX,EAAexW,KAAKE,IAAIF,KAAKC,IAAIkrB,EAAgBtsB,EAAOuS,gBAAiBvS,EAAO2R,gBACtF3R,EAAO+W,aAAaY,GACpB3X,EAAOwU,oBACPxU,EAAOsT,qBACT,CACA,IAAIiZ,EACJ,GApBI/rB,EAAOkN,aACT1N,EAAO6kB,gBAET,IAAI7kB,EAAON,GAAGrE,iBAAiB,qBAAqBV,QAAQmO,IACtDA,EAAQ0jB,UACV3jB,EAAqB7I,EAAQ8I,KAGjC9I,EAAO2K,aACP3K,EAAOmL,eACPnL,EAAOoS,iBACPpS,EAAOsT,sBASH9S,EAAO4Z,UAAY5Z,EAAO4Z,SAAStO,UAAYtL,EAAO4M,QACxD2J,IACIvW,EAAO6S,YACTrT,EAAOyQ,uBAEJ,CACL,IAA8B,SAAzBjQ,EAAOmJ,eAA4BnJ,EAAOmJ,cAAgB,IAAM3J,EAAOyS,QAAUjS,EAAO2M,eAAgB,CAC3G,MAAM7D,EAAStJ,EAAO6L,SAAWrL,EAAOqL,QAAQC,QAAU9L,EAAO6L,QAAQvC,OAAStJ,EAAOsJ,OACzFijB,EAAavsB,EAAOmY,QAAQ7O,EAAO1O,OAAS,EAAG,GAAG,GAAO,EAC3D,MACE2xB,EAAavsB,EAAOmY,QAAQnY,EAAO8J,YAAa,GAAG,GAAO,GAEvDyiB,GACHxV,GAEJ,CACIvW,EAAOyP,eAAiBhE,IAAajM,EAAOiM,UAC9CjM,EAAOkQ,gBAETlQ,EAAOgI,KAAK,SACd,CACA,eAAA0f,CAAgB+E,EAAcC,QACT,IAAfA,IACFA,GAAa,GAEf,MAAM1sB,EAAS3C,KACTsvB,EAAmB3sB,EAAOQ,OAAOyX,UAKvC,OAJKwU,IAEHA,EAAoC,eAArBE,EAAoC,WAAa,cAE9DF,IAAiBE,GAAqC,eAAjBF,GAAkD,aAAjBA,IAG1EzsB,EAAON,GAAGgD,UAAUiG,OAAO,GAAG3I,EAAOQ,OAAO8P,yBAAyBqc,KACrE3sB,EAAON,GAAGgD,UAAUC,IAAI,GAAG3C,EAAOQ,OAAO8P,yBAAyBmc,KAClEzsB,EAAOinB,uBACPjnB,EAAOQ,OAAOyX,UAAYwU,EAC1BzsB,EAAOsJ,OAAO3O,QAAQ4N,IACC,aAAjBkkB,EACFlkB,EAAQ3M,MAAMiJ,MAAQ,GAEtB0D,EAAQ3M,MAAMmJ,OAAS,KAG3B/E,EAAOgI,KAAK,mBACR0kB,GAAY1sB,EAAO0K,UAdd1K,CAgBX,CACA,uBAAA4sB,CAAwB3U,GACtB,MAAMjY,EAAS3C,KACX2C,EAAO0L,KAAqB,QAAduM,IAAwBjY,EAAO0L,KAAqB,QAAduM,IACxDjY,EAAO0L,IAAoB,QAAduM,EACbjY,EAAOyL,aAA2C,eAA5BzL,EAAOQ,OAAOyX,WAA8BjY,EAAO0L,IACrE1L,EAAO0L,KACT1L,EAAON,GAAGgD,UAAUC,IAAI,GAAG3C,EAAOQ,OAAO8P,6BACzCtQ,EAAON,GAAGmB,IAAM,QAEhBb,EAAON,GAAGgD,UAAUiG,OAAO,GAAG3I,EAAOQ,OAAO8P,6BAC5CtQ,EAAON,GAAGmB,IAAM,OAElBb,EAAO0K,SACT,CACA,KAAAmiB,CAAMhrB,GACJ,MAAM7B,EAAS3C,KACf,GAAI2C,EAAO8sB,QAAS,OAAO,EAG3B,IAAIptB,EAAKmC,GAAW7B,EAAOQ,OAAOd,GAIlC,GAHkB,iBAAPA,IACTA,EAAK/C,SAASvB,cAAcsE,KAEzBA,EACH,OAAO,EAETA,EAAGM,OAASA,EACRN,EAAGqtB,YAAcrtB,EAAGqtB,WAAW5wB,MAAQuD,EAAGqtB,WAAW5wB,KAAKhB,WAAa6E,EAAOQ,OAAOioB,sBAAsBuE,gBAC7GhtB,EAAOgJ,WAAY,GAErB,MAAMikB,EAAqB,IAClB,KAAKjtB,EAAOQ,OAAOwoB,cAAgB,IAAIlmB,OAAOC,MAAM,KAAKyT,KAAK,OAWvE,IAAI9V,EAREhB,GAAMA,EAAG0J,YAAc1J,EAAG0J,WAAWhO,cAC3BsE,EAAG0J,WAAWhO,cAAc6xB,KAInCrrB,EAAgBlC,EAAIutB,KAAsB,GAsBnD,OAlBKvsB,GAAaV,EAAOQ,OAAOmoB,iBAC9BjoB,EAAYjF,EAAc,MAAOuE,EAAOQ,OAAOwoB,cAC/CtpB,EAAGic,OAAOjb,GACVkB,EAAgBlC,EAAI,IAAIM,EAAOQ,OAAOyI,cAActO,QAAQ4N,IAC1D7H,EAAUib,OAAOpT,MAGrBjP,OAAO4R,OAAOlL,EAAQ,CACpBN,KACAgB,YACA6K,SAAUvL,EAAOgJ,YAActJ,EAAGqtB,WAAW5wB,KAAK+wB,WAAaxtB,EAAGqtB,WAAW5wB,KAAOuE,EACpFysB,OAAQntB,EAAOgJ,UAAYtJ,EAAGqtB,WAAW5wB,KAAOuD,EAChDotB,SAAS,EAETphB,IAA8B,QAAzBhM,EAAGmB,IAAI8E,eAA6D,QAAlCzC,EAAaxD,EAAI,aACxD+L,aAA0C,eAA5BzL,EAAOQ,OAAOyX,YAAwD,QAAzBvY,EAAGmB,IAAI8E,eAA6D,QAAlCzC,EAAaxD,EAAI,cAC9GiM,SAAiD,gBAAvCzI,EAAaxC,EAAW,cAE7B,CACT,CACA,IAAA8nB,CAAK9oB,GACH,MAAMM,EAAS3C,KACf,GAAI2C,EAAOqV,YAAa,OAAOrV,EAE/B,IAAgB,IADAA,EAAO6sB,MAAMntB,GACN,OAAOM,EAC9BA,EAAOgI,KAAK,cAGRhI,EAAOQ,OAAOkN,aAChB1N,EAAO6kB,gBAIT7kB,EAAOmqB,aAGPnqB,EAAO2K,aAGP3K,EAAOmL,eACHnL,EAAOQ,OAAOyP,eAChBjQ,EAAOkQ,gBAILlQ,EAAOQ,OAAO0iB,YAAcljB,EAAO8L,SACrC9L,EAAOmjB,gBAILnjB,EAAOQ,OAAOgK,MAAQxK,EAAO6L,SAAW7L,EAAOQ,OAAOqL,QAAQC,QAChE9L,EAAOmY,QAAQnY,EAAOQ,OAAOuY,aAAe/Y,EAAO6L,QAAQoD,aAAc,EAAGjP,EAAOQ,OAAO8U,oBAAoB,GAAO,GAErHtV,EAAOmY,QAAQnY,EAAOQ,OAAOuY,aAAc,EAAG/Y,EAAOQ,OAAO8U,oBAAoB,GAAO,GAIrFtV,EAAOQ,OAAOgK,MAChBxK,EAAOkb,gBAAWnc,GAAW,GAI/BiB,EAAO8pB,eACP,MAAMsD,EAAe,IAAIptB,EAAON,GAAGrE,iBAAiB,qBAsBpD,OArBI2E,EAAOgJ,WACTokB,EAAaprB,QAAQhC,EAAOmtB,OAAO9xB,iBAAiB,qBAEtD+xB,EAAazyB,QAAQmO,IACfA,EAAQ0jB,SACV3jB,EAAqB7I,EAAQ8I,GAE7BA,EAAQ/N,iBAAiB,OAAQgd,IAC/BlP,EAAqB7I,EAAQ+X,EAAE1d,YAIrCmP,EAAQxJ,GAGRA,EAAOqV,aAAc,EACrB7L,EAAQxJ,GAGRA,EAAOgI,KAAK,QACZhI,EAAOgI,KAAK,aACLhI,CACT,CACA,OAAAqtB,CAAQC,EAAgBC,QACC,IAAnBD,IACFA,GAAiB,QAEC,IAAhBC,IACFA,GAAc,GAEhB,MAAMvtB,EAAS3C,MACT,OACJmD,EAAM,GACNd,EAAE,UACFgB,EAAS,OACT4I,GACEtJ,EACJ,YAA6B,IAAlBA,EAAOQ,QAA0BR,EAAO+G,YAGnD/G,EAAOgI,KAAK,iBAGZhI,EAAOqV,aAAc,EAGrBrV,EAAOgqB,eAGHxpB,EAAOgK,MACTxK,EAAOod,cAILmQ,IACFvtB,EAAO2qB,gBACHjrB,GAAoB,iBAAPA,GACfA,EAAG6J,gBAAgB,SAEjB7I,GACFA,EAAU6I,gBAAgB,SAExBD,GAAUA,EAAO1O,QACnB0O,EAAO3O,QAAQ4N,IACbA,EAAQ7F,UAAUiG,OAAOnI,EAAOyR,kBAAmBzR,EAAO0R,uBAAwB1R,EAAO4T,iBAAkB5T,EAAO6T,eAAgB7T,EAAO8T,gBACzI/L,EAAQgB,gBAAgB,SACxBhB,EAAQgB,gBAAgB,8BAI9BvJ,EAAOgI,KAAK,WAGZ1O,OAAOkB,KAAKwF,EAAO8G,iBAAiBnM,QAAQ2wB,IAC1CtrB,EAAOoH,IAAIkkB,MAEU,IAAnBgC,IACEttB,EAAON,IAA2B,iBAAdM,EAAON,KAC7BM,EAAON,GAAGM,OAAS,MD/zH3B,SAAqBtG,GACnB,MAAM8zB,EAAS9zB,EACfJ,OAAOkB,KAAKgzB,GAAQ7yB,QAAQvB,IAC1B,IACEo0B,EAAOp0B,GAAO,IAChB,CAAE,MAAO2e,GAET,CACA,WACSyV,EAAOp0B,EAChB,CAAE,MAAO2e,GAET,GAEJ,CCmzHM0V,CAAYztB,IAEdA,EAAO+G,WAAY,GA5CV,IA8CX,CACA,qBAAO2mB,CAAeC,GACpB,EAAO/C,GAAkB+C,EAC3B,CACA,2BAAW/C,GACT,OAAOA,EACT,CACA,mBAAWrC,GACT,OAAOA,EACT,CACA,oBAAOqF,CAAc1C,GACdL,GAAOjxB,UAAUqxB,cAAaJ,GAAOjxB,UAAUqxB,YAAc,IAClE,MAAMD,EAAUH,GAAOjxB,UAAUqxB,YACd,mBAARC,GAAsBF,EAAQtwB,QAAQwwB,GAAO,GACtDF,EAAQhpB,KAAKkpB,EAEjB,CACA,UAAO2C,CAAIh1B,GACT,OAAI+J,MAAMC,QAAQhK,IAChBA,EAAO8B,QAAQmzB,GAAKjD,GAAO+C,cAAcE,IAClCjD,KAETA,GAAO+C,cAAc/0B,GACdgyB,GACT,EAEFvxB,OAAOkB,KAAK+uB,IAAY5uB,QAAQozB,IAC9Bz0B,OAAOkB,KAAK+uB,GAAWwE,IAAiBpzB,QAAQqzB,IAC9CnD,GAAOjxB,UAAUo0B,GAAezE,GAAWwE,GAAgBC,OAG/DnD,GAAOgD,IAAI,CApwHX,SAAgB9tB,GACd,IAAI,OACFC,EAAM,GACNyG,EAAE,KACFuB,GACEjI,EACJ,MAAM5B,EAAS,IACf,IAAI8vB,EAAW,KACXC,EAAiB,KACrB,MAAMC,EAAgB,KACfnuB,IAAUA,EAAO+G,WAAc/G,EAAOqV,cAC3CrN,EAAK,gBACLA,EAAK,YAsCDomB,EAA2B,KAC1BpuB,IAAUA,EAAO+G,WAAc/G,EAAOqV,aAC3CrN,EAAK,sBAEPvB,EAAG,OAAQ,KACLzG,EAAOQ,OAAOkoB,qBAAmD,IAA1BvqB,EAAOkwB,eAxC7CruB,IAAUA,EAAO+G,WAAc/G,EAAOqV,cAC3C4Y,EAAW,IAAII,eAAe/D,IAC5B4D,EAAiB/vB,EAAOL,sBAAsB,KAC5C,MAAM,MACJ+G,EAAK,OACLE,GACE/E,EACJ,IAAIsuB,EAAWzpB,EACX8L,EAAY5L,EAChBulB,EAAQ3vB,QAAQ4zB,IACd,IAAI,eACFC,EAAc,YACdC,EAAW,OACXp0B,GACEk0B,EACAl0B,GAAUA,IAAW2F,EAAON,KAChC4uB,EAAWG,EAAcA,EAAY5pB,OAAS2pB,EAAe,IAAMA,GAAgBE,WACnF/d,EAAY8d,EAAcA,EAAY1pB,QAAUypB,EAAe,IAAMA,GAAgBG,aAEnFL,IAAazpB,GAAS8L,IAAc5L,GACtCopB,QAINF,EAASW,QAAQ5uB,EAAON,MAoBxBvB,EAAOpD,iBAAiB,SAAUozB,GAClChwB,EAAOpD,iBAAiB,oBAAqBqzB,MAE/C3nB,EAAG,UAAW,KApBRynB,GACF/vB,EAAOH,qBAAqBkwB,GAE1BD,GAAYA,EAASY,WAAa7uB,EAAON,KAC3CuuB,EAASY,UAAU7uB,EAAON,IAC1BuuB,EAAW,MAiBb9vB,EAAOnD,oBAAoB,SAAUmzB,GACrChwB,EAAOnD,oBAAoB,oBAAqBozB,IAEpD,EAEA,SAAkBruB,GAChB,IAAI,OACFC,EAAM,aACNmrB,EAAY,GACZ1kB,EAAE,KACFuB,GACEjI,EACJ,MAAM+uB,EAAY,GACZ3wB,EAAS,IACT4wB,EAAS,SAAU10B,EAAQ20B,QACf,IAAZA,IACFA,EAAU,CAAC,GAEb,MACMf,EAAW,IADI9vB,EAAO8wB,kBAAoB9wB,EAAO+wB,wBACrBC,IAIhC,GAAInvB,EAAOyc,oBAAqB,OAChC,GAAyB,IAArB0S,EAAUv0B,OAEZ,YADAoN,EAAK,iBAAkBmnB,EAAU,IAGnC,MAAMC,EAAiB,WACrBpnB,EAAK,iBAAkBmnB,EAAU,GACnC,EACIhxB,EAAOL,sBACTK,EAAOL,sBAAsBsxB,GAE7BjxB,EAAOR,WAAWyxB,EAAgB,KAGtCnB,EAASW,QAAQv0B,EAAQ,CACvBg1B,gBAA0C,IAAvBL,EAAQK,YAAoCL,EAAQK,WACvEC,UAAWtvB,EAAOgJ,iBAA2C,IAAtBgmB,EAAQM,WAAmCN,GAASM,UAC3FC,mBAAgD,IAA1BP,EAAQO,eAAuCP,EAAQO,gBAE/ET,EAAU9sB,KAAKisB,EACjB,EAyBA9C,EAAa,CACX8C,UAAU,EACVuB,gBAAgB,EAChBC,sBAAsB,IAExBhpB,EAAG,OA7BU,KACX,GAAKzG,EAAOQ,OAAOytB,SAAnB,CACA,GAAIjuB,EAAOQ,OAAOgvB,eAAgB,CAChC,MAAME,EDwEZ,SAAwBhwB,GACtB,MAAMiwB,EAAU,GAChB,IAAIjR,EAAShf,EAAGkwB,cAChB,KAAOlR,GAIHiR,EAAQ3tB,KAAK0c,GAEfA,EAASA,EAAOkR,cAElB,OAAOD,CACT,CCpF+BE,CAAe7vB,EAAOmtB,QAC/C,IAAK,IAAInuB,EAAI,EAAGA,EAAI0wB,EAAiB90B,OAAQoE,GAAK,EAChD+vB,EAAOW,EAAiB1wB,GAE5B,CAEA+vB,EAAO/uB,EAAOmtB,OAAQ,CACpBmC,UAAWtvB,EAAOQ,OAAOivB,uBAI3BV,EAAO/uB,EAAOU,UAAW,CACvB2uB,YAAY,GAdqB,IA6BrC5oB,EAAG,UAZa,KACdqoB,EAAUn0B,QAAQszB,IAChBA,EAAS6B,eAEXhB,EAAUhnB,OAAO,EAAGgnB,EAAUl0B,SASlC,IC1OA,IA8Be3B,GADP82B,GA5BJC,IAAsB,EACtBC,GAAe,KACfC,IAAwB,EACxBC,GAAiB,KACjBC,GAAoB,EACpBC,IAAkC,EAClCC,GAA2B,KACzBC,IAqBER,IAAQ,EACD92B,GAAg8D4D,UAAUC,WAAaD,UAAU2zB,QAAUryB,OAAOsyB,OAAz+D,2TAA2TrqB,KAAKnN,KAAM,0kDAA0kDmN,KAAKnN,GAAEkvB,OAAO,EAAG,OAAK4H,IAAQ,GAC37DA,IAGX,SAASW,GAAqBC,GAG1B,OAFAV,GAAe,GACf,GAAGjuB,KAAKyF,MAAMwoB,GAAcU,GACrBA,CACX,CAYA,SAASC,GAAiCD,GAGtC,OAFAL,GAA2B,GAC3B,GAAGtuB,KAAKyF,MAAM6oB,GAA0BK,GACjCA,CACX,CAYA,SAASE,GAAuBF,GAG5B,OAFAR,GAAiB,GACjB,GAAGnuB,KAAKyF,MAAM0oB,GAAgBQ,GACvBA,CACX,CA5DAG,IAAAA,aAAiBnuB,IAAI,gCAAiC,YAClDvI,EAAAA,EAAAA,QAAO22B,IAAAA,UAAyB,OAAQ,SAAUC,GAC9C,IAAMC,EAAYH,IAAAA,QAAYr3B,IAAI,aAE9Bw3B,IACkB,SAAdA,GAkbhB,SAA+BC,GACvBX,KACAY,EAAE,uBAAuBxd,KAAK,qBAAqByd,KAAK,wCACxDD,EAAE,uBAAuBxd,KAAK,qBAAqB0d,IAAI,UAAW,SAClEF,EAAE,uBAAuBxd,KAAK,qBAAqB0d,IAAI,YAAa,QACpEF,EAAE,uBAAuBxd,KAAK,qBAAqB0d,IAAI,eAAgB,SAG3EF,EAAE,uBAAuBxd,KAAK,KAAK0d,IAAI,UAAW,QAClDF,EAAE,aAAaxoB,SACfwoB,EAAE,aAAaE,IAAI,UAAW,QAE9B,IAAIC,EAAOC,YAAY,WACnB,GAAIL,EAAKM,MACLC,cAAcH,QAEGvyB,IAAbmyB,EAAKM,KAAmB,CACxB,IAAIE,EAAiBZ,IAAAA,MAAUa,UAAU,kCAEpCD,IACDA,EAAiB,KAGrB,IACIE,EAA2B,EADbT,EAAEhzB,QAAQ0G,QACO,GAE/BgtB,EAAkBl1B,SAASrB,eAAe,qBAE9C,GAAwB,OAApBu2B,EACA,QAGJA,EAAkBl1B,SAASlB,cAAc,QACzBgN,UAAY,oBAC5BopB,EAAgB5zB,GAAK,qBAEA,IAAjBsyB,KACAsB,EAAgBj2B,MAAMiJ,MAAQ+sB,EAAa,KAC3CC,EAAgBj2B,MAAMmR,YAA4B,KAAb6kB,EAAsB,MAG/D,IAAI5xB,EAASrD,SAASlB,cAAc,OACpCuE,EAAOyI,UAAY,kBACnBopB,EAAgBC,YAAY9xB,GAE5B,IAAI+xB,EAAiBp1B,SAASlB,cAAc,OAC5Cs2B,EAAetpB,UAAY,iBAC3BzI,EAAO8xB,YAAYC,GAEnB,IAAK,IAAI/yB,EAAI,EAAGA,GAAK,GAAIA,IAAK,CAC1B,IAAIgzB,EAAer1B,SAASlB,cAAc,OACtCw2B,EAAWnB,IAAAA,MAAUa,UAAU,wBAA0B3yB,GACzDkzB,EAAYpB,IAAAA,MAAUa,UAAU,uBAAyB3yB,GAEzDizB,IACAD,EAAavpB,UAAY,eACzBupB,EAAaG,UAAY,wCAA0CD,EAAY,YAAcD,EAAW,OACxGF,EAAeD,YAAYE,GAEnC,CAEA,IAAII,EAAqBz1B,SAASlB,cAAc,OAChD22B,EAAmB3pB,UAAY,qBAC/BzI,EAAO8xB,YAAYM,GAEnB,IAAIC,EAAqB11B,SAASlB,cAAc,OAChD42B,EAAmB5pB,UAAY,qBAC/BzI,EAAO8xB,YAAYO,GAEnB,IAAIC,EAAoB31B,SAASlB,cAAc,OAC/C62B,EAAkB7pB,UAAY,oBAC9BzI,EAAO8xB,YAAYQ,GAEnBnB,EAAE,uBAAuBxU,QAAQkV,GAEjC,IAAIhH,GAAO,YAAa,CACpB9F,SAAU,CACN1mB,MAAOqzB,GAEXlnB,MAAM,EACNkC,aAAc,GACdiC,OAAQ,YACRxB,gBAAgB,EAChBxD,cAAe,EACf4oB,gBAAiB,CACbC,OAAQ,EACRC,MAAO,IACPC,SAAU,EACVC,cAAc,EACdC,QAAS,GAEbC,WAAY,CACRnzB,GAAI,qBACJqe,KAAM,WAEV0G,WAAY,CACRC,OAAQ,sBACRC,OAAQ,uBAEZqG,QAAS,CAAC8H,GAAiBC,GAAYC,GAAYC,MA5dvE,YACkC,IAA1B/C,KACAA,IAAwB,EACjBY,IAAAA,MACFnd,KAAK,kBAAiB,MAChB,WAAQ,GACduf,KAAKrC,GAAuB9G,KAAK1sB,OAE9C,CAudgB81B,GA/fhB,YACgC,IAAxBnD,KACAA,IAAsB,EACfc,IAAAA,MACFnd,KAAK,oBAAmB,MAClB,WAAQ,GACduf,KAAKxC,GAAqB3G,KAAK1sB,OAE5C,CAwfgB+1B,GAhfhB,YAC4C,IAApC/C,KACAA,IAAkC,EAC3BS,IAAAA,MACFnd,KAAK,4BAA2B,MAC1B,WAAQ,GACduf,KAAKtC,GAAiC7G,KAAK1sB,OAExD,CAyegBg2B,GAEA,IAAIC,EAAgB/B,YAAY,WAxHhD,IACQgC,EAwHiC,OAAjBtD,IAA4C,OAAnBE,IAAwD,OAA7BG,KACpDmB,cAAc6B,GAE0B,IAApCnC,EAAE,uBAAuBv2B,SA7drD,WACI,IAAM44B,EAAUrC,EAAE,YAElB,GAAwC,IAApCA,EAAE,uBAAuBv2B,OAAc,CACvC,IAAIi3B,EAAkBl1B,SAASlB,cAAc,OAC7Co2B,EAAgBppB,UAAY,qBAC5BopB,EAAgB5zB,GAAK,qBAErB,IAAI+B,EAASrD,SAASlB,cAAc,OACpCuE,EAAOyI,UAAY,mBAEnB,IAAIgrB,EAAwB92B,SAASlB,cAAc,OACnDg4B,EAAsBhrB,UAAY,wBAElCopB,EAAgBC,YAAY2B,GAC5BA,EAAsB3B,YAAY9xB,GAElC,IAAI+xB,EAAiBp1B,SAASlB,cAAc,OAC5Cs2B,EAAetpB,UAAY,iBAC3BspB,EAAe9zB,GAAK,mBACpB+B,EAAO8xB,YAAYC,GAEnB,IAAK,IAAI/yB,EAAI,EAAGA,EAAIw0B,EAAQ54B,OAAQoE,IAAK,CACrC,IAAIwD,EAAMgxB,EAAQx0B,GACd00B,EAASvC,EAAE3uB,GAAKmR,KAAK,KAAKggB,KAAK,QAC/BC,EAAgBzC,EAAE3uB,GAAK6uB,IAAI,cAC3BwC,EAAU1C,EAAE3uB,GAAKmR,KAAK,iBAAiBvR,OACvC0xB,EAAe3C,EAAE3uB,GAAKmR,KAAK,iBAAiB0d,IAAI,SAQhDW,GAPUb,EAAE3uB,GAAKmR,KAAK,wBAAwBvR,OAC/B+uB,EAAE3uB,GAAKmR,KAAK,wBAAwB0d,IAAI,SAMxC10B,SAASlB,cAAc,QAC1Cu2B,EAAavpB,UAAY,gCACzBupB,EAAaG,UAAY,YAAcuB,EAAS,kBAAoBnD,GAAe,gCAAkC,0BAA4B,uBAAyBqD,EAAgB,wIAA0IE,EAAe,KAAOD,EAAU,mBAEpW9B,EAAeD,YAAYE,EAC/B,CAEAb,EAAE,yCAAyCxU,QAAQkV,GACnDV,EAAEsC,GAAuB9W,QAAQ,+EACjCwU,EAAEsC,GAAuB9X,OAAO,koCAEhCwV,EAAE,aAAaxoB,UAEM,IAAjB4nB,KACAY,EAAE,QAAQE,IAAI,aAAc,UAC5BF,EAAE,gBAAgBE,IAAI,aAAc,QACpCF,EAAE,gBAAgBE,IAAI,aAAc,KAGxC,IAAIxG,GAAO,aAAc,CACrBrgB,MAAM,EACNkC,aAAc6jB,GAAe,GAAK,GAClC5mB,cAAe4mB,GAAe,EAAI,EAClCxL,SAAU,CACN1mB,MAAO,IACP01B,sBAAsB,GAE1B/I,QAAS,CAACiI,MAOtB,WACI,IAAIe,EAAwBr3B,SAASrB,eAAe,yBAEpD,GAA8B,OAA1B04B,EAAgC,EAChCA,EAAwBr3B,SAASlB,cAAc,QACzBwC,GAAK,wBAC3B+1B,EAAsB7B,UAAY,4DAClC6B,EAAsBvrB,UAAY,wBAClC0oB,EAAE,uBAAuBxV,OAAOqY,GAEhC,IAAIh0B,EAASrD,SAASlB,cAAc,OACpCuE,EAAOyI,UAAY,wBACnB0oB,EAAE,uBAAuBxV,OAAO3b,GAEhC,IAAI+xB,EAAiBp1B,SAASlB,cAAc,OAC5Cs2B,EAAetpB,UAAY,iBAC3BzI,EAAO8xB,YAAYC,GAEnB,IAAK,IAAI/yB,EAAI,EAAGA,EAAIixB,GAAar1B,OAAQoE,IAAK,CAC1C,IAAIi1B,EAAehE,GAAajxB,GAE5Bk1B,GADeD,EAAaE,OACTnpB,SAASipB,EAAaG,YAAc,QACvDC,EAAqB,OAASJ,EAAaK,MAAQ,KAEnDtC,EAAer1B,SAASlB,cAAc,OAC1Cu2B,EAAavpB,UAAY,gCACzBupB,EAAaG,UAAY,wEAA0EkC,EAAqB,2XAA6XH,EAAmB,4BAExgBnC,EAAeD,YAAYE,EAC/B,CAEA,IAAInH,GAAO,kBAAmB,CAC1BrgB,MAAM,EACNkC,aAAc6jB,GAAe,GAAK,GAClC5mB,cAAe4mB,GAAe,EAAI,GAE1C,CAEJ,CA1CQgE,EACJ,CACJ,CA2Z4BC,GA1N5B,WACqCrD,EAAE,uBAAuBE,IAAI,UAA9D,IAEIoD,EAAiB93B,SAASlB,cAAc,OAC5Cg5B,EAAehsB,UAAY,iBAC3BgsB,EAAe74B,MAAMmJ,OAASosB,EAAE,uBAAuBE,IAAI,UAK3DoD,EAAetC,UAAY,4GAA8G5B,GAAe,GAAK,GAAlI,gzCAE3BY,EAAE,8BAA8BxU,QAAQ8X,GAExC,IAAMC,EAAYnE,GAAe,WAAa,QAC1CoE,EAAch4B,SAASrB,eAAe,eAE1C61B,EAAE,sBAAsB1qB,GAAGiuB,EAAW,WAClCC,EAAYr6B,IAAM,GAElBqD,WAAW,WACP,IAAIi3B,EAAgBzE,GAAeC,IAAmBuB,UAAU,SAChEgD,EAAYr6B,IAAMs6B,CACtB,EAAG,IACP,GAEAzD,EAAE,mBAAmB1qB,GAAGiuB,EAAW,WAI/B,GAFAtE,UAE0CrxB,IAAtCoxB,GAAeC,IAAkC,CACjD,IAAIwE,EAAgBzE,GAAeC,IAAmBuB,UAAU,SAChEgD,EAAYr6B,IAAMs6B,CACtB,CAEAzD,EAAE,oBAAoBE,IAAI,QAAS,SACWtyB,IAA1CoxB,GAAeC,GAAoB,GACnCe,EAAE,oBAAoBE,IAAI,QAAS,QAEnCF,EAAE,oBAAoBE,IAAI,QAAS,GAE3C,GAEAF,EAAE,mBAAmB1qB,GAAGiuB,EAAW,WAG/B,GAFAtE,UAE0CrxB,IAAtCoxB,GAAeC,IAAkC,CACjD,IAAIwE,EAAgBzE,GAAeC,IAAmBuB,UAAU,SAChEgD,EAAYr6B,IAAMs6B,CACtB,CAEAzD,EAAE,oBAAoBE,IAAI,QAAS,SACWtyB,IAA1CoxB,GAAeC,GAAoB,GACnCe,EAAE,oBAAoBE,IAAI,QAAS,QAEnCF,EAAE,oBAAoBE,IAAI,QAAS,GAE3C,EACJ,CAiK4BwD,GApJ5B,WACqC1D,EAAE,uBAAuBE,IAAI,UAA9D,IAEIyD,EAAiBn4B,SAASlB,cAAc,OAC5Cq5B,EAAersB,UAAY,iBAC3BqsB,EAAel5B,MAAMmJ,OAASosB,EAAE,uBAAuBE,IAAI,UAC3DyD,EAAeC,UAAYjE,IAAAA,WAAekE,MAAM,+CAEhD7D,EAAE,8BAA8BxU,QAAQmY,EAC5C,CA4I4BG,GAhK5B,WACqC9D,EAAE,uBAAuBE,IAAI,UAA9D,IAEI6D,EAA+Bv4B,SAASlB,cAAc,OAC1Dy5B,EAA6BzsB,UAAY,+BACzCysB,EAA6Bt5B,MAAMmJ,OAASosB,EAAE,uBAAuBE,IAAI,UACzE6D,EAA6B/C,UAAY,iGAEzChB,EAAE,8BAA8BxU,QAAQuY,EAC5C,CAwJ4BC,GA3I5B,WACqChE,EAAE,uBAAuBE,IAAI,UAA9D,IAEI+D,EAAsBz4B,SAASlB,cAAc,OACjD25B,EAAoB3sB,UAAY,sBAChC2sB,EAAoBx5B,MAAMmJ,OAASosB,EAAE,uBAAuBE,IAAI,UAChE+D,EAAoBL,UAAYjE,IAAAA,WAAekE,MAAM,+CAErD7D,EAAE,8BAA8BxU,QAAQyY,EAC5C,CAmI4BC,GA/W5B,WACI,IAAIC,EAAuB34B,SAASrB,eAAe,wBAEnD,GAA6B,OAAzBg6B,EAA+B,EAC/BA,EAAuB34B,SAASlB,cAAc,QACzBwC,GAAK,uBAC1Bq3B,EAAqB7sB,UAAY,uBAKjC,IAHA,IAAI8sB,EAAuB,GACvBC,EAA0B,CAAC,EAC3BC,EAAe,EACVz2B,EAAI,EAAGA,EAAIsxB,GAAyB11B,OAAQoE,IAAK,CACtD,IAAI02B,EAA2BpF,GAAyBtxB,GACpD22B,EAA2BD,EAAyBvB,OACpDyB,EAA2BF,EAAyBG,OAEpDC,GAD4BJ,EAAyBK,QAC3BL,EAAyB7M,OAEvD4M,IACAD,EAAwBC,GAAgB,CAAE5M,IAAKiN,GAC/CP,GAAwB,kCAAoCE,EAAe,aAAeA,EAAe,2CAA6CG,EAA2B,iCAAmCD,EAA2B,iBACnP,CAEA,IAAIK,EAAcr5B,SAASlB,cAAc,OACzCu6B,EAAYvtB,UAAY,cACxButB,EAAY7D,UAAY,6CAA+ChB,EAAE,qBAAqBtsB,QAAU,ylBAA2lB0wB,EAAuB,qHAC1tBD,EAAqBxD,YAAYkE,GAEjC7E,EAAE,8BAA8BxU,QAAQ2Y,GAExC,IAAMZ,EAAYnE,GAAe,WAAa,QAC1C0F,EAAgB,EAChBC,EAAe,CAAC,EAChBC,EAAe5F,GAAe,EAAI,EAEtC2F,EAAa,GAAK,EAClB,IAAK,IAAIl3B,EAAI,EAAGA,EAAIy2B,EAAcz2B,IAAK,CACnC,IAAIo3B,EAAYjF,EAAE,uBAAyBnyB,GAAGq3B,aAEpC,IAANr3B,GAAiB,IAANA,IAIL,IAANA,GACAmyB,EAAE,6BAA6BtsB,MAAMuxB,GAGzCF,EAAal3B,EAAI,GAAKo3B,EAAYH,EAAgBE,EAClDF,GAAiBG,EACrB,CAEAjF,EAAE,UAAU1qB,GAAGiuB,EAAW,WACtB,IAAM4B,EAAStrB,SAASmmB,EAAE9zB,MAAMs2B,KAAK,WACjCgB,EAAch4B,SAASrB,eAAe,eAI1C,GAFA61B,EAAE,QAAQE,IAAI,aAAc,SAEb,IAAXiF,EACAnF,EAAE,uBAAuBE,IAAI,UAAW,IACxCF,EAAE,mBAAmBE,IAAI,UAAW,QACpCF,EAAE,mBAAmBE,IAAI,UAAW,QACpCF,EAAE,iCAAiCE,IAAI,UAAW,QAClDF,EAAE,wBAAwBE,IAAI,UAAW,QACzCF,EAAE,QAAQE,IAAI,aAAc,QAC5BsD,EAAYr6B,IAAM,QACf,GAAe,IAAXg8B,EAAc,CACrBnF,EAAE,uBAAuBE,IAAI,UAAW,QACxCF,EAAE,mBAAmBE,IAAI,UAAW,gBACpCF,EAAE,mBAAmBE,IAAI,UAAW,QACpCF,EAAE,iCAAiCE,IAAI,UAAW,QAClDF,EAAE,wBAAwBE,IAAI,UAAW,QAEzC,IAAMkF,EAAep4B,OAAO4pB,YAAcoJ,EAAE,mBAAmBqF,cAAgBrF,EAAE,yBAAyBqF,cAAgBrF,EAAE,mBAAmBqF,cAG/I,GAFArF,EAAE,gBAAgBE,IAAI,SAAUkF,EAAe,MAE3CpG,GAAeC,IAAoB,CACnC,IAAIwE,EAAgBzE,GAAeC,IAAmBuB,UAAU,SAE5DgD,EAAYr6B,MAAQs6B,IACpBD,EAAYr6B,IAAMs6B,EAE1B,CACJ,MAAO,GAAe,IAAX0B,EACPnF,EAAE,uBAAuBE,IAAI,UAAW,QACxCF,EAAE,mBAAmBE,IAAI,UAAW,QACpCF,EAAE,mBAAmBE,IAAI,UAAW,QACpCF,EAAE,iCAAiCE,IAAI,UAAW,QAClDF,EAAE,wBAAwBE,IAAI,UAAW,QACzCsD,EAAYr6B,IAAM,QACf,GAAe,IAAXg8B,EACPnF,EAAE,uBAAuBE,IAAI,UAAW,QACxCF,EAAE,mBAAmBE,IAAI,UAAW,QACpCF,EAAE,mBAAmBE,IAAI,UAAW,QACpCF,EAAE,iCAAiCE,IAAI,UAAW,QAClDF,EAAE,wBAAwBE,IAAI,UAAW,QACzCsD,EAAYr6B,IAAM,OACf,CACH,IAAMm8B,EAAmBjB,EAAwBc,GAEjD,GAAIG,EAAkB,CAClB,IAAIF,EAAep4B,OAAO4pB,YAAcoJ,EAAE,mBAAmBqF,cAAgBrF,EAAE,yBAAyBqF,cAAgBrF,EAAE,mBAAmBqF,cACzIE,EAAgB,EAChBC,EAAY,MACZC,EAAkBzF,EAAE,uBAAuBE,IAAI,UAErC,GAAViF,GACAC,EAAe,IACfK,EAAkB,KACD,GAAVN,GACPC,EAAeK,EAAkB,IACjCD,EAAY,OACK,GAAVL,GAEU,GAAVA,KADPI,EAAgB,IAKpBvF,EAAE,iCAAiCE,IAAI,iBAAkBqF,EAAgB,MACzEvF,EAAE,uBAAuBE,IAAI,iBAAkBqF,EAAgB,MAC/DvF,EAAE,uBAAuBwC,KAAK,YAAagD,GAE3CxF,EAAE,iCAAiCE,IAAI,SAAUuF,EAAkB,MACnEzF,EAAE,uBAAuBE,IAAI,SAAUkF,EAAe,MAEtD,IAAIM,EAAqBl6B,SAASrB,eAAe,sBACjD61B,EAAE,uBAAuBE,IAAI,UAAW,QACxCF,EAAE,mBAAmBE,IAAI,UAAW,QACpCF,EAAE,mBAAmBE,IAAI,UAAW,QACpCF,EAAE,iCAAiCE,IAAI,UAAW,gBAClDF,EAAE,wBAAwBE,IAAI,UAAW,QAEzCwF,EAAmBv8B,IAAMm8B,EAAiB5N,GAC9C,CACJ,CAEAsI,EAAE,6BAA6BtsB,MAAMssB,EAAE9zB,MAAMg5B,mBAEhBt3B,IAAzBm3B,EAAaI,IACbnF,EAAE,6BAA6BE,IAAI,OAAQ6E,EAAaI,GAEhE,EAEJ,CACJ,CAiO4BQ,GArXxB3F,EAAE,0BAA0B4F,SAAS,sBACrC5F,EAAE,uBAAuBzS,SAAS/C,OAAOwV,EAAE,2BAC3CA,EAAE,0BAA0BE,IAAI,QAAS,SAuXhBP,IAAAA,QAAYkG,MAnIT,QAFxBzD,EAAsB52B,SAASrB,eAAe,mCAG9Ci4B,EAAsB52B,SAASlB,cAAc,QACzBwC,GAAK,8BACzBs1B,EAAoB33B,MAAMq7B,QAAU,eACpC1D,EAAoB33B,MAAMsR,UAAY,MACtCqmB,EAAoBpB,UAAY,6GAEhChB,EAAE,mBAAmBxd,KAAK,oBAAoBgJ,QAAQ4W,IAgI9C,EAAG,IACP,CAER,EAhkBc,GAikBlB,CA/iBgB2D,CAAsBlG,GAGlC,EAKJ,E", "sources": ["webpack://@justoverclock/header-slideshow/webpack/bootstrap", "webpack://@justoverclock/header-slideshow/webpack/runtime/compat get default export", "webpack://@justoverclock/header-slideshow/webpack/runtime/define property getters", "webpack://@justoverclock/header-slideshow/webpack/runtime/hasOwnProperty shorthand", "webpack://@justoverclock/header-slideshow/external root \"flarum.core.compat['common/extend']\"", "webpack://@justoverclock/header-slideshow/external root \"flarum.core.compat['forum/app']\"", "webpack://@justoverclock/header-slideshow/external root \"flarum.core.compat['forum/components/HeaderPrimary']\"", "webpack://@justoverclock/header-slideshow/./node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/shared/ssr-window.esm.mjs", "webpack://@justoverclock/header-slideshow/./node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/shared/utils.mjs", "webpack://@justoverclock/header-slideshow/./node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/shared/swiper-core.mjs", "webpack://@justoverclock/header-slideshow/./src/forum/index.ts"], "sourcesContent": ["// The require scope\nvar __webpack_require__ = {};\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['common/extend'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['forum/app'];", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['forum/components/HeaderPrimary'];", "/**\n * SSR Window 5.0.1\n * Better handling for window object in SSR environment\n * https://github.com/nolimits4web/ssr-window\n *\n * Copyright 2025, <PERSON>\n *\n * Licensed under MIT\n *\n * Released on: June 27, 2025\n */\n/* eslint-disable no-param-reassign */\nfunction isObject(obj) {\n  return obj !== null && typeof obj === 'object' && 'constructor' in obj && obj.constructor === Object;\n}\nfunction extend(target, src) {\n  if (target === void 0) {\n    target = {};\n  }\n  if (src === void 0) {\n    src = {};\n  }\n  const noExtend = ['__proto__', 'constructor', 'prototype'];\n  Object.keys(src).filter(key => noExtend.indexOf(key) < 0).forEach(key => {\n    if (typeof target[key] === 'undefined') target[key] = src[key];else if (isObject(src[key]) && isObject(target[key]) && Object.keys(src[key]).length > 0) {\n      extend(target[key], src[key]);\n    }\n  });\n}\nconst ssrDocument = {\n  body: {},\n  addEventListener() {},\n  removeEventListener() {},\n  activeElement: {\n    blur() {},\n    nodeName: ''\n  },\n  querySelector() {\n    return null;\n  },\n  querySelectorAll() {\n    return [];\n  },\n  getElementById() {\n    return null;\n  },\n  createEvent() {\n    return {\n      initEvent() {}\n    };\n  },\n  createElement() {\n    return {\n      children: [],\n      childNodes: [],\n      style: {},\n      setAttribute() {},\n      getElementsByTagName() {\n        return [];\n      }\n    };\n  },\n  createElementNS() {\n    return {};\n  },\n  importNode() {\n    return null;\n  },\n  location: {\n    hash: '',\n    host: '',\n    hostname: '',\n    href: '',\n    origin: '',\n    pathname: '',\n    protocol: '',\n    search: ''\n  }\n};\nfunction getDocument() {\n  const doc = typeof document !== 'undefined' ? document : {};\n  extend(doc, ssrDocument);\n  return doc;\n}\nconst ssrWindow = {\n  document: ssrDocument,\n  navigator: {\n    userAgent: ''\n  },\n  location: {\n    hash: '',\n    host: '',\n    hostname: '',\n    href: '',\n    origin: '',\n    pathname: '',\n    protocol: '',\n    search: ''\n  },\n  history: {\n    replaceState() {},\n    pushState() {},\n    go() {},\n    back() {}\n  },\n  CustomEvent: function CustomEvent() {\n    return this;\n  },\n  addEventListener() {},\n  removeEventListener() {},\n  getComputedStyle() {\n    return {\n      getPropertyValue() {\n        return '';\n      }\n    };\n  },\n  Image() {},\n  Date() {},\n  screen: {},\n  setTimeout() {},\n  clearTimeout() {},\n  matchMedia() {\n    return {};\n  },\n  requestAnimationFrame(callback) {\n    if (typeof setTimeout === 'undefined') {\n      callback();\n      return null;\n    }\n    return setTimeout(callback, 0);\n  },\n  cancelAnimationFrame(id) {\n    if (typeof setTimeout === 'undefined') {\n      return;\n    }\n    clearTimeout(id);\n  }\n};\nfunction getWindow() {\n  const win = typeof window !== 'undefined' ? window : {};\n  extend(win, ssrWindow);\n  return win;\n}\n\nexport { getWindow as a, getDocument as g };\n", "import { a as getWindow, g as getDocument } from './ssr-window.esm.mjs';\n\nfunction classesToTokens(classes) {\n  if (classes === void 0) {\n    classes = '';\n  }\n  return classes.trim().split(' ').filter(c => !!c.trim());\n}\n\nfunction deleteProps(obj) {\n  const object = obj;\n  Object.keys(object).forEach(key => {\n    try {\n      object[key] = null;\n    } catch (e) {\n      // no getter for object\n    }\n    try {\n      delete object[key];\n    } catch (e) {\n      // something got wrong\n    }\n  });\n}\nfunction nextTick(callback, delay) {\n  if (delay === void 0) {\n    delay = 0;\n  }\n  return setTimeout(callback, delay);\n}\nfunction now() {\n  return Date.now();\n}\nfunction getComputedStyle(el) {\n  const window = getWindow();\n  let style;\n  if (window.getComputedStyle) {\n    style = window.getComputedStyle(el, null);\n  }\n  if (!style && el.currentStyle) {\n    style = el.currentStyle;\n  }\n  if (!style) {\n    style = el.style;\n  }\n  return style;\n}\nfunction getTranslate(el, axis) {\n  if (axis === void 0) {\n    axis = 'x';\n  }\n  const window = getWindow();\n  let matrix;\n  let curTransform;\n  let transformMatrix;\n  const curStyle = getComputedStyle(el);\n  if (window.WebKitCSSMatrix) {\n    curTransform = curStyle.transform || curStyle.webkitTransform;\n    if (curTransform.split(',').length > 6) {\n      curTransform = curTransform.split(', ').map(a => a.replace(',', '.')).join(', ');\n    }\n    // Some old versions of Webkit choke when 'none' is passed; pass\n    // empty string instead in this case\n    transformMatrix = new window.WebKitCSSMatrix(curTransform === 'none' ? '' : curTransform);\n  } else {\n    transformMatrix = curStyle.MozTransform || curStyle.OTransform || curStyle.MsTransform || curStyle.msTransform || curStyle.transform || curStyle.getPropertyValue('transform').replace('translate(', 'matrix(1, 0, 0, 1,');\n    matrix = transformMatrix.toString().split(',');\n  }\n  if (axis === 'x') {\n    // Latest Chrome and webkits Fix\n    if (window.WebKitCSSMatrix) curTransform = transformMatrix.m41;\n    // Crazy IE10 Matrix\n    else if (matrix.length === 16) curTransform = parseFloat(matrix[12]);\n    // Normal Browsers\n    else curTransform = parseFloat(matrix[4]);\n  }\n  if (axis === 'y') {\n    // Latest Chrome and webkits Fix\n    if (window.WebKitCSSMatrix) curTransform = transformMatrix.m42;\n    // Crazy IE10 Matrix\n    else if (matrix.length === 16) curTransform = parseFloat(matrix[13]);\n    // Normal Browsers\n    else curTransform = parseFloat(matrix[5]);\n  }\n  return curTransform || 0;\n}\nfunction isObject(o) {\n  return typeof o === 'object' && o !== null && o.constructor && Object.prototype.toString.call(o).slice(8, -1) === 'Object';\n}\nfunction isNode(node) {\n  // eslint-disable-next-line\n  if (typeof window !== 'undefined' && typeof window.HTMLElement !== 'undefined') {\n    return node instanceof HTMLElement;\n  }\n  return node && (node.nodeType === 1 || node.nodeType === 11);\n}\nfunction extend() {\n  const to = Object(arguments.length <= 0 ? undefined : arguments[0]);\n  const noExtend = ['__proto__', 'constructor', 'prototype'];\n  for (let i = 1; i < arguments.length; i += 1) {\n    const nextSource = i < 0 || arguments.length <= i ? undefined : arguments[i];\n    if (nextSource !== undefined && nextSource !== null && !isNode(nextSource)) {\n      const keysArray = Object.keys(Object(nextSource)).filter(key => noExtend.indexOf(key) < 0);\n      for (let nextIndex = 0, len = keysArray.length; nextIndex < len; nextIndex += 1) {\n        const nextKey = keysArray[nextIndex];\n        const desc = Object.getOwnPropertyDescriptor(nextSource, nextKey);\n        if (desc !== undefined && desc.enumerable) {\n          if (isObject(to[nextKey]) && isObject(nextSource[nextKey])) {\n            if (nextSource[nextKey].__swiper__) {\n              to[nextKey] = nextSource[nextKey];\n            } else {\n              extend(to[nextKey], nextSource[nextKey]);\n            }\n          } else if (!isObject(to[nextKey]) && isObject(nextSource[nextKey])) {\n            to[nextKey] = {};\n            if (nextSource[nextKey].__swiper__) {\n              to[nextKey] = nextSource[nextKey];\n            } else {\n              extend(to[nextKey], nextSource[nextKey]);\n            }\n          } else {\n            to[nextKey] = nextSource[nextKey];\n          }\n        }\n      }\n    }\n  }\n  return to;\n}\nfunction setCSSProperty(el, varName, varValue) {\n  el.style.setProperty(varName, varValue);\n}\nfunction animateCSSModeScroll(_ref) {\n  let {\n    swiper,\n    targetPosition,\n    side\n  } = _ref;\n  const window = getWindow();\n  const startPosition = -swiper.translate;\n  let startTime = null;\n  let time;\n  const duration = swiper.params.speed;\n  swiper.wrapperEl.style.scrollSnapType = 'none';\n  window.cancelAnimationFrame(swiper.cssModeFrameID);\n  const dir = targetPosition > startPosition ? 'next' : 'prev';\n  const isOutOfBound = (current, target) => {\n    return dir === 'next' && current >= target || dir === 'prev' && current <= target;\n  };\n  const animate = () => {\n    time = new Date().getTime();\n    if (startTime === null) {\n      startTime = time;\n    }\n    const progress = Math.max(Math.min((time - startTime) / duration, 1), 0);\n    const easeProgress = 0.5 - Math.cos(progress * Math.PI) / 2;\n    let currentPosition = startPosition + easeProgress * (targetPosition - startPosition);\n    if (isOutOfBound(currentPosition, targetPosition)) {\n      currentPosition = targetPosition;\n    }\n    swiper.wrapperEl.scrollTo({\n      [side]: currentPosition\n    });\n    if (isOutOfBound(currentPosition, targetPosition)) {\n      swiper.wrapperEl.style.overflow = 'hidden';\n      swiper.wrapperEl.style.scrollSnapType = '';\n      setTimeout(() => {\n        swiper.wrapperEl.style.overflow = '';\n        swiper.wrapperEl.scrollTo({\n          [side]: currentPosition\n        });\n      });\n      window.cancelAnimationFrame(swiper.cssModeFrameID);\n      return;\n    }\n    swiper.cssModeFrameID = window.requestAnimationFrame(animate);\n  };\n  animate();\n}\nfunction getSlideTransformEl(slideEl) {\n  return slideEl.querySelector('.swiper-slide-transform') || slideEl.shadowRoot && slideEl.shadowRoot.querySelector('.swiper-slide-transform') || slideEl;\n}\nfunction elementChildren(element, selector) {\n  if (selector === void 0) {\n    selector = '';\n  }\n  const window = getWindow();\n  const children = [...element.children];\n  if (window.HTMLSlotElement && element instanceof HTMLSlotElement) {\n    children.push(...element.assignedElements());\n  }\n  if (!selector) {\n    return children;\n  }\n  return children.filter(el => el.matches(selector));\n}\nfunction elementIsChildOfSlot(el, slot) {\n  // Breadth-first search through all parent's children and assigned elements\n  const elementsQueue = [slot];\n  while (elementsQueue.length > 0) {\n    const elementToCheck = elementsQueue.shift();\n    if (el === elementToCheck) {\n      return true;\n    }\n    elementsQueue.push(...elementToCheck.children, ...(elementToCheck.shadowRoot ? elementToCheck.shadowRoot.children : []), ...(elementToCheck.assignedElements ? elementToCheck.assignedElements() : []));\n  }\n}\nfunction elementIsChildOf(el, parent) {\n  const window = getWindow();\n  let isChild = parent.contains(el);\n  if (!isChild && window.HTMLSlotElement && parent instanceof HTMLSlotElement) {\n    const children = [...parent.assignedElements()];\n    isChild = children.includes(el);\n    if (!isChild) {\n      isChild = elementIsChildOfSlot(el, parent);\n    }\n  }\n  return isChild;\n}\nfunction showWarning(text) {\n  try {\n    console.warn(text);\n    return;\n  } catch (err) {\n    // err\n  }\n}\nfunction createElement(tag, classes) {\n  if (classes === void 0) {\n    classes = [];\n  }\n  const el = document.createElement(tag);\n  el.classList.add(...(Array.isArray(classes) ? classes : classesToTokens(classes)));\n  return el;\n}\nfunction elementOffset(el) {\n  const window = getWindow();\n  const document = getDocument();\n  const box = el.getBoundingClientRect();\n  const body = document.body;\n  const clientTop = el.clientTop || body.clientTop || 0;\n  const clientLeft = el.clientLeft || body.clientLeft || 0;\n  const scrollTop = el === window ? window.scrollY : el.scrollTop;\n  const scrollLeft = el === window ? window.scrollX : el.scrollLeft;\n  return {\n    top: box.top + scrollTop - clientTop,\n    left: box.left + scrollLeft - clientLeft\n  };\n}\nfunction elementPrevAll(el, selector) {\n  const prevEls = [];\n  while (el.previousElementSibling) {\n    const prev = el.previousElementSibling; // eslint-disable-line\n    if (selector) {\n      if (prev.matches(selector)) prevEls.push(prev);\n    } else prevEls.push(prev);\n    el = prev;\n  }\n  return prevEls;\n}\nfunction elementNextAll(el, selector) {\n  const nextEls = [];\n  while (el.nextElementSibling) {\n    const next = el.nextElementSibling; // eslint-disable-line\n    if (selector) {\n      if (next.matches(selector)) nextEls.push(next);\n    } else nextEls.push(next);\n    el = next;\n  }\n  return nextEls;\n}\nfunction elementStyle(el, prop) {\n  const window = getWindow();\n  return window.getComputedStyle(el, null).getPropertyValue(prop);\n}\nfunction elementIndex(el) {\n  let child = el;\n  let i;\n  if (child) {\n    i = 0;\n    // eslint-disable-next-line\n    while ((child = child.previousSibling) !== null) {\n      if (child.nodeType === 1) i += 1;\n    }\n    return i;\n  }\n  return undefined;\n}\nfunction elementParents(el, selector) {\n  const parents = []; // eslint-disable-line\n  let parent = el.parentElement; // eslint-disable-line\n  while (parent) {\n    if (selector) {\n      if (parent.matches(selector)) parents.push(parent);\n    } else {\n      parents.push(parent);\n    }\n    parent = parent.parentElement;\n  }\n  return parents;\n}\nfunction elementTransitionEnd(el, callback) {\n  function fireCallBack(e) {\n    if (e.target !== el) return;\n    callback.call(el, e);\n    el.removeEventListener('transitionend', fireCallBack);\n  }\n  if (callback) {\n    el.addEventListener('transitionend', fireCallBack);\n  }\n}\nfunction elementOuterSize(el, size, includeMargins) {\n  const window = getWindow();\n  if (includeMargins) {\n    return el[size === 'width' ? 'offsetWidth' : 'offsetHeight'] + parseFloat(window.getComputedStyle(el, null).getPropertyValue(size === 'width' ? 'margin-right' : 'margin-top')) + parseFloat(window.getComputedStyle(el, null).getPropertyValue(size === 'width' ? 'margin-left' : 'margin-bottom'));\n  }\n  return el.offsetWidth;\n}\nfunction makeElementsArray(el) {\n  return (Array.isArray(el) ? el : [el]).filter(e => !!e);\n}\nfunction getRotateFix(swiper) {\n  return v => {\n    if (Math.abs(v) > 0 && swiper.browser && swiper.browser.need3dFix && Math.abs(v) % 90 === 0) {\n      return v + 0.001;\n    }\n    return v;\n  };\n}\nfunction setInnerHTML(el, html) {\n  if (html === void 0) {\n    html = '';\n  }\n  if (typeof trustedTypes !== 'undefined') {\n    el.innerHTML = trustedTypes.createPolicy('html', {\n      createHTML: s => s\n    }).createHTML(html);\n  } else {\n    el.innerHTML = html;\n  }\n}\n\nexport { setCSSProperty as a, elementParents as b, createElement as c, elementOffset as d, elementChildren as e, now as f, getSlideTransformEl as g, elementOuterSize as h, elementIndex as i, classesToTokens as j, getTranslate as k, elementTransitionEnd as l, makeElementsArray as m, nextTick as n, isObject as o, getRotateFix as p, elementStyle as q, elementNextAll as r, setInnerHTML as s, elementPrevAll as t, animateCSSModeScroll as u, showWarning as v, elementIsChildOf as w, extend as x, deleteProps as y };\n", "import { a as getWindow, g as getDocument } from './ssr-window.esm.mjs';\nimport { b as elementParents, q as elementStyle, e as elementChildren, a as setCSSProperty, h as elementOuterSize, r as elementNextAll, t as elementPrevAll, k as getTranslate, u as animateCSSModeScroll, n as nextTick, v as showWarning, c as createElement, w as elementIsChildOf, f as now, x as extend, i as elementIndex, y as deleteProps } from './utils.mjs';\n\nlet support;\nfunction calcSupport() {\n  const window = getWindow();\n  const document = getDocument();\n  return {\n    smoothScroll: document.documentElement && document.documentElement.style && 'scrollBehavior' in document.documentElement.style,\n    touch: !!('ontouchstart' in window || window.DocumentTouch && document instanceof window.DocumentTouch)\n  };\n}\nfunction getSupport() {\n  if (!support) {\n    support = calcSupport();\n  }\n  return support;\n}\n\nlet deviceCached;\nfunction calcDevice(_temp) {\n  let {\n    userAgent\n  } = _temp === void 0 ? {} : _temp;\n  const support = getSupport();\n  const window = getWindow();\n  const platform = window.navigator.platform;\n  const ua = userAgent || window.navigator.userAgent;\n  const device = {\n    ios: false,\n    android: false\n  };\n  const screenWidth = window.screen.width;\n  const screenHeight = window.screen.height;\n  const android = ua.match(/(Android);?[\\s\\/]+([\\d.]+)?/); // eslint-disable-line\n  let ipad = ua.match(/(iPad).*OS\\s([\\d_]+)/);\n  const ipod = ua.match(/(iPod)(.*OS\\s([\\d_]+))?/);\n  const iphone = !ipad && ua.match(/(iPhone\\sOS|iOS)\\s([\\d_]+)/);\n  const windows = platform === 'Win32';\n  let macos = platform === 'MacIntel';\n\n  // iPadOs 13 fix\n  const iPadScreens = ['1024x1366', '1366x1024', '834x1194', '1194x834', '834x1112', '1112x834', '768x1024', '1024x768', '820x1180', '1180x820', '810x1080', '1080x810'];\n  if (!ipad && macos && support.touch && iPadScreens.indexOf(`${screenWidth}x${screenHeight}`) >= 0) {\n    ipad = ua.match(/(Version)\\/([\\d.]+)/);\n    if (!ipad) ipad = [0, 1, '13_0_0'];\n    macos = false;\n  }\n\n  // Android\n  if (android && !windows) {\n    device.os = 'android';\n    device.android = true;\n  }\n  if (ipad || iphone || ipod) {\n    device.os = 'ios';\n    device.ios = true;\n  }\n\n  // Export object\n  return device;\n}\nfunction getDevice(overrides) {\n  if (overrides === void 0) {\n    overrides = {};\n  }\n  if (!deviceCached) {\n    deviceCached = calcDevice(overrides);\n  }\n  return deviceCached;\n}\n\nlet browser;\nfunction calcBrowser() {\n  const window = getWindow();\n  const device = getDevice();\n  let needPerspectiveFix = false;\n  function isSafari() {\n    const ua = window.navigator.userAgent.toLowerCase();\n    return ua.indexOf('safari') >= 0 && ua.indexOf('chrome') < 0 && ua.indexOf('android') < 0;\n  }\n  if (isSafari()) {\n    const ua = String(window.navigator.userAgent);\n    if (ua.includes('Version/')) {\n      const [major, minor] = ua.split('Version/')[1].split(' ')[0].split('.').map(num => Number(num));\n      needPerspectiveFix = major < 16 || major === 16 && minor < 2;\n    }\n  }\n  const isWebView = /(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(window.navigator.userAgent);\n  const isSafariBrowser = isSafari();\n  const need3dFix = isSafariBrowser || isWebView && device.ios;\n  return {\n    isSafari: needPerspectiveFix || isSafariBrowser,\n    needPerspectiveFix,\n    need3dFix,\n    isWebView\n  };\n}\nfunction getBrowser() {\n  if (!browser) {\n    browser = calcBrowser();\n  }\n  return browser;\n}\n\nfunction Resize(_ref) {\n  let {\n    swiper,\n    on,\n    emit\n  } = _ref;\n  const window = getWindow();\n  let observer = null;\n  let animationFrame = null;\n  const resizeHandler = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    emit('beforeResize');\n    emit('resize');\n  };\n  const createObserver = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    observer = new ResizeObserver(entries => {\n      animationFrame = window.requestAnimationFrame(() => {\n        const {\n          width,\n          height\n        } = swiper;\n        let newWidth = width;\n        let newHeight = height;\n        entries.forEach(_ref2 => {\n          let {\n            contentBoxSize,\n            contentRect,\n            target\n          } = _ref2;\n          if (target && target !== swiper.el) return;\n          newWidth = contentRect ? contentRect.width : (contentBoxSize[0] || contentBoxSize).inlineSize;\n          newHeight = contentRect ? contentRect.height : (contentBoxSize[0] || contentBoxSize).blockSize;\n        });\n        if (newWidth !== width || newHeight !== height) {\n          resizeHandler();\n        }\n      });\n    });\n    observer.observe(swiper.el);\n  };\n  const removeObserver = () => {\n    if (animationFrame) {\n      window.cancelAnimationFrame(animationFrame);\n    }\n    if (observer && observer.unobserve && swiper.el) {\n      observer.unobserve(swiper.el);\n      observer = null;\n    }\n  };\n  const orientationChangeHandler = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    emit('orientationchange');\n  };\n  on('init', () => {\n    if (swiper.params.resizeObserver && typeof window.ResizeObserver !== 'undefined') {\n      createObserver();\n      return;\n    }\n    window.addEventListener('resize', resizeHandler);\n    window.addEventListener('orientationchange', orientationChangeHandler);\n  });\n  on('destroy', () => {\n    removeObserver();\n    window.removeEventListener('resize', resizeHandler);\n    window.removeEventListener('orientationchange', orientationChangeHandler);\n  });\n}\n\nfunction Observer(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  const observers = [];\n  const window = getWindow();\n  const attach = function (target, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    const ObserverFunc = window.MutationObserver || window.WebkitMutationObserver;\n    const observer = new ObserverFunc(mutations => {\n      // The observerUpdate event should only be triggered\n      // once despite the number of mutations.  Additional\n      // triggers are redundant and are very costly\n      if (swiper.__preventObserver__) return;\n      if (mutations.length === 1) {\n        emit('observerUpdate', mutations[0]);\n        return;\n      }\n      const observerUpdate = function observerUpdate() {\n        emit('observerUpdate', mutations[0]);\n      };\n      if (window.requestAnimationFrame) {\n        window.requestAnimationFrame(observerUpdate);\n      } else {\n        window.setTimeout(observerUpdate, 0);\n      }\n    });\n    observer.observe(target, {\n      attributes: typeof options.attributes === 'undefined' ? true : options.attributes,\n      childList: swiper.isElement || (typeof options.childList === 'undefined' ? true : options).childList,\n      characterData: typeof options.characterData === 'undefined' ? true : options.characterData\n    });\n    observers.push(observer);\n  };\n  const init = () => {\n    if (!swiper.params.observer) return;\n    if (swiper.params.observeParents) {\n      const containerParents = elementParents(swiper.hostEl);\n      for (let i = 0; i < containerParents.length; i += 1) {\n        attach(containerParents[i]);\n      }\n    }\n    // Observe container\n    attach(swiper.hostEl, {\n      childList: swiper.params.observeSlideChildren\n    });\n\n    // Observe wrapper\n    attach(swiper.wrapperEl, {\n      attributes: false\n    });\n  };\n  const destroy = () => {\n    observers.forEach(observer => {\n      observer.disconnect();\n    });\n    observers.splice(0, observers.length);\n  };\n  extendParams({\n    observer: false,\n    observeParents: false,\n    observeSlideChildren: false\n  });\n  on('init', init);\n  on('destroy', destroy);\n}\n\n/* eslint-disable no-underscore-dangle */\n\nvar eventsEmitter = {\n  on(events, handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    const method = priority ? 'unshift' : 'push';\n    events.split(' ').forEach(event => {\n      if (!self.eventsListeners[event]) self.eventsListeners[event] = [];\n      self.eventsListeners[event][method](handler);\n    });\n    return self;\n  },\n  once(events, handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    function onceHandler() {\n      self.off(events, onceHandler);\n      if (onceHandler.__emitterProxy) {\n        delete onceHandler.__emitterProxy;\n      }\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      handler.apply(self, args);\n    }\n    onceHandler.__emitterProxy = handler;\n    return self.on(events, onceHandler, priority);\n  },\n  onAny(handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    const method = priority ? 'unshift' : 'push';\n    if (self.eventsAnyListeners.indexOf(handler) < 0) {\n      self.eventsAnyListeners[method](handler);\n    }\n    return self;\n  },\n  offAny(handler) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsAnyListeners) return self;\n    const index = self.eventsAnyListeners.indexOf(handler);\n    if (index >= 0) {\n      self.eventsAnyListeners.splice(index, 1);\n    }\n    return self;\n  },\n  off(events, handler) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsListeners) return self;\n    events.split(' ').forEach(event => {\n      if (typeof handler === 'undefined') {\n        self.eventsListeners[event] = [];\n      } else if (self.eventsListeners[event]) {\n        self.eventsListeners[event].forEach((eventHandler, index) => {\n          if (eventHandler === handler || eventHandler.__emitterProxy && eventHandler.__emitterProxy === handler) {\n            self.eventsListeners[event].splice(index, 1);\n          }\n        });\n      }\n    });\n    return self;\n  },\n  emit() {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsListeners) return self;\n    let events;\n    let data;\n    let context;\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    if (typeof args[0] === 'string' || Array.isArray(args[0])) {\n      events = args[0];\n      data = args.slice(1, args.length);\n      context = self;\n    } else {\n      events = args[0].events;\n      data = args[0].data;\n      context = args[0].context || self;\n    }\n    data.unshift(context);\n    const eventsArray = Array.isArray(events) ? events : events.split(' ');\n    eventsArray.forEach(event => {\n      if (self.eventsAnyListeners && self.eventsAnyListeners.length) {\n        self.eventsAnyListeners.forEach(eventHandler => {\n          eventHandler.apply(context, [event, ...data]);\n        });\n      }\n      if (self.eventsListeners && self.eventsListeners[event]) {\n        self.eventsListeners[event].forEach(eventHandler => {\n          eventHandler.apply(context, data);\n        });\n      }\n    });\n    return self;\n  }\n};\n\nfunction updateSize() {\n  const swiper = this;\n  let width;\n  let height;\n  const el = swiper.el;\n  if (typeof swiper.params.width !== 'undefined' && swiper.params.width !== null) {\n    width = swiper.params.width;\n  } else {\n    width = el.clientWidth;\n  }\n  if (typeof swiper.params.height !== 'undefined' && swiper.params.height !== null) {\n    height = swiper.params.height;\n  } else {\n    height = el.clientHeight;\n  }\n  if (width === 0 && swiper.isHorizontal() || height === 0 && swiper.isVertical()) {\n    return;\n  }\n\n  // Subtract paddings\n  width = width - parseInt(elementStyle(el, 'padding-left') || 0, 10) - parseInt(elementStyle(el, 'padding-right') || 0, 10);\n  height = height - parseInt(elementStyle(el, 'padding-top') || 0, 10) - parseInt(elementStyle(el, 'padding-bottom') || 0, 10);\n  if (Number.isNaN(width)) width = 0;\n  if (Number.isNaN(height)) height = 0;\n  Object.assign(swiper, {\n    width,\n    height,\n    size: swiper.isHorizontal() ? width : height\n  });\n}\n\nfunction updateSlides() {\n  const swiper = this;\n  function getDirectionPropertyValue(node, label) {\n    return parseFloat(node.getPropertyValue(swiper.getDirectionLabel(label)) || 0);\n  }\n  const params = swiper.params;\n  const {\n    wrapperEl,\n    slidesEl,\n    size: swiperSize,\n    rtlTranslate: rtl,\n    wrongRTL\n  } = swiper;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  const previousSlidesLength = isVirtual ? swiper.virtual.slides.length : swiper.slides.length;\n  const slides = elementChildren(slidesEl, `.${swiper.params.slideClass}, swiper-slide`);\n  const slidesLength = isVirtual ? swiper.virtual.slides.length : slides.length;\n  let snapGrid = [];\n  const slidesGrid = [];\n  const slidesSizesGrid = [];\n  let offsetBefore = params.slidesOffsetBefore;\n  if (typeof offsetBefore === 'function') {\n    offsetBefore = params.slidesOffsetBefore.call(swiper);\n  }\n  let offsetAfter = params.slidesOffsetAfter;\n  if (typeof offsetAfter === 'function') {\n    offsetAfter = params.slidesOffsetAfter.call(swiper);\n  }\n  const previousSnapGridLength = swiper.snapGrid.length;\n  const previousSlidesGridLength = swiper.slidesGrid.length;\n  let spaceBetween = params.spaceBetween;\n  let slidePosition = -offsetBefore;\n  let prevSlideSize = 0;\n  let index = 0;\n  if (typeof swiperSize === 'undefined') {\n    return;\n  }\n  if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n    spaceBetween = parseFloat(spaceBetween.replace('%', '')) / 100 * swiperSize;\n  } else if (typeof spaceBetween === 'string') {\n    spaceBetween = parseFloat(spaceBetween);\n  }\n  swiper.virtualSize = -spaceBetween;\n\n  // reset margins\n  slides.forEach(slideEl => {\n    if (rtl) {\n      slideEl.style.marginLeft = '';\n    } else {\n      slideEl.style.marginRight = '';\n    }\n    slideEl.style.marginBottom = '';\n    slideEl.style.marginTop = '';\n  });\n\n  // reset cssMode offsets\n  if (params.centeredSlides && params.cssMode) {\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-before', '');\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-after', '');\n  }\n  const gridEnabled = params.grid && params.grid.rows > 1 && swiper.grid;\n  if (gridEnabled) {\n    swiper.grid.initSlides(slides);\n  } else if (swiper.grid) {\n    swiper.grid.unsetSlides();\n  }\n\n  // Calc slides\n  let slideSize;\n  const shouldResetSlideSize = params.slidesPerView === 'auto' && params.breakpoints && Object.keys(params.breakpoints).filter(key => {\n    return typeof params.breakpoints[key].slidesPerView !== 'undefined';\n  }).length > 0;\n  for (let i = 0; i < slidesLength; i += 1) {\n    slideSize = 0;\n    let slide;\n    if (slides[i]) slide = slides[i];\n    if (gridEnabled) {\n      swiper.grid.updateSlide(i, slide, slides);\n    }\n    if (slides[i] && elementStyle(slide, 'display') === 'none') continue; // eslint-disable-line\n\n    if (params.slidesPerView === 'auto') {\n      if (shouldResetSlideSize) {\n        slides[i].style[swiper.getDirectionLabel('width')] = ``;\n      }\n      const slideStyles = getComputedStyle(slide);\n      const currentTransform = slide.style.transform;\n      const currentWebKitTransform = slide.style.webkitTransform;\n      if (currentTransform) {\n        slide.style.transform = 'none';\n      }\n      if (currentWebKitTransform) {\n        slide.style.webkitTransform = 'none';\n      }\n      if (params.roundLengths) {\n        slideSize = swiper.isHorizontal() ? elementOuterSize(slide, 'width', true) : elementOuterSize(slide, 'height', true);\n      } else {\n        // eslint-disable-next-line\n        const width = getDirectionPropertyValue(slideStyles, 'width');\n        const paddingLeft = getDirectionPropertyValue(slideStyles, 'padding-left');\n        const paddingRight = getDirectionPropertyValue(slideStyles, 'padding-right');\n        const marginLeft = getDirectionPropertyValue(slideStyles, 'margin-left');\n        const marginRight = getDirectionPropertyValue(slideStyles, 'margin-right');\n        const boxSizing = slideStyles.getPropertyValue('box-sizing');\n        if (boxSizing && boxSizing === 'border-box') {\n          slideSize = width + marginLeft + marginRight;\n        } else {\n          const {\n            clientWidth,\n            offsetWidth\n          } = slide;\n          slideSize = width + paddingLeft + paddingRight + marginLeft + marginRight + (offsetWidth - clientWidth);\n        }\n      }\n      if (currentTransform) {\n        slide.style.transform = currentTransform;\n      }\n      if (currentWebKitTransform) {\n        slide.style.webkitTransform = currentWebKitTransform;\n      }\n      if (params.roundLengths) slideSize = Math.floor(slideSize);\n    } else {\n      slideSize = (swiperSize - (params.slidesPerView - 1) * spaceBetween) / params.slidesPerView;\n      if (params.roundLengths) slideSize = Math.floor(slideSize);\n      if (slides[i]) {\n        slides[i].style[swiper.getDirectionLabel('width')] = `${slideSize}px`;\n      }\n    }\n    if (slides[i]) {\n      slides[i].swiperSlideSize = slideSize;\n    }\n    slidesSizesGrid.push(slideSize);\n    if (params.centeredSlides) {\n      slidePosition = slidePosition + slideSize / 2 + prevSlideSize / 2 + spaceBetween;\n      if (prevSlideSize === 0 && i !== 0) slidePosition = slidePosition - swiperSize / 2 - spaceBetween;\n      if (i === 0) slidePosition = slidePosition - swiperSize / 2 - spaceBetween;\n      if (Math.abs(slidePosition) < 1 / 1000) slidePosition = 0;\n      if (params.roundLengths) slidePosition = Math.floor(slidePosition);\n      if (index % params.slidesPerGroup === 0) snapGrid.push(slidePosition);\n      slidesGrid.push(slidePosition);\n    } else {\n      if (params.roundLengths) slidePosition = Math.floor(slidePosition);\n      if ((index - Math.min(swiper.params.slidesPerGroupSkip, index)) % swiper.params.slidesPerGroup === 0) snapGrid.push(slidePosition);\n      slidesGrid.push(slidePosition);\n      slidePosition = slidePosition + slideSize + spaceBetween;\n    }\n    swiper.virtualSize += slideSize + spaceBetween;\n    prevSlideSize = slideSize;\n    index += 1;\n  }\n  swiper.virtualSize = Math.max(swiper.virtualSize, swiperSize) + offsetAfter;\n  if (rtl && wrongRTL && (params.effect === 'slide' || params.effect === 'coverflow')) {\n    wrapperEl.style.width = `${swiper.virtualSize + spaceBetween}px`;\n  }\n  if (params.setWrapperSize) {\n    wrapperEl.style[swiper.getDirectionLabel('width')] = `${swiper.virtualSize + spaceBetween}px`;\n  }\n  if (gridEnabled) {\n    swiper.grid.updateWrapperSize(slideSize, snapGrid);\n  }\n\n  // Remove last grid elements depending on width\n  if (!params.centeredSlides) {\n    const newSlidesGrid = [];\n    for (let i = 0; i < snapGrid.length; i += 1) {\n      let slidesGridItem = snapGrid[i];\n      if (params.roundLengths) slidesGridItem = Math.floor(slidesGridItem);\n      if (snapGrid[i] <= swiper.virtualSize - swiperSize) {\n        newSlidesGrid.push(slidesGridItem);\n      }\n    }\n    snapGrid = newSlidesGrid;\n    if (Math.floor(swiper.virtualSize - swiperSize) - Math.floor(snapGrid[snapGrid.length - 1]) > 1) {\n      snapGrid.push(swiper.virtualSize - swiperSize);\n    }\n  }\n  if (isVirtual && params.loop) {\n    const size = slidesSizesGrid[0] + spaceBetween;\n    if (params.slidesPerGroup > 1) {\n      const groups = Math.ceil((swiper.virtual.slidesBefore + swiper.virtual.slidesAfter) / params.slidesPerGroup);\n      const groupSize = size * params.slidesPerGroup;\n      for (let i = 0; i < groups; i += 1) {\n        snapGrid.push(snapGrid[snapGrid.length - 1] + groupSize);\n      }\n    }\n    for (let i = 0; i < swiper.virtual.slidesBefore + swiper.virtual.slidesAfter; i += 1) {\n      if (params.slidesPerGroup === 1) {\n        snapGrid.push(snapGrid[snapGrid.length - 1] + size);\n      }\n      slidesGrid.push(slidesGrid[slidesGrid.length - 1] + size);\n      swiper.virtualSize += size;\n    }\n  }\n  if (snapGrid.length === 0) snapGrid = [0];\n  if (spaceBetween !== 0) {\n    const key = swiper.isHorizontal() && rtl ? 'marginLeft' : swiper.getDirectionLabel('marginRight');\n    slides.filter((_, slideIndex) => {\n      if (!params.cssMode || params.loop) return true;\n      if (slideIndex === slides.length - 1) {\n        return false;\n      }\n      return true;\n    }).forEach(slideEl => {\n      slideEl.style[key] = `${spaceBetween}px`;\n    });\n  }\n  if (params.centeredSlides && params.centeredSlidesBounds) {\n    let allSlidesSize = 0;\n    slidesSizesGrid.forEach(slideSizeValue => {\n      allSlidesSize += slideSizeValue + (spaceBetween || 0);\n    });\n    allSlidesSize -= spaceBetween;\n    const maxSnap = allSlidesSize > swiperSize ? allSlidesSize - swiperSize : 0;\n    snapGrid = snapGrid.map(snap => {\n      if (snap <= 0) return -offsetBefore;\n      if (snap > maxSnap) return maxSnap + offsetAfter;\n      return snap;\n    });\n  }\n  if (params.centerInsufficientSlides) {\n    let allSlidesSize = 0;\n    slidesSizesGrid.forEach(slideSizeValue => {\n      allSlidesSize += slideSizeValue + (spaceBetween || 0);\n    });\n    allSlidesSize -= spaceBetween;\n    const offsetSize = (params.slidesOffsetBefore || 0) + (params.slidesOffsetAfter || 0);\n    if (allSlidesSize + offsetSize < swiperSize) {\n      const allSlidesOffset = (swiperSize - allSlidesSize - offsetSize) / 2;\n      snapGrid.forEach((snap, snapIndex) => {\n        snapGrid[snapIndex] = snap - allSlidesOffset;\n      });\n      slidesGrid.forEach((snap, snapIndex) => {\n        slidesGrid[snapIndex] = snap + allSlidesOffset;\n      });\n    }\n  }\n  Object.assign(swiper, {\n    slides,\n    snapGrid,\n    slidesGrid,\n    slidesSizesGrid\n  });\n  if (params.centeredSlides && params.cssMode && !params.centeredSlidesBounds) {\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-before', `${-snapGrid[0]}px`);\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-after', `${swiper.size / 2 - slidesSizesGrid[slidesSizesGrid.length - 1] / 2}px`);\n    const addToSnapGrid = -swiper.snapGrid[0];\n    const addToSlidesGrid = -swiper.slidesGrid[0];\n    swiper.snapGrid = swiper.snapGrid.map(v => v + addToSnapGrid);\n    swiper.slidesGrid = swiper.slidesGrid.map(v => v + addToSlidesGrid);\n  }\n  if (slidesLength !== previousSlidesLength) {\n    swiper.emit('slidesLengthChange');\n  }\n  if (snapGrid.length !== previousSnapGridLength) {\n    if (swiper.params.watchOverflow) swiper.checkOverflow();\n    swiper.emit('snapGridLengthChange');\n  }\n  if (slidesGrid.length !== previousSlidesGridLength) {\n    swiper.emit('slidesGridLengthChange');\n  }\n  if (params.watchSlidesProgress) {\n    swiper.updateSlidesOffset();\n  }\n  swiper.emit('slidesUpdated');\n  if (!isVirtual && !params.cssMode && (params.effect === 'slide' || params.effect === 'fade')) {\n    const backFaceHiddenClass = `${params.containerModifierClass}backface-hidden`;\n    const hasClassBackfaceClassAdded = swiper.el.classList.contains(backFaceHiddenClass);\n    if (slidesLength <= params.maxBackfaceHiddenSlides) {\n      if (!hasClassBackfaceClassAdded) swiper.el.classList.add(backFaceHiddenClass);\n    } else if (hasClassBackfaceClassAdded) {\n      swiper.el.classList.remove(backFaceHiddenClass);\n    }\n  }\n}\n\nfunction updateAutoHeight(speed) {\n  const swiper = this;\n  const activeSlides = [];\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n  let newHeight = 0;\n  let i;\n  if (typeof speed === 'number') {\n    swiper.setTransition(speed);\n  } else if (speed === true) {\n    swiper.setTransition(swiper.params.speed);\n  }\n  const getSlideByIndex = index => {\n    if (isVirtual) {\n      return swiper.slides[swiper.getSlideIndexByData(index)];\n    }\n    return swiper.slides[index];\n  };\n  // Find slides currently in view\n  if (swiper.params.slidesPerView !== 'auto' && swiper.params.slidesPerView > 1) {\n    if (swiper.params.centeredSlides) {\n      (swiper.visibleSlides || []).forEach(slide => {\n        activeSlides.push(slide);\n      });\n    } else {\n      for (i = 0; i < Math.ceil(swiper.params.slidesPerView); i += 1) {\n        const index = swiper.activeIndex + i;\n        if (index > swiper.slides.length && !isVirtual) break;\n        activeSlides.push(getSlideByIndex(index));\n      }\n    }\n  } else {\n    activeSlides.push(getSlideByIndex(swiper.activeIndex));\n  }\n\n  // Find new height from highest slide in view\n  for (i = 0; i < activeSlides.length; i += 1) {\n    if (typeof activeSlides[i] !== 'undefined') {\n      const height = activeSlides[i].offsetHeight;\n      newHeight = height > newHeight ? height : newHeight;\n    }\n  }\n\n  // Update Height\n  if (newHeight || newHeight === 0) swiper.wrapperEl.style.height = `${newHeight}px`;\n}\n\nfunction updateSlidesOffset() {\n  const swiper = this;\n  const slides = swiper.slides;\n  // eslint-disable-next-line\n  const minusOffset = swiper.isElement ? swiper.isHorizontal() ? swiper.wrapperEl.offsetLeft : swiper.wrapperEl.offsetTop : 0;\n  for (let i = 0; i < slides.length; i += 1) {\n    slides[i].swiperSlideOffset = (swiper.isHorizontal() ? slides[i].offsetLeft : slides[i].offsetTop) - minusOffset - swiper.cssOverflowAdjustment();\n  }\n}\n\nconst toggleSlideClasses$1 = (slideEl, condition, className) => {\n  if (condition && !slideEl.classList.contains(className)) {\n    slideEl.classList.add(className);\n  } else if (!condition && slideEl.classList.contains(className)) {\n    slideEl.classList.remove(className);\n  }\n};\nfunction updateSlidesProgress(translate) {\n  if (translate === void 0) {\n    translate = this && this.translate || 0;\n  }\n  const swiper = this;\n  const params = swiper.params;\n  const {\n    slides,\n    rtlTranslate: rtl,\n    snapGrid\n  } = swiper;\n  if (slides.length === 0) return;\n  if (typeof slides[0].swiperSlideOffset === 'undefined') swiper.updateSlidesOffset();\n  let offsetCenter = -translate;\n  if (rtl) offsetCenter = translate;\n  swiper.visibleSlidesIndexes = [];\n  swiper.visibleSlides = [];\n  let spaceBetween = params.spaceBetween;\n  if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n    spaceBetween = parseFloat(spaceBetween.replace('%', '')) / 100 * swiper.size;\n  } else if (typeof spaceBetween === 'string') {\n    spaceBetween = parseFloat(spaceBetween);\n  }\n  for (let i = 0; i < slides.length; i += 1) {\n    const slide = slides[i];\n    let slideOffset = slide.swiperSlideOffset;\n    if (params.cssMode && params.centeredSlides) {\n      slideOffset -= slides[0].swiperSlideOffset;\n    }\n    const slideProgress = (offsetCenter + (params.centeredSlides ? swiper.minTranslate() : 0) - slideOffset) / (slide.swiperSlideSize + spaceBetween);\n    const originalSlideProgress = (offsetCenter - snapGrid[0] + (params.centeredSlides ? swiper.minTranslate() : 0) - slideOffset) / (slide.swiperSlideSize + spaceBetween);\n    const slideBefore = -(offsetCenter - slideOffset);\n    const slideAfter = slideBefore + swiper.slidesSizesGrid[i];\n    const isFullyVisible = slideBefore >= 0 && slideBefore <= swiper.size - swiper.slidesSizesGrid[i];\n    const isVisible = slideBefore >= 0 && slideBefore < swiper.size - 1 || slideAfter > 1 && slideAfter <= swiper.size || slideBefore <= 0 && slideAfter >= swiper.size;\n    if (isVisible) {\n      swiper.visibleSlides.push(slide);\n      swiper.visibleSlidesIndexes.push(i);\n    }\n    toggleSlideClasses$1(slide, isVisible, params.slideVisibleClass);\n    toggleSlideClasses$1(slide, isFullyVisible, params.slideFullyVisibleClass);\n    slide.progress = rtl ? -slideProgress : slideProgress;\n    slide.originalProgress = rtl ? -originalSlideProgress : originalSlideProgress;\n  }\n}\n\nfunction updateProgress(translate) {\n  const swiper = this;\n  if (typeof translate === 'undefined') {\n    const multiplier = swiper.rtlTranslate ? -1 : 1;\n    // eslint-disable-next-line\n    translate = swiper && swiper.translate && swiper.translate * multiplier || 0;\n  }\n  const params = swiper.params;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  let {\n    progress,\n    isBeginning,\n    isEnd,\n    progressLoop\n  } = swiper;\n  const wasBeginning = isBeginning;\n  const wasEnd = isEnd;\n  if (translatesDiff === 0) {\n    progress = 0;\n    isBeginning = true;\n    isEnd = true;\n  } else {\n    progress = (translate - swiper.minTranslate()) / translatesDiff;\n    const isBeginningRounded = Math.abs(translate - swiper.minTranslate()) < 1;\n    const isEndRounded = Math.abs(translate - swiper.maxTranslate()) < 1;\n    isBeginning = isBeginningRounded || progress <= 0;\n    isEnd = isEndRounded || progress >= 1;\n    if (isBeginningRounded) progress = 0;\n    if (isEndRounded) progress = 1;\n  }\n  if (params.loop) {\n    const firstSlideIndex = swiper.getSlideIndexByData(0);\n    const lastSlideIndex = swiper.getSlideIndexByData(swiper.slides.length - 1);\n    const firstSlideTranslate = swiper.slidesGrid[firstSlideIndex];\n    const lastSlideTranslate = swiper.slidesGrid[lastSlideIndex];\n    const translateMax = swiper.slidesGrid[swiper.slidesGrid.length - 1];\n    const translateAbs = Math.abs(translate);\n    if (translateAbs >= firstSlideTranslate) {\n      progressLoop = (translateAbs - firstSlideTranslate) / translateMax;\n    } else {\n      progressLoop = (translateAbs + translateMax - lastSlideTranslate) / translateMax;\n    }\n    if (progressLoop > 1) progressLoop -= 1;\n  }\n  Object.assign(swiper, {\n    progress,\n    progressLoop,\n    isBeginning,\n    isEnd\n  });\n  if (params.watchSlidesProgress || params.centeredSlides && params.autoHeight) swiper.updateSlidesProgress(translate);\n  if (isBeginning && !wasBeginning) {\n    swiper.emit('reachBeginning toEdge');\n  }\n  if (isEnd && !wasEnd) {\n    swiper.emit('reachEnd toEdge');\n  }\n  if (wasBeginning && !isBeginning || wasEnd && !isEnd) {\n    swiper.emit('fromEdge');\n  }\n  swiper.emit('progress', progress);\n}\n\nconst toggleSlideClasses = (slideEl, condition, className) => {\n  if (condition && !slideEl.classList.contains(className)) {\n    slideEl.classList.add(className);\n  } else if (!condition && slideEl.classList.contains(className)) {\n    slideEl.classList.remove(className);\n  }\n};\nfunction updateSlidesClasses() {\n  const swiper = this;\n  const {\n    slides,\n    params,\n    slidesEl,\n    activeIndex\n  } = swiper;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n  const getFilteredSlide = selector => {\n    return elementChildren(slidesEl, `.${params.slideClass}${selector}, swiper-slide${selector}`)[0];\n  };\n  let activeSlide;\n  let prevSlide;\n  let nextSlide;\n  if (isVirtual) {\n    if (params.loop) {\n      let slideIndex = activeIndex - swiper.virtual.slidesBefore;\n      if (slideIndex < 0) slideIndex = swiper.virtual.slides.length + slideIndex;\n      if (slideIndex >= swiper.virtual.slides.length) slideIndex -= swiper.virtual.slides.length;\n      activeSlide = getFilteredSlide(`[data-swiper-slide-index=\"${slideIndex}\"]`);\n    } else {\n      activeSlide = getFilteredSlide(`[data-swiper-slide-index=\"${activeIndex}\"]`);\n    }\n  } else {\n    if (gridEnabled) {\n      activeSlide = slides.find(slideEl => slideEl.column === activeIndex);\n      nextSlide = slides.find(slideEl => slideEl.column === activeIndex + 1);\n      prevSlide = slides.find(slideEl => slideEl.column === activeIndex - 1);\n    } else {\n      activeSlide = slides[activeIndex];\n    }\n  }\n  if (activeSlide) {\n    if (!gridEnabled) {\n      // Next Slide\n      nextSlide = elementNextAll(activeSlide, `.${params.slideClass}, swiper-slide`)[0];\n      if (params.loop && !nextSlide) {\n        nextSlide = slides[0];\n      }\n\n      // Prev Slide\n      prevSlide = elementPrevAll(activeSlide, `.${params.slideClass}, swiper-slide`)[0];\n      if (params.loop && !prevSlide === 0) {\n        prevSlide = slides[slides.length - 1];\n      }\n    }\n  }\n  slides.forEach(slideEl => {\n    toggleSlideClasses(slideEl, slideEl === activeSlide, params.slideActiveClass);\n    toggleSlideClasses(slideEl, slideEl === nextSlide, params.slideNextClass);\n    toggleSlideClasses(slideEl, slideEl === prevSlide, params.slidePrevClass);\n  });\n  swiper.emitSlidesClasses();\n}\n\nconst processLazyPreloader = (swiper, imageEl) => {\n  if (!swiper || swiper.destroyed || !swiper.params) return;\n  const slideSelector = () => swiper.isElement ? `swiper-slide` : `.${swiper.params.slideClass}`;\n  const slideEl = imageEl.closest(slideSelector());\n  if (slideEl) {\n    let lazyEl = slideEl.querySelector(`.${swiper.params.lazyPreloaderClass}`);\n    if (!lazyEl && swiper.isElement) {\n      if (slideEl.shadowRoot) {\n        lazyEl = slideEl.shadowRoot.querySelector(`.${swiper.params.lazyPreloaderClass}`);\n      } else {\n        // init later\n        requestAnimationFrame(() => {\n          if (slideEl.shadowRoot) {\n            lazyEl = slideEl.shadowRoot.querySelector(`.${swiper.params.lazyPreloaderClass}`);\n            if (lazyEl) lazyEl.remove();\n          }\n        });\n      }\n    }\n    if (lazyEl) lazyEl.remove();\n  }\n};\nconst unlazy = (swiper, index) => {\n  if (!swiper.slides[index]) return;\n  const imageEl = swiper.slides[index].querySelector('[loading=\"lazy\"]');\n  if (imageEl) imageEl.removeAttribute('loading');\n};\nconst preload = swiper => {\n  if (!swiper || swiper.destroyed || !swiper.params) return;\n  let amount = swiper.params.lazyPreloadPrevNext;\n  const len = swiper.slides.length;\n  if (!len || !amount || amount < 0) return;\n  amount = Math.min(amount, len);\n  const slidesPerView = swiper.params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : Math.ceil(swiper.params.slidesPerView);\n  const activeIndex = swiper.activeIndex;\n  if (swiper.params.grid && swiper.params.grid.rows > 1) {\n    const activeColumn = activeIndex;\n    const preloadColumns = [activeColumn - amount];\n    preloadColumns.push(...Array.from({\n      length: amount\n    }).map((_, i) => {\n      return activeColumn + slidesPerView + i;\n    }));\n    swiper.slides.forEach((slideEl, i) => {\n      if (preloadColumns.includes(slideEl.column)) unlazy(swiper, i);\n    });\n    return;\n  }\n  const slideIndexLastInView = activeIndex + slidesPerView - 1;\n  if (swiper.params.rewind || swiper.params.loop) {\n    for (let i = activeIndex - amount; i <= slideIndexLastInView + amount; i += 1) {\n      const realIndex = (i % len + len) % len;\n      if (realIndex < activeIndex || realIndex > slideIndexLastInView) unlazy(swiper, realIndex);\n    }\n  } else {\n    for (let i = Math.max(activeIndex - amount, 0); i <= Math.min(slideIndexLastInView + amount, len - 1); i += 1) {\n      if (i !== activeIndex && (i > slideIndexLastInView || i < activeIndex)) {\n        unlazy(swiper, i);\n      }\n    }\n  }\n};\n\nfunction getActiveIndexByTranslate(swiper) {\n  const {\n    slidesGrid,\n    params\n  } = swiper;\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  let activeIndex;\n  for (let i = 0; i < slidesGrid.length; i += 1) {\n    if (typeof slidesGrid[i + 1] !== 'undefined') {\n      if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1] - (slidesGrid[i + 1] - slidesGrid[i]) / 2) {\n        activeIndex = i;\n      } else if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1]) {\n        activeIndex = i + 1;\n      }\n    } else if (translate >= slidesGrid[i]) {\n      activeIndex = i;\n    }\n  }\n  // Normalize slideIndex\n  if (params.normalizeSlideIndex) {\n    if (activeIndex < 0 || typeof activeIndex === 'undefined') activeIndex = 0;\n  }\n  return activeIndex;\n}\nfunction updateActiveIndex(newActiveIndex) {\n  const swiper = this;\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  const {\n    snapGrid,\n    params,\n    activeIndex: previousIndex,\n    realIndex: previousRealIndex,\n    snapIndex: previousSnapIndex\n  } = swiper;\n  let activeIndex = newActiveIndex;\n  let snapIndex;\n  const getVirtualRealIndex = aIndex => {\n    let realIndex = aIndex - swiper.virtual.slidesBefore;\n    if (realIndex < 0) {\n      realIndex = swiper.virtual.slides.length + realIndex;\n    }\n    if (realIndex >= swiper.virtual.slides.length) {\n      realIndex -= swiper.virtual.slides.length;\n    }\n    return realIndex;\n  };\n  if (typeof activeIndex === 'undefined') {\n    activeIndex = getActiveIndexByTranslate(swiper);\n  }\n  if (snapGrid.indexOf(translate) >= 0) {\n    snapIndex = snapGrid.indexOf(translate);\n  } else {\n    const skip = Math.min(params.slidesPerGroupSkip, activeIndex);\n    snapIndex = skip + Math.floor((activeIndex - skip) / params.slidesPerGroup);\n  }\n  if (snapIndex >= snapGrid.length) snapIndex = snapGrid.length - 1;\n  if (activeIndex === previousIndex && !swiper.params.loop) {\n    if (snapIndex !== previousSnapIndex) {\n      swiper.snapIndex = snapIndex;\n      swiper.emit('snapIndexChange');\n    }\n    return;\n  }\n  if (activeIndex === previousIndex && swiper.params.loop && swiper.virtual && swiper.params.virtual.enabled) {\n    swiper.realIndex = getVirtualRealIndex(activeIndex);\n    return;\n  }\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n\n  // Get real index\n  let realIndex;\n  if (swiper.virtual && params.virtual.enabled && params.loop) {\n    realIndex = getVirtualRealIndex(activeIndex);\n  } else if (gridEnabled) {\n    const firstSlideInColumn = swiper.slides.find(slideEl => slideEl.column === activeIndex);\n    let activeSlideIndex = parseInt(firstSlideInColumn.getAttribute('data-swiper-slide-index'), 10);\n    if (Number.isNaN(activeSlideIndex)) {\n      activeSlideIndex = Math.max(swiper.slides.indexOf(firstSlideInColumn), 0);\n    }\n    realIndex = Math.floor(activeSlideIndex / params.grid.rows);\n  } else if (swiper.slides[activeIndex]) {\n    const slideIndex = swiper.slides[activeIndex].getAttribute('data-swiper-slide-index');\n    if (slideIndex) {\n      realIndex = parseInt(slideIndex, 10);\n    } else {\n      realIndex = activeIndex;\n    }\n  } else {\n    realIndex = activeIndex;\n  }\n  Object.assign(swiper, {\n    previousSnapIndex,\n    snapIndex,\n    previousRealIndex,\n    realIndex,\n    previousIndex,\n    activeIndex\n  });\n  if (swiper.initialized) {\n    preload(swiper);\n  }\n  swiper.emit('activeIndexChange');\n  swiper.emit('snapIndexChange');\n  if (swiper.initialized || swiper.params.runCallbacksOnInit) {\n    if (previousRealIndex !== realIndex) {\n      swiper.emit('realIndexChange');\n    }\n    swiper.emit('slideChange');\n  }\n}\n\nfunction updateClickedSlide(el, path) {\n  const swiper = this;\n  const params = swiper.params;\n  let slide = el.closest(`.${params.slideClass}, swiper-slide`);\n  if (!slide && swiper.isElement && path && path.length > 1 && path.includes(el)) {\n    [...path.slice(path.indexOf(el) + 1, path.length)].forEach(pathEl => {\n      if (!slide && pathEl.matches && pathEl.matches(`.${params.slideClass}, swiper-slide`)) {\n        slide = pathEl;\n      }\n    });\n  }\n  let slideFound = false;\n  let slideIndex;\n  if (slide) {\n    for (let i = 0; i < swiper.slides.length; i += 1) {\n      if (swiper.slides[i] === slide) {\n        slideFound = true;\n        slideIndex = i;\n        break;\n      }\n    }\n  }\n  if (slide && slideFound) {\n    swiper.clickedSlide = slide;\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      swiper.clickedIndex = parseInt(slide.getAttribute('data-swiper-slide-index'), 10);\n    } else {\n      swiper.clickedIndex = slideIndex;\n    }\n  } else {\n    swiper.clickedSlide = undefined;\n    swiper.clickedIndex = undefined;\n    return;\n  }\n  if (params.slideToClickedSlide && swiper.clickedIndex !== undefined && swiper.clickedIndex !== swiper.activeIndex) {\n    swiper.slideToClickedSlide();\n  }\n}\n\nvar update = {\n  updateSize,\n  updateSlides,\n  updateAutoHeight,\n  updateSlidesOffset,\n  updateSlidesProgress,\n  updateProgress,\n  updateSlidesClasses,\n  updateActiveIndex,\n  updateClickedSlide\n};\n\nfunction getSwiperTranslate(axis) {\n  if (axis === void 0) {\n    axis = this.isHorizontal() ? 'x' : 'y';\n  }\n  const swiper = this;\n  const {\n    params,\n    rtlTranslate: rtl,\n    translate,\n    wrapperEl\n  } = swiper;\n  if (params.virtualTranslate) {\n    return rtl ? -translate : translate;\n  }\n  if (params.cssMode) {\n    return translate;\n  }\n  let currentTranslate = getTranslate(wrapperEl, axis);\n  currentTranslate += swiper.cssOverflowAdjustment();\n  if (rtl) currentTranslate = -currentTranslate;\n  return currentTranslate || 0;\n}\n\nfunction setTranslate(translate, byController) {\n  const swiper = this;\n  const {\n    rtlTranslate: rtl,\n    params,\n    wrapperEl,\n    progress\n  } = swiper;\n  let x = 0;\n  let y = 0;\n  const z = 0;\n  if (swiper.isHorizontal()) {\n    x = rtl ? -translate : translate;\n  } else {\n    y = translate;\n  }\n  if (params.roundLengths) {\n    x = Math.floor(x);\n    y = Math.floor(y);\n  }\n  swiper.previousTranslate = swiper.translate;\n  swiper.translate = swiper.isHorizontal() ? x : y;\n  if (params.cssMode) {\n    wrapperEl[swiper.isHorizontal() ? 'scrollLeft' : 'scrollTop'] = swiper.isHorizontal() ? -x : -y;\n  } else if (!params.virtualTranslate) {\n    if (swiper.isHorizontal()) {\n      x -= swiper.cssOverflowAdjustment();\n    } else {\n      y -= swiper.cssOverflowAdjustment();\n    }\n    wrapperEl.style.transform = `translate3d(${x}px, ${y}px, ${z}px)`;\n  }\n\n  // Check if we need to update progress\n  let newProgress;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  if (translatesDiff === 0) {\n    newProgress = 0;\n  } else {\n    newProgress = (translate - swiper.minTranslate()) / translatesDiff;\n  }\n  if (newProgress !== progress) {\n    swiper.updateProgress(translate);\n  }\n  swiper.emit('setTranslate', swiper.translate, byController);\n}\n\nfunction minTranslate() {\n  return -this.snapGrid[0];\n}\n\nfunction maxTranslate() {\n  return -this.snapGrid[this.snapGrid.length - 1];\n}\n\nfunction translateTo(translate, speed, runCallbacks, translateBounds, internal) {\n  if (translate === void 0) {\n    translate = 0;\n  }\n  if (speed === void 0) {\n    speed = this.params.speed;\n  }\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (translateBounds === void 0) {\n    translateBounds = true;\n  }\n  const swiper = this;\n  const {\n    params,\n    wrapperEl\n  } = swiper;\n  if (swiper.animating && params.preventInteractionOnTransition) {\n    return false;\n  }\n  const minTranslate = swiper.minTranslate();\n  const maxTranslate = swiper.maxTranslate();\n  let newTranslate;\n  if (translateBounds && translate > minTranslate) newTranslate = minTranslate;else if (translateBounds && translate < maxTranslate) newTranslate = maxTranslate;else newTranslate = translate;\n\n  // Update progress\n  swiper.updateProgress(newTranslate);\n  if (params.cssMode) {\n    const isH = swiper.isHorizontal();\n    if (speed === 0) {\n      wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = -newTranslate;\n    } else {\n      if (!swiper.support.smoothScroll) {\n        animateCSSModeScroll({\n          swiper,\n          targetPosition: -newTranslate,\n          side: isH ? 'left' : 'top'\n        });\n        return true;\n      }\n      wrapperEl.scrollTo({\n        [isH ? 'left' : 'top']: -newTranslate,\n        behavior: 'smooth'\n      });\n    }\n    return true;\n  }\n  if (speed === 0) {\n    swiper.setTransition(0);\n    swiper.setTranslate(newTranslate);\n    if (runCallbacks) {\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.emit('transitionEnd');\n    }\n  } else {\n    swiper.setTransition(speed);\n    swiper.setTranslate(newTranslate);\n    if (runCallbacks) {\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.emit('transitionStart');\n    }\n    if (!swiper.animating) {\n      swiper.animating = true;\n      if (!swiper.onTranslateToWrapperTransitionEnd) {\n        swiper.onTranslateToWrapperTransitionEnd = function transitionEnd(e) {\n          if (!swiper || swiper.destroyed) return;\n          if (e.target !== this) return;\n          swiper.wrapperEl.removeEventListener('transitionend', swiper.onTranslateToWrapperTransitionEnd);\n          swiper.onTranslateToWrapperTransitionEnd = null;\n          delete swiper.onTranslateToWrapperTransitionEnd;\n          swiper.animating = false;\n          if (runCallbacks) {\n            swiper.emit('transitionEnd');\n          }\n        };\n      }\n      swiper.wrapperEl.addEventListener('transitionend', swiper.onTranslateToWrapperTransitionEnd);\n    }\n  }\n  return true;\n}\n\nvar translate = {\n  getTranslate: getSwiperTranslate,\n  setTranslate,\n  minTranslate,\n  maxTranslate,\n  translateTo\n};\n\nfunction setTransition(duration, byController) {\n  const swiper = this;\n  if (!swiper.params.cssMode) {\n    swiper.wrapperEl.style.transitionDuration = `${duration}ms`;\n    swiper.wrapperEl.style.transitionDelay = duration === 0 ? `0ms` : '';\n  }\n  swiper.emit('setTransition', duration, byController);\n}\n\nfunction transitionEmit(_ref) {\n  let {\n    swiper,\n    runCallbacks,\n    direction,\n    step\n  } = _ref;\n  const {\n    activeIndex,\n    previousIndex\n  } = swiper;\n  let dir = direction;\n  if (!dir) {\n    if (activeIndex > previousIndex) dir = 'next';else if (activeIndex < previousIndex) dir = 'prev';else dir = 'reset';\n  }\n  swiper.emit(`transition${step}`);\n  if (runCallbacks && dir === 'reset') {\n    swiper.emit(`slideResetTransition${step}`);\n  } else if (runCallbacks && activeIndex !== previousIndex) {\n    swiper.emit(`slideChangeTransition${step}`);\n    if (dir === 'next') {\n      swiper.emit(`slideNextTransition${step}`);\n    } else {\n      swiper.emit(`slidePrevTransition${step}`);\n    }\n  }\n}\n\nfunction transitionStart(runCallbacks, direction) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  if (params.cssMode) return;\n  if (params.autoHeight) {\n    swiper.updateAutoHeight();\n  }\n  transitionEmit({\n    swiper,\n    runCallbacks,\n    direction,\n    step: 'Start'\n  });\n}\n\nfunction transitionEnd(runCallbacks, direction) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  swiper.animating = false;\n  if (params.cssMode) return;\n  swiper.setTransition(0);\n  transitionEmit({\n    swiper,\n    runCallbacks,\n    direction,\n    step: 'End'\n  });\n}\n\nvar transition = {\n  setTransition,\n  transitionStart,\n  transitionEnd\n};\n\nfunction slideTo(index, speed, runCallbacks, internal, initial) {\n  if (index === void 0) {\n    index = 0;\n  }\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (typeof index === 'string') {\n    index = parseInt(index, 10);\n  }\n  const swiper = this;\n  let slideIndex = index;\n  if (slideIndex < 0) slideIndex = 0;\n  const {\n    params,\n    snapGrid,\n    slidesGrid,\n    previousIndex,\n    activeIndex,\n    rtlTranslate: rtl,\n    wrapperEl,\n    enabled\n  } = swiper;\n  if (!enabled && !internal && !initial || swiper.destroyed || swiper.animating && params.preventInteractionOnTransition) {\n    return false;\n  }\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  const skip = Math.min(swiper.params.slidesPerGroupSkip, slideIndex);\n  let snapIndex = skip + Math.floor((slideIndex - skip) / swiper.params.slidesPerGroup);\n  if (snapIndex >= snapGrid.length) snapIndex = snapGrid.length - 1;\n  const translate = -snapGrid[snapIndex];\n  // Normalize slideIndex\n  if (params.normalizeSlideIndex) {\n    for (let i = 0; i < slidesGrid.length; i += 1) {\n      const normalizedTranslate = -Math.floor(translate * 100);\n      const normalizedGrid = Math.floor(slidesGrid[i] * 100);\n      const normalizedGridNext = Math.floor(slidesGrid[i + 1] * 100);\n      if (typeof slidesGrid[i + 1] !== 'undefined') {\n        if (normalizedTranslate >= normalizedGrid && normalizedTranslate < normalizedGridNext - (normalizedGridNext - normalizedGrid) / 2) {\n          slideIndex = i;\n        } else if (normalizedTranslate >= normalizedGrid && normalizedTranslate < normalizedGridNext) {\n          slideIndex = i + 1;\n        }\n      } else if (normalizedTranslate >= normalizedGrid) {\n        slideIndex = i;\n      }\n    }\n  }\n  // Directions locks\n  if (swiper.initialized && slideIndex !== activeIndex) {\n    if (!swiper.allowSlideNext && (rtl ? translate > swiper.translate && translate > swiper.minTranslate() : translate < swiper.translate && translate < swiper.minTranslate())) {\n      return false;\n    }\n    if (!swiper.allowSlidePrev && translate > swiper.translate && translate > swiper.maxTranslate()) {\n      if ((activeIndex || 0) !== slideIndex) {\n        return false;\n      }\n    }\n  }\n  if (slideIndex !== (previousIndex || 0) && runCallbacks) {\n    swiper.emit('beforeSlideChangeStart');\n  }\n\n  // Update progress\n  swiper.updateProgress(translate);\n  let direction;\n  if (slideIndex > activeIndex) direction = 'next';else if (slideIndex < activeIndex) direction = 'prev';else direction = 'reset';\n\n  // initial virtual\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n  const isInitialVirtual = isVirtual && initial;\n  // Update Index\n  if (!isInitialVirtual && (rtl && -translate === swiper.translate || !rtl && translate === swiper.translate)) {\n    swiper.updateActiveIndex(slideIndex);\n    // Update Height\n    if (params.autoHeight) {\n      swiper.updateAutoHeight();\n    }\n    swiper.updateSlidesClasses();\n    if (params.effect !== 'slide') {\n      swiper.setTranslate(translate);\n    }\n    if (direction !== 'reset') {\n      swiper.transitionStart(runCallbacks, direction);\n      swiper.transitionEnd(runCallbacks, direction);\n    }\n    return false;\n  }\n  if (params.cssMode) {\n    const isH = swiper.isHorizontal();\n    const t = rtl ? translate : -translate;\n    if (speed === 0) {\n      if (isVirtual) {\n        swiper.wrapperEl.style.scrollSnapType = 'none';\n        swiper._immediateVirtual = true;\n      }\n      if (isVirtual && !swiper._cssModeVirtualInitialSet && swiper.params.initialSlide > 0) {\n        swiper._cssModeVirtualInitialSet = true;\n        requestAnimationFrame(() => {\n          wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = t;\n        });\n      } else {\n        wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = t;\n      }\n      if (isVirtual) {\n        requestAnimationFrame(() => {\n          swiper.wrapperEl.style.scrollSnapType = '';\n          swiper._immediateVirtual = false;\n        });\n      }\n    } else {\n      if (!swiper.support.smoothScroll) {\n        animateCSSModeScroll({\n          swiper,\n          targetPosition: t,\n          side: isH ? 'left' : 'top'\n        });\n        return true;\n      }\n      wrapperEl.scrollTo({\n        [isH ? 'left' : 'top']: t,\n        behavior: 'smooth'\n      });\n    }\n    return true;\n  }\n  const browser = getBrowser();\n  const isSafari = browser.isSafari;\n  if (isVirtual && !initial && isSafari && swiper.isElement) {\n    swiper.virtual.update(false, false, slideIndex);\n  }\n  swiper.setTransition(speed);\n  swiper.setTranslate(translate);\n  swiper.updateActiveIndex(slideIndex);\n  swiper.updateSlidesClasses();\n  swiper.emit('beforeTransitionStart', speed, internal);\n  swiper.transitionStart(runCallbacks, direction);\n  if (speed === 0) {\n    swiper.transitionEnd(runCallbacks, direction);\n  } else if (!swiper.animating) {\n    swiper.animating = true;\n    if (!swiper.onSlideToWrapperTransitionEnd) {\n      swiper.onSlideToWrapperTransitionEnd = function transitionEnd(e) {\n        if (!swiper || swiper.destroyed) return;\n        if (e.target !== this) return;\n        swiper.wrapperEl.removeEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n        swiper.onSlideToWrapperTransitionEnd = null;\n        delete swiper.onSlideToWrapperTransitionEnd;\n        swiper.transitionEnd(runCallbacks, direction);\n      };\n    }\n    swiper.wrapperEl.addEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n  }\n  return true;\n}\n\nfunction slideToLoop(index, speed, runCallbacks, internal) {\n  if (index === void 0) {\n    index = 0;\n  }\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (typeof index === 'string') {\n    const indexAsNumber = parseInt(index, 10);\n    index = indexAsNumber;\n  }\n  const swiper = this;\n  if (swiper.destroyed) return;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  const gridEnabled = swiper.grid && swiper.params.grid && swiper.params.grid.rows > 1;\n  let newIndex = index;\n  if (swiper.params.loop) {\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      // eslint-disable-next-line\n      newIndex = newIndex + swiper.virtual.slidesBefore;\n    } else {\n      let targetSlideIndex;\n      if (gridEnabled) {\n        const slideIndex = newIndex * swiper.params.grid.rows;\n        targetSlideIndex = swiper.slides.find(slideEl => slideEl.getAttribute('data-swiper-slide-index') * 1 === slideIndex).column;\n      } else {\n        targetSlideIndex = swiper.getSlideIndexByData(newIndex);\n      }\n      const cols = gridEnabled ? Math.ceil(swiper.slides.length / swiper.params.grid.rows) : swiper.slides.length;\n      const {\n        centeredSlides\n      } = swiper.params;\n      let slidesPerView = swiper.params.slidesPerView;\n      if (slidesPerView === 'auto') {\n        slidesPerView = swiper.slidesPerViewDynamic();\n      } else {\n        slidesPerView = Math.ceil(parseFloat(swiper.params.slidesPerView, 10));\n        if (centeredSlides && slidesPerView % 2 === 0) {\n          slidesPerView = slidesPerView + 1;\n        }\n      }\n      let needLoopFix = cols - targetSlideIndex < slidesPerView;\n      if (centeredSlides) {\n        needLoopFix = needLoopFix || targetSlideIndex < Math.ceil(slidesPerView / 2);\n      }\n      if (internal && centeredSlides && swiper.params.slidesPerView !== 'auto' && !gridEnabled) {\n        needLoopFix = false;\n      }\n      if (needLoopFix) {\n        const direction = centeredSlides ? targetSlideIndex < swiper.activeIndex ? 'prev' : 'next' : targetSlideIndex - swiper.activeIndex - 1 < swiper.params.slidesPerView ? 'next' : 'prev';\n        swiper.loopFix({\n          direction,\n          slideTo: true,\n          activeSlideIndex: direction === 'next' ? targetSlideIndex + 1 : targetSlideIndex - cols + 1,\n          slideRealIndex: direction === 'next' ? swiper.realIndex : undefined\n        });\n      }\n      if (gridEnabled) {\n        const slideIndex = newIndex * swiper.params.grid.rows;\n        newIndex = swiper.slides.find(slideEl => slideEl.getAttribute('data-swiper-slide-index') * 1 === slideIndex).column;\n      } else {\n        newIndex = swiper.getSlideIndexByData(newIndex);\n      }\n    }\n  }\n  requestAnimationFrame(() => {\n    swiper.slideTo(newIndex, speed, runCallbacks, internal);\n  });\n  return swiper;\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slideNext(speed, runCallbacks, internal) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    enabled,\n    params,\n    animating\n  } = swiper;\n  if (!enabled || swiper.destroyed) return swiper;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  let perGroup = params.slidesPerGroup;\n  if (params.slidesPerView === 'auto' && params.slidesPerGroup === 1 && params.slidesPerGroupAuto) {\n    perGroup = Math.max(swiper.slidesPerViewDynamic('current', true), 1);\n  }\n  const increment = swiper.activeIndex < params.slidesPerGroupSkip ? 1 : perGroup;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  if (params.loop) {\n    if (animating && !isVirtual && params.loopPreventsSliding) return false;\n    swiper.loopFix({\n      direction: 'next'\n    });\n    // eslint-disable-next-line\n    swiper._clientLeft = swiper.wrapperEl.clientLeft;\n    if (swiper.activeIndex === swiper.slides.length - 1 && params.cssMode) {\n      requestAnimationFrame(() => {\n        swiper.slideTo(swiper.activeIndex + increment, speed, runCallbacks, internal);\n      });\n      return true;\n    }\n  }\n  if (params.rewind && swiper.isEnd) {\n    return swiper.slideTo(0, speed, runCallbacks, internal);\n  }\n  return swiper.slideTo(swiper.activeIndex + increment, speed, runCallbacks, internal);\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slidePrev(speed, runCallbacks, internal) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    params,\n    snapGrid,\n    slidesGrid,\n    rtlTranslate,\n    enabled,\n    animating\n  } = swiper;\n  if (!enabled || swiper.destroyed) return swiper;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  if (params.loop) {\n    if (animating && !isVirtual && params.loopPreventsSliding) return false;\n    swiper.loopFix({\n      direction: 'prev'\n    });\n    // eslint-disable-next-line\n    swiper._clientLeft = swiper.wrapperEl.clientLeft;\n  }\n  const translate = rtlTranslate ? swiper.translate : -swiper.translate;\n  function normalize(val) {\n    if (val < 0) return -Math.floor(Math.abs(val));\n    return Math.floor(val);\n  }\n  const normalizedTranslate = normalize(translate);\n  const normalizedSnapGrid = snapGrid.map(val => normalize(val));\n  const isFreeMode = params.freeMode && params.freeMode.enabled;\n  let prevSnap = snapGrid[normalizedSnapGrid.indexOf(normalizedTranslate) - 1];\n  if (typeof prevSnap === 'undefined' && (params.cssMode || isFreeMode)) {\n    let prevSnapIndex;\n    snapGrid.forEach((snap, snapIndex) => {\n      if (normalizedTranslate >= snap) {\n        // prevSnap = snap;\n        prevSnapIndex = snapIndex;\n      }\n    });\n    if (typeof prevSnapIndex !== 'undefined') {\n      prevSnap = isFreeMode ? snapGrid[prevSnapIndex] : snapGrid[prevSnapIndex > 0 ? prevSnapIndex - 1 : prevSnapIndex];\n    }\n  }\n  let prevIndex = 0;\n  if (typeof prevSnap !== 'undefined') {\n    prevIndex = slidesGrid.indexOf(prevSnap);\n    if (prevIndex < 0) prevIndex = swiper.activeIndex - 1;\n    if (params.slidesPerView === 'auto' && params.slidesPerGroup === 1 && params.slidesPerGroupAuto) {\n      prevIndex = prevIndex - swiper.slidesPerViewDynamic('previous', true) + 1;\n      prevIndex = Math.max(prevIndex, 0);\n    }\n  }\n  if (params.rewind && swiper.isBeginning) {\n    const lastIndex = swiper.params.virtual && swiper.params.virtual.enabled && swiper.virtual ? swiper.virtual.slides.length - 1 : swiper.slides.length - 1;\n    return swiper.slideTo(lastIndex, speed, runCallbacks, internal);\n  } else if (params.loop && swiper.activeIndex === 0 && params.cssMode) {\n    requestAnimationFrame(() => {\n      swiper.slideTo(prevIndex, speed, runCallbacks, internal);\n    });\n    return true;\n  }\n  return swiper.slideTo(prevIndex, speed, runCallbacks, internal);\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slideReset(speed, runCallbacks, internal) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  if (swiper.destroyed) return;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  return swiper.slideTo(swiper.activeIndex, speed, runCallbacks, internal);\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slideToClosest(speed, runCallbacks, internal, threshold) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (threshold === void 0) {\n    threshold = 0.5;\n  }\n  const swiper = this;\n  if (swiper.destroyed) return;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  let index = swiper.activeIndex;\n  const skip = Math.min(swiper.params.slidesPerGroupSkip, index);\n  const snapIndex = skip + Math.floor((index - skip) / swiper.params.slidesPerGroup);\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  if (translate >= swiper.snapGrid[snapIndex]) {\n    // The current translate is on or after the current snap index, so the choice\n    // is between the current index and the one after it.\n    const currentSnap = swiper.snapGrid[snapIndex];\n    const nextSnap = swiper.snapGrid[snapIndex + 1];\n    if (translate - currentSnap > (nextSnap - currentSnap) * threshold) {\n      index += swiper.params.slidesPerGroup;\n    }\n  } else {\n    // The current translate is before the current snap index, so the choice\n    // is between the current index and the one before it.\n    const prevSnap = swiper.snapGrid[snapIndex - 1];\n    const currentSnap = swiper.snapGrid[snapIndex];\n    if (translate - prevSnap <= (currentSnap - prevSnap) * threshold) {\n      index -= swiper.params.slidesPerGroup;\n    }\n  }\n  index = Math.max(index, 0);\n  index = Math.min(index, swiper.slidesGrid.length - 1);\n  return swiper.slideTo(index, speed, runCallbacks, internal);\n}\n\nfunction slideToClickedSlide() {\n  const swiper = this;\n  if (swiper.destroyed) return;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  const slidesPerView = params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : params.slidesPerView;\n  let slideToIndex = swiper.getSlideIndexWhenGrid(swiper.clickedIndex);\n  let realIndex;\n  const slideSelector = swiper.isElement ? `swiper-slide` : `.${params.slideClass}`;\n  const isGrid = swiper.grid && swiper.params.grid && swiper.params.grid.rows > 1;\n  if (params.loop) {\n    if (swiper.animating) return;\n    realIndex = parseInt(swiper.clickedSlide.getAttribute('data-swiper-slide-index'), 10);\n    if (params.centeredSlides) {\n      swiper.slideToLoop(realIndex);\n    } else if (slideToIndex > (isGrid ? (swiper.slides.length - slidesPerView) / 2 - (swiper.params.grid.rows - 1) : swiper.slides.length - slidesPerView)) {\n      swiper.loopFix();\n      slideToIndex = swiper.getSlideIndex(elementChildren(slidesEl, `${slideSelector}[data-swiper-slide-index=\"${realIndex}\"]`)[0]);\n      nextTick(() => {\n        swiper.slideTo(slideToIndex);\n      });\n    } else {\n      swiper.slideTo(slideToIndex);\n    }\n  } else {\n    swiper.slideTo(slideToIndex);\n  }\n}\n\nvar slide = {\n  slideTo,\n  slideToLoop,\n  slideNext,\n  slidePrev,\n  slideReset,\n  slideToClosest,\n  slideToClickedSlide\n};\n\nfunction loopCreate(slideRealIndex, initial) {\n  const swiper = this;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  if (!params.loop || swiper.virtual && swiper.params.virtual.enabled) return;\n  const initSlides = () => {\n    const slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n    slides.forEach((el, index) => {\n      el.setAttribute('data-swiper-slide-index', index);\n    });\n  };\n  const clearBlankSlides = () => {\n    const slides = elementChildren(slidesEl, `.${params.slideBlankClass}`);\n    slides.forEach(el => {\n      el.remove();\n    });\n    if (slides.length > 0) {\n      swiper.recalcSlides();\n      swiper.updateSlides();\n    }\n  };\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n  if (params.loopAddBlankSlides && (params.slidesPerGroup > 1 || gridEnabled)) {\n    clearBlankSlides();\n  }\n  const slidesPerGroup = params.slidesPerGroup * (gridEnabled ? params.grid.rows : 1);\n  const shouldFillGroup = swiper.slides.length % slidesPerGroup !== 0;\n  const shouldFillGrid = gridEnabled && swiper.slides.length % params.grid.rows !== 0;\n  const addBlankSlides = amountOfSlides => {\n    for (let i = 0; i < amountOfSlides; i += 1) {\n      const slideEl = swiper.isElement ? createElement('swiper-slide', [params.slideBlankClass]) : createElement('div', [params.slideClass, params.slideBlankClass]);\n      swiper.slidesEl.append(slideEl);\n    }\n  };\n  if (shouldFillGroup) {\n    if (params.loopAddBlankSlides) {\n      const slidesToAdd = slidesPerGroup - swiper.slides.length % slidesPerGroup;\n      addBlankSlides(slidesToAdd);\n      swiper.recalcSlides();\n      swiper.updateSlides();\n    } else {\n      showWarning('Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)');\n    }\n    initSlides();\n  } else if (shouldFillGrid) {\n    if (params.loopAddBlankSlides) {\n      const slidesToAdd = params.grid.rows - swiper.slides.length % params.grid.rows;\n      addBlankSlides(slidesToAdd);\n      swiper.recalcSlides();\n      swiper.updateSlides();\n    } else {\n      showWarning('Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)');\n    }\n    initSlides();\n  } else {\n    initSlides();\n  }\n  swiper.loopFix({\n    slideRealIndex,\n    direction: params.centeredSlides ? undefined : 'next',\n    initial\n  });\n}\n\nfunction loopFix(_temp) {\n  let {\n    slideRealIndex,\n    slideTo = true,\n    direction,\n    setTranslate,\n    activeSlideIndex,\n    initial,\n    byController,\n    byMousewheel\n  } = _temp === void 0 ? {} : _temp;\n  const swiper = this;\n  if (!swiper.params.loop) return;\n  swiper.emit('beforeLoopFix');\n  const {\n    slides,\n    allowSlidePrev,\n    allowSlideNext,\n    slidesEl,\n    params\n  } = swiper;\n  const {\n    centeredSlides,\n    initialSlide\n  } = params;\n  swiper.allowSlidePrev = true;\n  swiper.allowSlideNext = true;\n  if (swiper.virtual && params.virtual.enabled) {\n    if (slideTo) {\n      if (!params.centeredSlides && swiper.snapIndex === 0) {\n        swiper.slideTo(swiper.virtual.slides.length, 0, false, true);\n      } else if (params.centeredSlides && swiper.snapIndex < params.slidesPerView) {\n        swiper.slideTo(swiper.virtual.slides.length + swiper.snapIndex, 0, false, true);\n      } else if (swiper.snapIndex === swiper.snapGrid.length - 1) {\n        swiper.slideTo(swiper.virtual.slidesBefore, 0, false, true);\n      }\n    }\n    swiper.allowSlidePrev = allowSlidePrev;\n    swiper.allowSlideNext = allowSlideNext;\n    swiper.emit('loopFix');\n    return;\n  }\n  let slidesPerView = params.slidesPerView;\n  if (slidesPerView === 'auto') {\n    slidesPerView = swiper.slidesPerViewDynamic();\n  } else {\n    slidesPerView = Math.ceil(parseFloat(params.slidesPerView, 10));\n    if (centeredSlides && slidesPerView % 2 === 0) {\n      slidesPerView = slidesPerView + 1;\n    }\n  }\n  const slidesPerGroup = params.slidesPerGroupAuto ? slidesPerView : params.slidesPerGroup;\n  let loopedSlides = centeredSlides ? Math.max(slidesPerGroup, Math.ceil(slidesPerView / 2)) : slidesPerGroup;\n  if (loopedSlides % slidesPerGroup !== 0) {\n    loopedSlides += slidesPerGroup - loopedSlides % slidesPerGroup;\n  }\n  loopedSlides += params.loopAdditionalSlides;\n  swiper.loopedSlides = loopedSlides;\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n  if (slides.length < slidesPerView + loopedSlides || swiper.params.effect === 'cards' && slides.length < slidesPerView + loopedSlides * 2) {\n    showWarning('Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled or not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters');\n  } else if (gridEnabled && params.grid.fill === 'row') {\n    showWarning('Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`');\n  }\n  const prependSlidesIndexes = [];\n  const appendSlidesIndexes = [];\n  const cols = gridEnabled ? Math.ceil(slides.length / params.grid.rows) : slides.length;\n  const isInitialOverflow = initial && cols - initialSlide < slidesPerView && !centeredSlides;\n  let activeIndex = isInitialOverflow ? initialSlide : swiper.activeIndex;\n  if (typeof activeSlideIndex === 'undefined') {\n    activeSlideIndex = swiper.getSlideIndex(slides.find(el => el.classList.contains(params.slideActiveClass)));\n  } else {\n    activeIndex = activeSlideIndex;\n  }\n  const isNext = direction === 'next' || !direction;\n  const isPrev = direction === 'prev' || !direction;\n  let slidesPrepended = 0;\n  let slidesAppended = 0;\n  const activeColIndex = gridEnabled ? slides[activeSlideIndex].column : activeSlideIndex;\n  const activeColIndexWithShift = activeColIndex + (centeredSlides && typeof setTranslate === 'undefined' ? -slidesPerView / 2 + 0.5 : 0);\n  // prepend last slides before start\n  if (activeColIndexWithShift < loopedSlides) {\n    slidesPrepended = Math.max(loopedSlides - activeColIndexWithShift, slidesPerGroup);\n    for (let i = 0; i < loopedSlides - activeColIndexWithShift; i += 1) {\n      const index = i - Math.floor(i / cols) * cols;\n      if (gridEnabled) {\n        const colIndexToPrepend = cols - index - 1;\n        for (let i = slides.length - 1; i >= 0; i -= 1) {\n          if (slides[i].column === colIndexToPrepend) prependSlidesIndexes.push(i);\n        }\n        // slides.forEach((slide, slideIndex) => {\n        //   if (slide.column === colIndexToPrepend) prependSlidesIndexes.push(slideIndex);\n        // });\n      } else {\n        prependSlidesIndexes.push(cols - index - 1);\n      }\n    }\n  } else if (activeColIndexWithShift + slidesPerView > cols - loopedSlides) {\n    slidesAppended = Math.max(activeColIndexWithShift - (cols - loopedSlides * 2), slidesPerGroup);\n    if (isInitialOverflow) {\n      slidesAppended = Math.max(slidesAppended, slidesPerView - cols + initialSlide + 1);\n    }\n    for (let i = 0; i < slidesAppended; i += 1) {\n      const index = i - Math.floor(i / cols) * cols;\n      if (gridEnabled) {\n        slides.forEach((slide, slideIndex) => {\n          if (slide.column === index) appendSlidesIndexes.push(slideIndex);\n        });\n      } else {\n        appendSlidesIndexes.push(index);\n      }\n    }\n  }\n  swiper.__preventObserver__ = true;\n  requestAnimationFrame(() => {\n    swiper.__preventObserver__ = false;\n  });\n  if (swiper.params.effect === 'cards' && slides.length < slidesPerView + loopedSlides * 2) {\n    if (appendSlidesIndexes.includes(activeSlideIndex)) {\n      appendSlidesIndexes.splice(appendSlidesIndexes.indexOf(activeSlideIndex), 1);\n    }\n    if (prependSlidesIndexes.includes(activeSlideIndex)) {\n      prependSlidesIndexes.splice(prependSlidesIndexes.indexOf(activeSlideIndex), 1);\n    }\n  }\n  if (isPrev) {\n    prependSlidesIndexes.forEach(index => {\n      slides[index].swiperLoopMoveDOM = true;\n      slidesEl.prepend(slides[index]);\n      slides[index].swiperLoopMoveDOM = false;\n    });\n  }\n  if (isNext) {\n    appendSlidesIndexes.forEach(index => {\n      slides[index].swiperLoopMoveDOM = true;\n      slidesEl.append(slides[index]);\n      slides[index].swiperLoopMoveDOM = false;\n    });\n  }\n  swiper.recalcSlides();\n  if (params.slidesPerView === 'auto') {\n    swiper.updateSlides();\n  } else if (gridEnabled && (prependSlidesIndexes.length > 0 && isPrev || appendSlidesIndexes.length > 0 && isNext)) {\n    swiper.slides.forEach((slide, slideIndex) => {\n      swiper.grid.updateSlide(slideIndex, slide, swiper.slides);\n    });\n  }\n  if (params.watchSlidesProgress) {\n    swiper.updateSlidesOffset();\n  }\n  if (slideTo) {\n    if (prependSlidesIndexes.length > 0 && isPrev) {\n      if (typeof slideRealIndex === 'undefined') {\n        const currentSlideTranslate = swiper.slidesGrid[activeIndex];\n        const newSlideTranslate = swiper.slidesGrid[activeIndex + slidesPrepended];\n        const diff = newSlideTranslate - currentSlideTranslate;\n        if (byMousewheel) {\n          swiper.setTranslate(swiper.translate - diff);\n        } else {\n          swiper.slideTo(activeIndex + Math.ceil(slidesPrepended), 0, false, true);\n          if (setTranslate) {\n            swiper.touchEventsData.startTranslate = swiper.touchEventsData.startTranslate - diff;\n            swiper.touchEventsData.currentTranslate = swiper.touchEventsData.currentTranslate - diff;\n          }\n        }\n      } else {\n        if (setTranslate) {\n          const shift = gridEnabled ? prependSlidesIndexes.length / params.grid.rows : prependSlidesIndexes.length;\n          swiper.slideTo(swiper.activeIndex + shift, 0, false, true);\n          swiper.touchEventsData.currentTranslate = swiper.translate;\n        }\n      }\n    } else if (appendSlidesIndexes.length > 0 && isNext) {\n      if (typeof slideRealIndex === 'undefined') {\n        const currentSlideTranslate = swiper.slidesGrid[activeIndex];\n        const newSlideTranslate = swiper.slidesGrid[activeIndex - slidesAppended];\n        const diff = newSlideTranslate - currentSlideTranslate;\n        if (byMousewheel) {\n          swiper.setTranslate(swiper.translate - diff);\n        } else {\n          swiper.slideTo(activeIndex - slidesAppended, 0, false, true);\n          if (setTranslate) {\n            swiper.touchEventsData.startTranslate = swiper.touchEventsData.startTranslate - diff;\n            swiper.touchEventsData.currentTranslate = swiper.touchEventsData.currentTranslate - diff;\n          }\n        }\n      } else {\n        const shift = gridEnabled ? appendSlidesIndexes.length / params.grid.rows : appendSlidesIndexes.length;\n        swiper.slideTo(swiper.activeIndex - shift, 0, false, true);\n      }\n    }\n  }\n  swiper.allowSlidePrev = allowSlidePrev;\n  swiper.allowSlideNext = allowSlideNext;\n  if (swiper.controller && swiper.controller.control && !byController) {\n    const loopParams = {\n      slideRealIndex,\n      direction,\n      setTranslate,\n      activeSlideIndex,\n      byController: true\n    };\n    if (Array.isArray(swiper.controller.control)) {\n      swiper.controller.control.forEach(c => {\n        if (!c.destroyed && c.params.loop) c.loopFix({\n          ...loopParams,\n          slideTo: c.params.slidesPerView === params.slidesPerView ? slideTo : false\n        });\n      });\n    } else if (swiper.controller.control instanceof swiper.constructor && swiper.controller.control.params.loop) {\n      swiper.controller.control.loopFix({\n        ...loopParams,\n        slideTo: swiper.controller.control.params.slidesPerView === params.slidesPerView ? slideTo : false\n      });\n    }\n  }\n  swiper.emit('loopFix');\n}\n\nfunction loopDestroy() {\n  const swiper = this;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  if (!params.loop || !slidesEl || swiper.virtual && swiper.params.virtual.enabled) return;\n  swiper.recalcSlides();\n  const newSlidesOrder = [];\n  swiper.slides.forEach(slideEl => {\n    const index = typeof slideEl.swiperSlideIndex === 'undefined' ? slideEl.getAttribute('data-swiper-slide-index') * 1 : slideEl.swiperSlideIndex;\n    newSlidesOrder[index] = slideEl;\n  });\n  swiper.slides.forEach(slideEl => {\n    slideEl.removeAttribute('data-swiper-slide-index');\n  });\n  newSlidesOrder.forEach(slideEl => {\n    slidesEl.append(slideEl);\n  });\n  swiper.recalcSlides();\n  swiper.slideTo(swiper.realIndex, 0);\n}\n\nvar loop = {\n  loopCreate,\n  loopFix,\n  loopDestroy\n};\n\nfunction setGrabCursor(moving) {\n  const swiper = this;\n  if (!swiper.params.simulateTouch || swiper.params.watchOverflow && swiper.isLocked || swiper.params.cssMode) return;\n  const el = swiper.params.touchEventsTarget === 'container' ? swiper.el : swiper.wrapperEl;\n  if (swiper.isElement) {\n    swiper.__preventObserver__ = true;\n  }\n  el.style.cursor = 'move';\n  el.style.cursor = moving ? 'grabbing' : 'grab';\n  if (swiper.isElement) {\n    requestAnimationFrame(() => {\n      swiper.__preventObserver__ = false;\n    });\n  }\n}\n\nfunction unsetGrabCursor() {\n  const swiper = this;\n  if (swiper.params.watchOverflow && swiper.isLocked || swiper.params.cssMode) {\n    return;\n  }\n  if (swiper.isElement) {\n    swiper.__preventObserver__ = true;\n  }\n  swiper[swiper.params.touchEventsTarget === 'container' ? 'el' : 'wrapperEl'].style.cursor = '';\n  if (swiper.isElement) {\n    requestAnimationFrame(() => {\n      swiper.__preventObserver__ = false;\n    });\n  }\n}\n\nvar grabCursor = {\n  setGrabCursor,\n  unsetGrabCursor\n};\n\n// Modified from https://stackoverflow.com/questions/54520554/custom-element-getrootnode-closest-function-crossing-multiple-parent-shadowd\nfunction closestElement(selector, base) {\n  if (base === void 0) {\n    base = this;\n  }\n  function __closestFrom(el) {\n    if (!el || el === getDocument() || el === getWindow()) return null;\n    if (el.assignedSlot) el = el.assignedSlot;\n    const found = el.closest(selector);\n    if (!found && !el.getRootNode) {\n      return null;\n    }\n    return found || __closestFrom(el.getRootNode().host);\n  }\n  return __closestFrom(base);\n}\nfunction preventEdgeSwipe(swiper, event, startX) {\n  const window = getWindow();\n  const {\n    params\n  } = swiper;\n  const edgeSwipeDetection = params.edgeSwipeDetection;\n  const edgeSwipeThreshold = params.edgeSwipeThreshold;\n  if (edgeSwipeDetection && (startX <= edgeSwipeThreshold || startX >= window.innerWidth - edgeSwipeThreshold)) {\n    if (edgeSwipeDetection === 'prevent') {\n      event.preventDefault();\n      return true;\n    }\n    return false;\n  }\n  return true;\n}\nfunction onTouchStart(event) {\n  const swiper = this;\n  const document = getDocument();\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  const data = swiper.touchEventsData;\n  if (e.type === 'pointerdown') {\n    if (data.pointerId !== null && data.pointerId !== e.pointerId) {\n      return;\n    }\n    data.pointerId = e.pointerId;\n  } else if (e.type === 'touchstart' && e.targetTouches.length === 1) {\n    data.touchId = e.targetTouches[0].identifier;\n  }\n  if (e.type === 'touchstart') {\n    // don't proceed touch event\n    preventEdgeSwipe(swiper, e, e.targetTouches[0].pageX);\n    return;\n  }\n  const {\n    params,\n    touches,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && e.pointerType === 'mouse') return;\n  if (swiper.animating && params.preventInteractionOnTransition) {\n    return;\n  }\n  if (!swiper.animating && params.cssMode && params.loop) {\n    swiper.loopFix();\n  }\n  let targetEl = e.target;\n  if (params.touchEventsTarget === 'wrapper') {\n    if (!elementIsChildOf(targetEl, swiper.wrapperEl)) return;\n  }\n  if ('which' in e && e.which === 3) return;\n  if ('button' in e && e.button > 0) return;\n  if (data.isTouched && data.isMoved) return;\n\n  // change target el for shadow root component\n  const swipingClassHasValue = !!params.noSwipingClass && params.noSwipingClass !== '';\n  // eslint-disable-next-line\n  const eventPath = e.composedPath ? e.composedPath() : e.path;\n  if (swipingClassHasValue && e.target && e.target.shadowRoot && eventPath) {\n    targetEl = eventPath[0];\n  }\n  const noSwipingSelector = params.noSwipingSelector ? params.noSwipingSelector : `.${params.noSwipingClass}`;\n  const isTargetShadow = !!(e.target && e.target.shadowRoot);\n\n  // use closestElement for shadow root element to get the actual closest for nested shadow root element\n  if (params.noSwiping && (isTargetShadow ? closestElement(noSwipingSelector, targetEl) : targetEl.closest(noSwipingSelector))) {\n    swiper.allowClick = true;\n    return;\n  }\n  if (params.swipeHandler) {\n    if (!targetEl.closest(params.swipeHandler)) return;\n  }\n  touches.currentX = e.pageX;\n  touches.currentY = e.pageY;\n  const startX = touches.currentX;\n  const startY = touches.currentY;\n\n  // Do NOT start if iOS edge swipe is detected. Otherwise iOS app cannot swipe-to-go-back anymore\n\n  if (!preventEdgeSwipe(swiper, e, startX)) {\n    return;\n  }\n  Object.assign(data, {\n    isTouched: true,\n    isMoved: false,\n    allowTouchCallbacks: true,\n    isScrolling: undefined,\n    startMoving: undefined\n  });\n  touches.startX = startX;\n  touches.startY = startY;\n  data.touchStartTime = now();\n  swiper.allowClick = true;\n  swiper.updateSize();\n  swiper.swipeDirection = undefined;\n  if (params.threshold > 0) data.allowThresholdMove = false;\n  let preventDefault = true;\n  if (targetEl.matches(data.focusableElements)) {\n    preventDefault = false;\n    if (targetEl.nodeName === 'SELECT') {\n      data.isTouched = false;\n    }\n  }\n  if (document.activeElement && document.activeElement.matches(data.focusableElements) && document.activeElement !== targetEl && (e.pointerType === 'mouse' || e.pointerType !== 'mouse' && !targetEl.matches(data.focusableElements))) {\n    document.activeElement.blur();\n  }\n  const shouldPreventDefault = preventDefault && swiper.allowTouchMove && params.touchStartPreventDefault;\n  if ((params.touchStartForcePreventDefault || shouldPreventDefault) && !targetEl.isContentEditable) {\n    e.preventDefault();\n  }\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode && swiper.animating && !params.cssMode) {\n    swiper.freeMode.onTouchStart();\n  }\n  swiper.emit('touchStart', e);\n}\n\nfunction onTouchMove(event) {\n  const document = getDocument();\n  const swiper = this;\n  const data = swiper.touchEventsData;\n  const {\n    params,\n    touches,\n    rtlTranslate: rtl,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && event.pointerType === 'mouse') return;\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  if (e.type === 'pointermove') {\n    if (data.touchId !== null) return; // return from pointer if we use touch\n    const id = e.pointerId;\n    if (id !== data.pointerId) return;\n  }\n  let targetTouch;\n  if (e.type === 'touchmove') {\n    targetTouch = [...e.changedTouches].find(t => t.identifier === data.touchId);\n    if (!targetTouch || targetTouch.identifier !== data.touchId) return;\n  } else {\n    targetTouch = e;\n  }\n  if (!data.isTouched) {\n    if (data.startMoving && data.isScrolling) {\n      swiper.emit('touchMoveOpposite', e);\n    }\n    return;\n  }\n  const pageX = targetTouch.pageX;\n  const pageY = targetTouch.pageY;\n  if (e.preventedByNestedSwiper) {\n    touches.startX = pageX;\n    touches.startY = pageY;\n    return;\n  }\n  if (!swiper.allowTouchMove) {\n    if (!e.target.matches(data.focusableElements)) {\n      swiper.allowClick = false;\n    }\n    if (data.isTouched) {\n      Object.assign(touches, {\n        startX: pageX,\n        startY: pageY,\n        currentX: pageX,\n        currentY: pageY\n      });\n      data.touchStartTime = now();\n    }\n    return;\n  }\n  if (params.touchReleaseOnEdges && !params.loop) {\n    if (swiper.isVertical()) {\n      // Vertical\n      if (pageY < touches.startY && swiper.translate <= swiper.maxTranslate() || pageY > touches.startY && swiper.translate >= swiper.minTranslate()) {\n        data.isTouched = false;\n        data.isMoved = false;\n        return;\n      }\n    } else if (rtl && (pageX > touches.startX && -swiper.translate <= swiper.maxTranslate() || pageX < touches.startX && -swiper.translate >= swiper.minTranslate())) {\n      return;\n    } else if (!rtl && (pageX < touches.startX && swiper.translate <= swiper.maxTranslate() || pageX > touches.startX && swiper.translate >= swiper.minTranslate())) {\n      return;\n    }\n  }\n  if (document.activeElement && document.activeElement.matches(data.focusableElements) && document.activeElement !== e.target && e.pointerType !== 'mouse') {\n    document.activeElement.blur();\n  }\n  if (document.activeElement) {\n    if (e.target === document.activeElement && e.target.matches(data.focusableElements)) {\n      data.isMoved = true;\n      swiper.allowClick = false;\n      return;\n    }\n  }\n  if (data.allowTouchCallbacks) {\n    swiper.emit('touchMove', e);\n  }\n  touches.previousX = touches.currentX;\n  touches.previousY = touches.currentY;\n  touches.currentX = pageX;\n  touches.currentY = pageY;\n  const diffX = touches.currentX - touches.startX;\n  const diffY = touches.currentY - touches.startY;\n  if (swiper.params.threshold && Math.sqrt(diffX ** 2 + diffY ** 2) < swiper.params.threshold) return;\n  if (typeof data.isScrolling === 'undefined') {\n    let touchAngle;\n    if (swiper.isHorizontal() && touches.currentY === touches.startY || swiper.isVertical() && touches.currentX === touches.startX) {\n      data.isScrolling = false;\n    } else {\n      // eslint-disable-next-line\n      if (diffX * diffX + diffY * diffY >= 25) {\n        touchAngle = Math.atan2(Math.abs(diffY), Math.abs(diffX)) * 180 / Math.PI;\n        data.isScrolling = swiper.isHorizontal() ? touchAngle > params.touchAngle : 90 - touchAngle > params.touchAngle;\n      }\n    }\n  }\n  if (data.isScrolling) {\n    swiper.emit('touchMoveOpposite', e);\n  }\n  if (typeof data.startMoving === 'undefined') {\n    if (touches.currentX !== touches.startX || touches.currentY !== touches.startY) {\n      data.startMoving = true;\n    }\n  }\n  if (data.isScrolling || e.type === 'touchmove' && data.preventTouchMoveFromPointerMove) {\n    data.isTouched = false;\n    return;\n  }\n  if (!data.startMoving) {\n    return;\n  }\n  swiper.allowClick = false;\n  if (!params.cssMode && e.cancelable) {\n    e.preventDefault();\n  }\n  if (params.touchMoveStopPropagation && !params.nested) {\n    e.stopPropagation();\n  }\n  let diff = swiper.isHorizontal() ? diffX : diffY;\n  let touchesDiff = swiper.isHorizontal() ? touches.currentX - touches.previousX : touches.currentY - touches.previousY;\n  if (params.oneWayMovement) {\n    diff = Math.abs(diff) * (rtl ? 1 : -1);\n    touchesDiff = Math.abs(touchesDiff) * (rtl ? 1 : -1);\n  }\n  touches.diff = diff;\n  diff *= params.touchRatio;\n  if (rtl) {\n    diff = -diff;\n    touchesDiff = -touchesDiff;\n  }\n  const prevTouchesDirection = swiper.touchesDirection;\n  swiper.swipeDirection = diff > 0 ? 'prev' : 'next';\n  swiper.touchesDirection = touchesDiff > 0 ? 'prev' : 'next';\n  const isLoop = swiper.params.loop && !params.cssMode;\n  const allowLoopFix = swiper.touchesDirection === 'next' && swiper.allowSlideNext || swiper.touchesDirection === 'prev' && swiper.allowSlidePrev;\n  if (!data.isMoved) {\n    if (isLoop && allowLoopFix) {\n      swiper.loopFix({\n        direction: swiper.swipeDirection\n      });\n    }\n    data.startTranslate = swiper.getTranslate();\n    swiper.setTransition(0);\n    if (swiper.animating) {\n      const evt = new window.CustomEvent('transitionend', {\n        bubbles: true,\n        cancelable: true,\n        detail: {\n          bySwiperTouchMove: true\n        }\n      });\n      swiper.wrapperEl.dispatchEvent(evt);\n    }\n    data.allowMomentumBounce = false;\n    // Grab Cursor\n    if (params.grabCursor && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n      swiper.setGrabCursor(true);\n    }\n    swiper.emit('sliderFirstMove', e);\n  }\n  let loopFixed;\n  new Date().getTime();\n  if (params._loopSwapReset !== false && data.isMoved && data.allowThresholdMove && prevTouchesDirection !== swiper.touchesDirection && isLoop && allowLoopFix && Math.abs(diff) >= 1) {\n    Object.assign(touches, {\n      startX: pageX,\n      startY: pageY,\n      currentX: pageX,\n      currentY: pageY,\n      startTranslate: data.currentTranslate\n    });\n    data.loopSwapReset = true;\n    data.startTranslate = data.currentTranslate;\n    return;\n  }\n  swiper.emit('sliderMove', e);\n  data.isMoved = true;\n  data.currentTranslate = diff + data.startTranslate;\n  let disableParentSwiper = true;\n  let resistanceRatio = params.resistanceRatio;\n  if (params.touchReleaseOnEdges) {\n    resistanceRatio = 0;\n  }\n  if (diff > 0) {\n    if (isLoop && allowLoopFix && !loopFixed && data.allowThresholdMove && data.currentTranslate > (params.centeredSlides ? swiper.minTranslate() - swiper.slidesSizesGrid[swiper.activeIndex + 1] - (params.slidesPerView !== 'auto' && swiper.slides.length - params.slidesPerView >= 2 ? swiper.slidesSizesGrid[swiper.activeIndex + 1] + swiper.params.spaceBetween : 0) - swiper.params.spaceBetween : swiper.minTranslate())) {\n      swiper.loopFix({\n        direction: 'prev',\n        setTranslate: true,\n        activeSlideIndex: 0\n      });\n    }\n    if (data.currentTranslate > swiper.minTranslate()) {\n      disableParentSwiper = false;\n      if (params.resistance) {\n        data.currentTranslate = swiper.minTranslate() - 1 + (-swiper.minTranslate() + data.startTranslate + diff) ** resistanceRatio;\n      }\n    }\n  } else if (diff < 0) {\n    if (isLoop && allowLoopFix && !loopFixed && data.allowThresholdMove && data.currentTranslate < (params.centeredSlides ? swiper.maxTranslate() + swiper.slidesSizesGrid[swiper.slidesSizesGrid.length - 1] + swiper.params.spaceBetween + (params.slidesPerView !== 'auto' && swiper.slides.length - params.slidesPerView >= 2 ? swiper.slidesSizesGrid[swiper.slidesSizesGrid.length - 1] + swiper.params.spaceBetween : 0) : swiper.maxTranslate())) {\n      swiper.loopFix({\n        direction: 'next',\n        setTranslate: true,\n        activeSlideIndex: swiper.slides.length - (params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : Math.ceil(parseFloat(params.slidesPerView, 10)))\n      });\n    }\n    if (data.currentTranslate < swiper.maxTranslate()) {\n      disableParentSwiper = false;\n      if (params.resistance) {\n        data.currentTranslate = swiper.maxTranslate() + 1 - (swiper.maxTranslate() - data.startTranslate - diff) ** resistanceRatio;\n      }\n    }\n  }\n  if (disableParentSwiper) {\n    e.preventedByNestedSwiper = true;\n  }\n\n  // Directions locks\n  if (!swiper.allowSlideNext && swiper.swipeDirection === 'next' && data.currentTranslate < data.startTranslate) {\n    data.currentTranslate = data.startTranslate;\n  }\n  if (!swiper.allowSlidePrev && swiper.swipeDirection === 'prev' && data.currentTranslate > data.startTranslate) {\n    data.currentTranslate = data.startTranslate;\n  }\n  if (!swiper.allowSlidePrev && !swiper.allowSlideNext) {\n    data.currentTranslate = data.startTranslate;\n  }\n\n  // Threshold\n  if (params.threshold > 0) {\n    if (Math.abs(diff) > params.threshold || data.allowThresholdMove) {\n      if (!data.allowThresholdMove) {\n        data.allowThresholdMove = true;\n        touches.startX = touches.currentX;\n        touches.startY = touches.currentY;\n        data.currentTranslate = data.startTranslate;\n        touches.diff = swiper.isHorizontal() ? touches.currentX - touches.startX : touches.currentY - touches.startY;\n        return;\n      }\n    } else {\n      data.currentTranslate = data.startTranslate;\n      return;\n    }\n  }\n  if (!params.followFinger || params.cssMode) return;\n\n  // Update active index in free mode\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode || params.watchSlidesProgress) {\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode) {\n    swiper.freeMode.onTouchMove();\n  }\n  // Update progress\n  swiper.updateProgress(data.currentTranslate);\n  // Update translate\n  swiper.setTranslate(data.currentTranslate);\n}\n\nfunction onTouchEnd(event) {\n  const swiper = this;\n  const data = swiper.touchEventsData;\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  let targetTouch;\n  const isTouchEvent = e.type === 'touchend' || e.type === 'touchcancel';\n  if (!isTouchEvent) {\n    if (data.touchId !== null) return; // return from pointer if we use touch\n    if (e.pointerId !== data.pointerId) return;\n    targetTouch = e;\n  } else {\n    targetTouch = [...e.changedTouches].find(t => t.identifier === data.touchId);\n    if (!targetTouch || targetTouch.identifier !== data.touchId) return;\n  }\n  if (['pointercancel', 'pointerout', 'pointerleave', 'contextmenu'].includes(e.type)) {\n    const proceed = ['pointercancel', 'contextmenu'].includes(e.type) && (swiper.browser.isSafari || swiper.browser.isWebView);\n    if (!proceed) {\n      return;\n    }\n  }\n  data.pointerId = null;\n  data.touchId = null;\n  const {\n    params,\n    touches,\n    rtlTranslate: rtl,\n    slidesGrid,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && e.pointerType === 'mouse') return;\n  if (data.allowTouchCallbacks) {\n    swiper.emit('touchEnd', e);\n  }\n  data.allowTouchCallbacks = false;\n  if (!data.isTouched) {\n    if (data.isMoved && params.grabCursor) {\n      swiper.setGrabCursor(false);\n    }\n    data.isMoved = false;\n    data.startMoving = false;\n    return;\n  }\n\n  // Return Grab Cursor\n  if (params.grabCursor && data.isMoved && data.isTouched && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n    swiper.setGrabCursor(false);\n  }\n\n  // Time diff\n  const touchEndTime = now();\n  const timeDiff = touchEndTime - data.touchStartTime;\n\n  // Tap, doubleTap, Click\n  if (swiper.allowClick) {\n    const pathTree = e.path || e.composedPath && e.composedPath();\n    swiper.updateClickedSlide(pathTree && pathTree[0] || e.target, pathTree);\n    swiper.emit('tap click', e);\n    if (timeDiff < 300 && touchEndTime - data.lastClickTime < 300) {\n      swiper.emit('doubleTap doubleClick', e);\n    }\n  }\n  data.lastClickTime = now();\n  nextTick(() => {\n    if (!swiper.destroyed) swiper.allowClick = true;\n  });\n  if (!data.isTouched || !data.isMoved || !swiper.swipeDirection || touches.diff === 0 && !data.loopSwapReset || data.currentTranslate === data.startTranslate && !data.loopSwapReset) {\n    data.isTouched = false;\n    data.isMoved = false;\n    data.startMoving = false;\n    return;\n  }\n  data.isTouched = false;\n  data.isMoved = false;\n  data.startMoving = false;\n  let currentPos;\n  if (params.followFinger) {\n    currentPos = rtl ? swiper.translate : -swiper.translate;\n  } else {\n    currentPos = -data.currentTranslate;\n  }\n  if (params.cssMode) {\n    return;\n  }\n  if (params.freeMode && params.freeMode.enabled) {\n    swiper.freeMode.onTouchEnd({\n      currentPos\n    });\n    return;\n  }\n\n  // Find current slide\n  const swipeToLast = currentPos >= -swiper.maxTranslate() && !swiper.params.loop;\n  let stopIndex = 0;\n  let groupSize = swiper.slidesSizesGrid[0];\n  for (let i = 0; i < slidesGrid.length; i += i < params.slidesPerGroupSkip ? 1 : params.slidesPerGroup) {\n    const increment = i < params.slidesPerGroupSkip - 1 ? 1 : params.slidesPerGroup;\n    if (typeof slidesGrid[i + increment] !== 'undefined') {\n      if (swipeToLast || currentPos >= slidesGrid[i] && currentPos < slidesGrid[i + increment]) {\n        stopIndex = i;\n        groupSize = slidesGrid[i + increment] - slidesGrid[i];\n      }\n    } else if (swipeToLast || currentPos >= slidesGrid[i]) {\n      stopIndex = i;\n      groupSize = slidesGrid[slidesGrid.length - 1] - slidesGrid[slidesGrid.length - 2];\n    }\n  }\n  let rewindFirstIndex = null;\n  let rewindLastIndex = null;\n  if (params.rewind) {\n    if (swiper.isBeginning) {\n      rewindLastIndex = params.virtual && params.virtual.enabled && swiper.virtual ? swiper.virtual.slides.length - 1 : swiper.slides.length - 1;\n    } else if (swiper.isEnd) {\n      rewindFirstIndex = 0;\n    }\n  }\n  // Find current slide size\n  const ratio = (currentPos - slidesGrid[stopIndex]) / groupSize;\n  const increment = stopIndex < params.slidesPerGroupSkip - 1 ? 1 : params.slidesPerGroup;\n  if (timeDiff > params.longSwipesMs) {\n    // Long touches\n    if (!params.longSwipes) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n    if (swiper.swipeDirection === 'next') {\n      if (ratio >= params.longSwipesRatio) swiper.slideTo(params.rewind && swiper.isEnd ? rewindFirstIndex : stopIndex + increment);else swiper.slideTo(stopIndex);\n    }\n    if (swiper.swipeDirection === 'prev') {\n      if (ratio > 1 - params.longSwipesRatio) {\n        swiper.slideTo(stopIndex + increment);\n      } else if (rewindLastIndex !== null && ratio < 0 && Math.abs(ratio) > params.longSwipesRatio) {\n        swiper.slideTo(rewindLastIndex);\n      } else {\n        swiper.slideTo(stopIndex);\n      }\n    }\n  } else {\n    // Short swipes\n    if (!params.shortSwipes) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n    const isNavButtonTarget = swiper.navigation && (e.target === swiper.navigation.nextEl || e.target === swiper.navigation.prevEl);\n    if (!isNavButtonTarget) {\n      if (swiper.swipeDirection === 'next') {\n        swiper.slideTo(rewindFirstIndex !== null ? rewindFirstIndex : stopIndex + increment);\n      }\n      if (swiper.swipeDirection === 'prev') {\n        swiper.slideTo(rewindLastIndex !== null ? rewindLastIndex : stopIndex);\n      }\n    } else if (e.target === swiper.navigation.nextEl) {\n      swiper.slideTo(stopIndex + increment);\n    } else {\n      swiper.slideTo(stopIndex);\n    }\n  }\n}\n\nfunction onResize() {\n  const swiper = this;\n  const {\n    params,\n    el\n  } = swiper;\n  if (el && el.offsetWidth === 0) return;\n\n  // Breakpoints\n  if (params.breakpoints) {\n    swiper.setBreakpoint();\n  }\n\n  // Save locks\n  const {\n    allowSlideNext,\n    allowSlidePrev,\n    snapGrid\n  } = swiper;\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n\n  // Disable locks on resize\n  swiper.allowSlideNext = true;\n  swiper.allowSlidePrev = true;\n  swiper.updateSize();\n  swiper.updateSlides();\n  swiper.updateSlidesClasses();\n  const isVirtualLoop = isVirtual && params.loop;\n  if ((params.slidesPerView === 'auto' || params.slidesPerView > 1) && swiper.isEnd && !swiper.isBeginning && !swiper.params.centeredSlides && !isVirtualLoop) {\n    swiper.slideTo(swiper.slides.length - 1, 0, false, true);\n  } else {\n    if (swiper.params.loop && !isVirtual) {\n      swiper.slideToLoop(swiper.realIndex, 0, false, true);\n    } else {\n      swiper.slideTo(swiper.activeIndex, 0, false, true);\n    }\n  }\n  if (swiper.autoplay && swiper.autoplay.running && swiper.autoplay.paused) {\n    clearTimeout(swiper.autoplay.resizeTimeout);\n    swiper.autoplay.resizeTimeout = setTimeout(() => {\n      if (swiper.autoplay && swiper.autoplay.running && swiper.autoplay.paused) {\n        swiper.autoplay.resume();\n      }\n    }, 500);\n  }\n  // Return locks after resize\n  swiper.allowSlidePrev = allowSlidePrev;\n  swiper.allowSlideNext = allowSlideNext;\n  if (swiper.params.watchOverflow && snapGrid !== swiper.snapGrid) {\n    swiper.checkOverflow();\n  }\n}\n\nfunction onClick(e) {\n  const swiper = this;\n  if (!swiper.enabled) return;\n  if (!swiper.allowClick) {\n    if (swiper.params.preventClicks) e.preventDefault();\n    if (swiper.params.preventClicksPropagation && swiper.animating) {\n      e.stopPropagation();\n      e.stopImmediatePropagation();\n    }\n  }\n}\n\nfunction onScroll() {\n  const swiper = this;\n  const {\n    wrapperEl,\n    rtlTranslate,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  swiper.previousTranslate = swiper.translate;\n  if (swiper.isHorizontal()) {\n    swiper.translate = -wrapperEl.scrollLeft;\n  } else {\n    swiper.translate = -wrapperEl.scrollTop;\n  }\n  // eslint-disable-next-line\n  if (swiper.translate === 0) swiper.translate = 0;\n  swiper.updateActiveIndex();\n  swiper.updateSlidesClasses();\n  let newProgress;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  if (translatesDiff === 0) {\n    newProgress = 0;\n  } else {\n    newProgress = (swiper.translate - swiper.minTranslate()) / translatesDiff;\n  }\n  if (newProgress !== swiper.progress) {\n    swiper.updateProgress(rtlTranslate ? -swiper.translate : swiper.translate);\n  }\n  swiper.emit('setTranslate', swiper.translate, false);\n}\n\nfunction onLoad(e) {\n  const swiper = this;\n  processLazyPreloader(swiper, e.target);\n  if (swiper.params.cssMode || swiper.params.slidesPerView !== 'auto' && !swiper.params.autoHeight) {\n    return;\n  }\n  swiper.update();\n}\n\nfunction onDocumentTouchStart() {\n  const swiper = this;\n  if (swiper.documentTouchHandlerProceeded) return;\n  swiper.documentTouchHandlerProceeded = true;\n  if (swiper.params.touchReleaseOnEdges) {\n    swiper.el.style.touchAction = 'auto';\n  }\n}\n\nconst events = (swiper, method) => {\n  const document = getDocument();\n  const {\n    params,\n    el,\n    wrapperEl,\n    device\n  } = swiper;\n  const capture = !!params.nested;\n  const domMethod = method === 'on' ? 'addEventListener' : 'removeEventListener';\n  const swiperMethod = method;\n  if (!el || typeof el === 'string') return;\n\n  // Touch Events\n  document[domMethod]('touchstart', swiper.onDocumentTouchStart, {\n    passive: false,\n    capture\n  });\n  el[domMethod]('touchstart', swiper.onTouchStart, {\n    passive: false\n  });\n  el[domMethod]('pointerdown', swiper.onTouchStart, {\n    passive: false\n  });\n  document[domMethod]('touchmove', swiper.onTouchMove, {\n    passive: false,\n    capture\n  });\n  document[domMethod]('pointermove', swiper.onTouchMove, {\n    passive: false,\n    capture\n  });\n  document[domMethod]('touchend', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointerup', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointercancel', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('touchcancel', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointerout', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointerleave', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('contextmenu', swiper.onTouchEnd, {\n    passive: true\n  });\n\n  // Prevent Links Clicks\n  if (params.preventClicks || params.preventClicksPropagation) {\n    el[domMethod]('click', swiper.onClick, true);\n  }\n  if (params.cssMode) {\n    wrapperEl[domMethod]('scroll', swiper.onScroll);\n  }\n\n  // Resize handler\n  if (params.updateOnWindowResize) {\n    swiper[swiperMethod](device.ios || device.android ? 'resize orientationchange observerUpdate' : 'resize observerUpdate', onResize, true);\n  } else {\n    swiper[swiperMethod]('observerUpdate', onResize, true);\n  }\n\n  // Images loader\n  el[domMethod]('load', swiper.onLoad, {\n    capture: true\n  });\n};\nfunction attachEvents() {\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  swiper.onTouchStart = onTouchStart.bind(swiper);\n  swiper.onTouchMove = onTouchMove.bind(swiper);\n  swiper.onTouchEnd = onTouchEnd.bind(swiper);\n  swiper.onDocumentTouchStart = onDocumentTouchStart.bind(swiper);\n  if (params.cssMode) {\n    swiper.onScroll = onScroll.bind(swiper);\n  }\n  swiper.onClick = onClick.bind(swiper);\n  swiper.onLoad = onLoad.bind(swiper);\n  events(swiper, 'on');\n}\nfunction detachEvents() {\n  const swiper = this;\n  events(swiper, 'off');\n}\nvar events$1 = {\n  attachEvents,\n  detachEvents\n};\n\nconst isGridEnabled = (swiper, params) => {\n  return swiper.grid && params.grid && params.grid.rows > 1;\n};\nfunction setBreakpoint() {\n  const swiper = this;\n  const {\n    realIndex,\n    initialized,\n    params,\n    el\n  } = swiper;\n  const breakpoints = params.breakpoints;\n  if (!breakpoints || breakpoints && Object.keys(breakpoints).length === 0) return;\n  const document = getDocument();\n\n  // Get breakpoint for window/container width and update parameters\n  const breakpointsBase = params.breakpointsBase === 'window' || !params.breakpointsBase ? params.breakpointsBase : 'container';\n  const breakpointContainer = ['window', 'container'].includes(params.breakpointsBase) || !params.breakpointsBase ? swiper.el : document.querySelector(params.breakpointsBase);\n  const breakpoint = swiper.getBreakpoint(breakpoints, breakpointsBase, breakpointContainer);\n  if (!breakpoint || swiper.currentBreakpoint === breakpoint) return;\n  const breakpointOnlyParams = breakpoint in breakpoints ? breakpoints[breakpoint] : undefined;\n  const breakpointParams = breakpointOnlyParams || swiper.originalParams;\n  const wasMultiRow = isGridEnabled(swiper, params);\n  const isMultiRow = isGridEnabled(swiper, breakpointParams);\n  const wasGrabCursor = swiper.params.grabCursor;\n  const isGrabCursor = breakpointParams.grabCursor;\n  const wasEnabled = params.enabled;\n  if (wasMultiRow && !isMultiRow) {\n    el.classList.remove(`${params.containerModifierClass}grid`, `${params.containerModifierClass}grid-column`);\n    swiper.emitContainerClasses();\n  } else if (!wasMultiRow && isMultiRow) {\n    el.classList.add(`${params.containerModifierClass}grid`);\n    if (breakpointParams.grid.fill && breakpointParams.grid.fill === 'column' || !breakpointParams.grid.fill && params.grid.fill === 'column') {\n      el.classList.add(`${params.containerModifierClass}grid-column`);\n    }\n    swiper.emitContainerClasses();\n  }\n  if (wasGrabCursor && !isGrabCursor) {\n    swiper.unsetGrabCursor();\n  } else if (!wasGrabCursor && isGrabCursor) {\n    swiper.setGrabCursor();\n  }\n\n  // Toggle navigation, pagination, scrollbar\n  ['navigation', 'pagination', 'scrollbar'].forEach(prop => {\n    if (typeof breakpointParams[prop] === 'undefined') return;\n    const wasModuleEnabled = params[prop] && params[prop].enabled;\n    const isModuleEnabled = breakpointParams[prop] && breakpointParams[prop].enabled;\n    if (wasModuleEnabled && !isModuleEnabled) {\n      swiper[prop].disable();\n    }\n    if (!wasModuleEnabled && isModuleEnabled) {\n      swiper[prop].enable();\n    }\n  });\n  const directionChanged = breakpointParams.direction && breakpointParams.direction !== params.direction;\n  const needsReLoop = params.loop && (breakpointParams.slidesPerView !== params.slidesPerView || directionChanged);\n  const wasLoop = params.loop;\n  if (directionChanged && initialized) {\n    swiper.changeDirection();\n  }\n  extend(swiper.params, breakpointParams);\n  const isEnabled = swiper.params.enabled;\n  const hasLoop = swiper.params.loop;\n  Object.assign(swiper, {\n    allowTouchMove: swiper.params.allowTouchMove,\n    allowSlideNext: swiper.params.allowSlideNext,\n    allowSlidePrev: swiper.params.allowSlidePrev\n  });\n  if (wasEnabled && !isEnabled) {\n    swiper.disable();\n  } else if (!wasEnabled && isEnabled) {\n    swiper.enable();\n  }\n  swiper.currentBreakpoint = breakpoint;\n  swiper.emit('_beforeBreakpoint', breakpointParams);\n  if (initialized) {\n    if (needsReLoop) {\n      swiper.loopDestroy();\n      swiper.loopCreate(realIndex);\n      swiper.updateSlides();\n    } else if (!wasLoop && hasLoop) {\n      swiper.loopCreate(realIndex);\n      swiper.updateSlides();\n    } else if (wasLoop && !hasLoop) {\n      swiper.loopDestroy();\n    }\n  }\n  swiper.emit('breakpoint', breakpointParams);\n}\n\nfunction getBreakpoint(breakpoints, base, containerEl) {\n  if (base === void 0) {\n    base = 'window';\n  }\n  if (!breakpoints || base === 'container' && !containerEl) return undefined;\n  let breakpoint = false;\n  const window = getWindow();\n  const currentHeight = base === 'window' ? window.innerHeight : containerEl.clientHeight;\n  const points = Object.keys(breakpoints).map(point => {\n    if (typeof point === 'string' && point.indexOf('@') === 0) {\n      const minRatio = parseFloat(point.substr(1));\n      const value = currentHeight * minRatio;\n      return {\n        value,\n        point\n      };\n    }\n    return {\n      value: point,\n      point\n    };\n  });\n  points.sort((a, b) => parseInt(a.value, 10) - parseInt(b.value, 10));\n  for (let i = 0; i < points.length; i += 1) {\n    const {\n      point,\n      value\n    } = points[i];\n    if (base === 'window') {\n      if (window.matchMedia(`(min-width: ${value}px)`).matches) {\n        breakpoint = point;\n      }\n    } else if (value <= containerEl.clientWidth) {\n      breakpoint = point;\n    }\n  }\n  return breakpoint || 'max';\n}\n\nvar breakpoints = {\n  setBreakpoint,\n  getBreakpoint\n};\n\nfunction prepareClasses(entries, prefix) {\n  const resultClasses = [];\n  entries.forEach(item => {\n    if (typeof item === 'object') {\n      Object.keys(item).forEach(classNames => {\n        if (item[classNames]) {\n          resultClasses.push(prefix + classNames);\n        }\n      });\n    } else if (typeof item === 'string') {\n      resultClasses.push(prefix + item);\n    }\n  });\n  return resultClasses;\n}\nfunction addClasses() {\n  const swiper = this;\n  const {\n    classNames,\n    params,\n    rtl,\n    el,\n    device\n  } = swiper;\n  // prettier-ignore\n  const suffixes = prepareClasses(['initialized', params.direction, {\n    'free-mode': swiper.params.freeMode && params.freeMode.enabled\n  }, {\n    'autoheight': params.autoHeight\n  }, {\n    'rtl': rtl\n  }, {\n    'grid': params.grid && params.grid.rows > 1\n  }, {\n    'grid-column': params.grid && params.grid.rows > 1 && params.grid.fill === 'column'\n  }, {\n    'android': device.android\n  }, {\n    'ios': device.ios\n  }, {\n    'css-mode': params.cssMode\n  }, {\n    'centered': params.cssMode && params.centeredSlides\n  }, {\n    'watch-progress': params.watchSlidesProgress\n  }], params.containerModifierClass);\n  classNames.push(...suffixes);\n  el.classList.add(...classNames);\n  swiper.emitContainerClasses();\n}\n\nfunction removeClasses() {\n  const swiper = this;\n  const {\n    el,\n    classNames\n  } = swiper;\n  if (!el || typeof el === 'string') return;\n  el.classList.remove(...classNames);\n  swiper.emitContainerClasses();\n}\n\nvar classes = {\n  addClasses,\n  removeClasses\n};\n\nfunction checkOverflow() {\n  const swiper = this;\n  const {\n    isLocked: wasLocked,\n    params\n  } = swiper;\n  const {\n    slidesOffsetBefore\n  } = params;\n  if (slidesOffsetBefore) {\n    const lastSlideIndex = swiper.slides.length - 1;\n    const lastSlideRightEdge = swiper.slidesGrid[lastSlideIndex] + swiper.slidesSizesGrid[lastSlideIndex] + slidesOffsetBefore * 2;\n    swiper.isLocked = swiper.size > lastSlideRightEdge;\n  } else {\n    swiper.isLocked = swiper.snapGrid.length === 1;\n  }\n  if (params.allowSlideNext === true) {\n    swiper.allowSlideNext = !swiper.isLocked;\n  }\n  if (params.allowSlidePrev === true) {\n    swiper.allowSlidePrev = !swiper.isLocked;\n  }\n  if (wasLocked && wasLocked !== swiper.isLocked) {\n    swiper.isEnd = false;\n  }\n  if (wasLocked !== swiper.isLocked) {\n    swiper.emit(swiper.isLocked ? 'lock' : 'unlock');\n  }\n}\nvar checkOverflow$1 = {\n  checkOverflow\n};\n\nvar defaults = {\n  init: true,\n  direction: 'horizontal',\n  oneWayMovement: false,\n  swiperElementNodeName: 'SWIPER-CONTAINER',\n  touchEventsTarget: 'wrapper',\n  initialSlide: 0,\n  speed: 300,\n  cssMode: false,\n  updateOnWindowResize: true,\n  resizeObserver: true,\n  nested: false,\n  createElements: false,\n  eventsPrefix: 'swiper',\n  enabled: true,\n  focusableElements: 'input, select, option, textarea, button, video, label',\n  // Overrides\n  width: null,\n  height: null,\n  //\n  preventInteractionOnTransition: false,\n  // ssr\n  userAgent: null,\n  url: null,\n  // To support iOS's swipe-to-go-back gesture (when being used in-app).\n  edgeSwipeDetection: false,\n  edgeSwipeThreshold: 20,\n  // Autoheight\n  autoHeight: false,\n  // Set wrapper width\n  setWrapperSize: false,\n  // Virtual Translate\n  virtualTranslate: false,\n  // Effects\n  effect: 'slide',\n  // 'slide' or 'fade' or 'cube' or 'coverflow' or 'flip'\n\n  // Breakpoints\n  breakpoints: undefined,\n  breakpointsBase: 'window',\n  // Slides grid\n  spaceBetween: 0,\n  slidesPerView: 1,\n  slidesPerGroup: 1,\n  slidesPerGroupSkip: 0,\n  slidesPerGroupAuto: false,\n  centeredSlides: false,\n  centeredSlidesBounds: false,\n  slidesOffsetBefore: 0,\n  // in px\n  slidesOffsetAfter: 0,\n  // in px\n  normalizeSlideIndex: true,\n  centerInsufficientSlides: false,\n  // Disable swiper and hide navigation when container not overflow\n  watchOverflow: true,\n  // Round length\n  roundLengths: false,\n  // Touches\n  touchRatio: 1,\n  touchAngle: 45,\n  simulateTouch: true,\n  shortSwipes: true,\n  longSwipes: true,\n  longSwipesRatio: 0.5,\n  longSwipesMs: 300,\n  followFinger: true,\n  allowTouchMove: true,\n  threshold: 5,\n  touchMoveStopPropagation: false,\n  touchStartPreventDefault: true,\n  touchStartForcePreventDefault: false,\n  touchReleaseOnEdges: false,\n  // Unique Navigation Elements\n  uniqueNavElements: true,\n  // Resistance\n  resistance: true,\n  resistanceRatio: 0.85,\n  // Progress\n  watchSlidesProgress: false,\n  // Cursor\n  grabCursor: false,\n  // Clicks\n  preventClicks: true,\n  preventClicksPropagation: true,\n  slideToClickedSlide: false,\n  // loop\n  loop: false,\n  loopAddBlankSlides: true,\n  loopAdditionalSlides: 0,\n  loopPreventsSliding: true,\n  // rewind\n  rewind: false,\n  // Swiping/no swiping\n  allowSlidePrev: true,\n  allowSlideNext: true,\n  swipeHandler: null,\n  // '.swipe-handler',\n  noSwiping: true,\n  noSwipingClass: 'swiper-no-swiping',\n  noSwipingSelector: null,\n  // Passive Listeners\n  passiveListeners: true,\n  maxBackfaceHiddenSlides: 10,\n  // NS\n  containerModifierClass: 'swiper-',\n  // NEW\n  slideClass: 'swiper-slide',\n  slideBlankClass: 'swiper-slide-blank',\n  slideActiveClass: 'swiper-slide-active',\n  slideVisibleClass: 'swiper-slide-visible',\n  slideFullyVisibleClass: 'swiper-slide-fully-visible',\n  slideNextClass: 'swiper-slide-next',\n  slidePrevClass: 'swiper-slide-prev',\n  wrapperClass: 'swiper-wrapper',\n  lazyPreloaderClass: 'swiper-lazy-preloader',\n  lazyPreloadPrevNext: 0,\n  // Callbacks\n  runCallbacksOnInit: true,\n  // Internals\n  _emitClasses: false\n};\n\nfunction moduleExtendParams(params, allModulesParams) {\n  return function extendParams(obj) {\n    if (obj === void 0) {\n      obj = {};\n    }\n    const moduleParamName = Object.keys(obj)[0];\n    const moduleParams = obj[moduleParamName];\n    if (typeof moduleParams !== 'object' || moduleParams === null) {\n      extend(allModulesParams, obj);\n      return;\n    }\n    if (params[moduleParamName] === true) {\n      params[moduleParamName] = {\n        enabled: true\n      };\n    }\n    if (moduleParamName === 'navigation' && params[moduleParamName] && params[moduleParamName].enabled && !params[moduleParamName].prevEl && !params[moduleParamName].nextEl) {\n      params[moduleParamName].auto = true;\n    }\n    if (['pagination', 'scrollbar'].indexOf(moduleParamName) >= 0 && params[moduleParamName] && params[moduleParamName].enabled && !params[moduleParamName].el) {\n      params[moduleParamName].auto = true;\n    }\n    if (!(moduleParamName in params && 'enabled' in moduleParams)) {\n      extend(allModulesParams, obj);\n      return;\n    }\n    if (typeof params[moduleParamName] === 'object' && !('enabled' in params[moduleParamName])) {\n      params[moduleParamName].enabled = true;\n    }\n    if (!params[moduleParamName]) params[moduleParamName] = {\n      enabled: false\n    };\n    extend(allModulesParams, obj);\n  };\n}\n\n/* eslint no-param-reassign: \"off\" */\nconst prototypes = {\n  eventsEmitter,\n  update,\n  translate,\n  transition,\n  slide,\n  loop,\n  grabCursor,\n  events: events$1,\n  breakpoints,\n  checkOverflow: checkOverflow$1,\n  classes\n};\nconst extendedDefaults = {};\nclass Swiper {\n  constructor() {\n    let el;\n    let params;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    if (args.length === 1 && args[0].constructor && Object.prototype.toString.call(args[0]).slice(8, -1) === 'Object') {\n      params = args[0];\n    } else {\n      [el, params] = args;\n    }\n    if (!params) params = {};\n    params = extend({}, params);\n    if (el && !params.el) params.el = el;\n    const document = getDocument();\n    if (params.el && typeof params.el === 'string' && document.querySelectorAll(params.el).length > 1) {\n      const swipers = [];\n      document.querySelectorAll(params.el).forEach(containerEl => {\n        const newParams = extend({}, params, {\n          el: containerEl\n        });\n        swipers.push(new Swiper(newParams));\n      });\n      // eslint-disable-next-line no-constructor-return\n      return swipers;\n    }\n\n    // Swiper Instance\n    const swiper = this;\n    swiper.__swiper__ = true;\n    swiper.support = getSupport();\n    swiper.device = getDevice({\n      userAgent: params.userAgent\n    });\n    swiper.browser = getBrowser();\n    swiper.eventsListeners = {};\n    swiper.eventsAnyListeners = [];\n    swiper.modules = [...swiper.__modules__];\n    if (params.modules && Array.isArray(params.modules)) {\n      swiper.modules.push(...params.modules);\n    }\n    const allModulesParams = {};\n    swiper.modules.forEach(mod => {\n      mod({\n        params,\n        swiper,\n        extendParams: moduleExtendParams(params, allModulesParams),\n        on: swiper.on.bind(swiper),\n        once: swiper.once.bind(swiper),\n        off: swiper.off.bind(swiper),\n        emit: swiper.emit.bind(swiper)\n      });\n    });\n\n    // Extend defaults with modules params\n    const swiperParams = extend({}, defaults, allModulesParams);\n\n    // Extend defaults with passed params\n    swiper.params = extend({}, swiperParams, extendedDefaults, params);\n    swiper.originalParams = extend({}, swiper.params);\n    swiper.passedParams = extend({}, params);\n\n    // add event listeners\n    if (swiper.params && swiper.params.on) {\n      Object.keys(swiper.params.on).forEach(eventName => {\n        swiper.on(eventName, swiper.params.on[eventName]);\n      });\n    }\n    if (swiper.params && swiper.params.onAny) {\n      swiper.onAny(swiper.params.onAny);\n    }\n\n    // Extend Swiper\n    Object.assign(swiper, {\n      enabled: swiper.params.enabled,\n      el,\n      // Classes\n      classNames: [],\n      // Slides\n      slides: [],\n      slidesGrid: [],\n      snapGrid: [],\n      slidesSizesGrid: [],\n      // isDirection\n      isHorizontal() {\n        return swiper.params.direction === 'horizontal';\n      },\n      isVertical() {\n        return swiper.params.direction === 'vertical';\n      },\n      // Indexes\n      activeIndex: 0,\n      realIndex: 0,\n      //\n      isBeginning: true,\n      isEnd: false,\n      // Props\n      translate: 0,\n      previousTranslate: 0,\n      progress: 0,\n      velocity: 0,\n      animating: false,\n      cssOverflowAdjustment() {\n        // Returns 0 unless `translate` is > 2**23\n        // Should be subtracted from css values to prevent overflow\n        return Math.trunc(this.translate / 2 ** 23) * 2 ** 23;\n      },\n      // Locks\n      allowSlideNext: swiper.params.allowSlideNext,\n      allowSlidePrev: swiper.params.allowSlidePrev,\n      // Touch Events\n      touchEventsData: {\n        isTouched: undefined,\n        isMoved: undefined,\n        allowTouchCallbacks: undefined,\n        touchStartTime: undefined,\n        isScrolling: undefined,\n        currentTranslate: undefined,\n        startTranslate: undefined,\n        allowThresholdMove: undefined,\n        // Form elements to match\n        focusableElements: swiper.params.focusableElements,\n        // Last click time\n        lastClickTime: 0,\n        clickTimeout: undefined,\n        // Velocities\n        velocities: [],\n        allowMomentumBounce: undefined,\n        startMoving: undefined,\n        pointerId: null,\n        touchId: null\n      },\n      // Clicks\n      allowClick: true,\n      // Touches\n      allowTouchMove: swiper.params.allowTouchMove,\n      touches: {\n        startX: 0,\n        startY: 0,\n        currentX: 0,\n        currentY: 0,\n        diff: 0\n      },\n      // Images\n      imagesToLoad: [],\n      imagesLoaded: 0\n    });\n    swiper.emit('_swiper');\n\n    // Init\n    if (swiper.params.init) {\n      swiper.init();\n    }\n\n    // Return app instance\n    // eslint-disable-next-line no-constructor-return\n    return swiper;\n  }\n  getDirectionLabel(property) {\n    if (this.isHorizontal()) {\n      return property;\n    }\n    // prettier-ignore\n    return {\n      'width': 'height',\n      'margin-top': 'margin-left',\n      'margin-bottom ': 'margin-right',\n      'margin-left': 'margin-top',\n      'margin-right': 'margin-bottom',\n      'padding-left': 'padding-top',\n      'padding-right': 'padding-bottom',\n      'marginRight': 'marginBottom'\n    }[property];\n  }\n  getSlideIndex(slideEl) {\n    const {\n      slidesEl,\n      params\n    } = this;\n    const slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n    const firstSlideIndex = elementIndex(slides[0]);\n    return elementIndex(slideEl) - firstSlideIndex;\n  }\n  getSlideIndexByData(index) {\n    return this.getSlideIndex(this.slides.find(slideEl => slideEl.getAttribute('data-swiper-slide-index') * 1 === index));\n  }\n  getSlideIndexWhenGrid(index) {\n    if (this.grid && this.params.grid && this.params.grid.rows > 1) {\n      if (this.params.grid.fill === 'column') {\n        index = Math.floor(index / this.params.grid.rows);\n      } else if (this.params.grid.fill === 'row') {\n        index = index % Math.ceil(this.slides.length / this.params.grid.rows);\n      }\n    }\n    return index;\n  }\n  recalcSlides() {\n    const swiper = this;\n    const {\n      slidesEl,\n      params\n    } = swiper;\n    swiper.slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n  }\n  enable() {\n    const swiper = this;\n    if (swiper.enabled) return;\n    swiper.enabled = true;\n    if (swiper.params.grabCursor) {\n      swiper.setGrabCursor();\n    }\n    swiper.emit('enable');\n  }\n  disable() {\n    const swiper = this;\n    if (!swiper.enabled) return;\n    swiper.enabled = false;\n    if (swiper.params.grabCursor) {\n      swiper.unsetGrabCursor();\n    }\n    swiper.emit('disable');\n  }\n  setProgress(progress, speed) {\n    const swiper = this;\n    progress = Math.min(Math.max(progress, 0), 1);\n    const min = swiper.minTranslate();\n    const max = swiper.maxTranslate();\n    const current = (max - min) * progress + min;\n    swiper.translateTo(current, typeof speed === 'undefined' ? 0 : speed);\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n  emitContainerClasses() {\n    const swiper = this;\n    if (!swiper.params._emitClasses || !swiper.el) return;\n    const cls = swiper.el.className.split(' ').filter(className => {\n      return className.indexOf('swiper') === 0 || className.indexOf(swiper.params.containerModifierClass) === 0;\n    });\n    swiper.emit('_containerClasses', cls.join(' '));\n  }\n  getSlideClasses(slideEl) {\n    const swiper = this;\n    if (swiper.destroyed) return '';\n    return slideEl.className.split(' ').filter(className => {\n      return className.indexOf('swiper-slide') === 0 || className.indexOf(swiper.params.slideClass) === 0;\n    }).join(' ');\n  }\n  emitSlidesClasses() {\n    const swiper = this;\n    if (!swiper.params._emitClasses || !swiper.el) return;\n    const updates = [];\n    swiper.slides.forEach(slideEl => {\n      const classNames = swiper.getSlideClasses(slideEl);\n      updates.push({\n        slideEl,\n        classNames\n      });\n      swiper.emit('_slideClass', slideEl, classNames);\n    });\n    swiper.emit('_slideClasses', updates);\n  }\n  slidesPerViewDynamic(view, exact) {\n    if (view === void 0) {\n      view = 'current';\n    }\n    if (exact === void 0) {\n      exact = false;\n    }\n    const swiper = this;\n    const {\n      params,\n      slides,\n      slidesGrid,\n      slidesSizesGrid,\n      size: swiperSize,\n      activeIndex\n    } = swiper;\n    let spv = 1;\n    if (typeof params.slidesPerView === 'number') return params.slidesPerView;\n    if (params.centeredSlides) {\n      let slideSize = slides[activeIndex] ? Math.ceil(slides[activeIndex].swiperSlideSize) : 0;\n      let breakLoop;\n      for (let i = activeIndex + 1; i < slides.length; i += 1) {\n        if (slides[i] && !breakLoop) {\n          slideSize += Math.ceil(slides[i].swiperSlideSize);\n          spv += 1;\n          if (slideSize > swiperSize) breakLoop = true;\n        }\n      }\n      for (let i = activeIndex - 1; i >= 0; i -= 1) {\n        if (slides[i] && !breakLoop) {\n          slideSize += slides[i].swiperSlideSize;\n          spv += 1;\n          if (slideSize > swiperSize) breakLoop = true;\n        }\n      }\n    } else {\n      // eslint-disable-next-line\n      if (view === 'current') {\n        for (let i = activeIndex + 1; i < slides.length; i += 1) {\n          const slideInView = exact ? slidesGrid[i] + slidesSizesGrid[i] - slidesGrid[activeIndex] < swiperSize : slidesGrid[i] - slidesGrid[activeIndex] < swiperSize;\n          if (slideInView) {\n            spv += 1;\n          }\n        }\n      } else {\n        // previous\n        for (let i = activeIndex - 1; i >= 0; i -= 1) {\n          const slideInView = slidesGrid[activeIndex] - slidesGrid[i] < swiperSize;\n          if (slideInView) {\n            spv += 1;\n          }\n        }\n      }\n    }\n    return spv;\n  }\n  update() {\n    const swiper = this;\n    if (!swiper || swiper.destroyed) return;\n    const {\n      snapGrid,\n      params\n    } = swiper;\n    // Breakpoints\n    if (params.breakpoints) {\n      swiper.setBreakpoint();\n    }\n    [...swiper.el.querySelectorAll('[loading=\"lazy\"]')].forEach(imageEl => {\n      if (imageEl.complete) {\n        processLazyPreloader(swiper, imageEl);\n      }\n    });\n    swiper.updateSize();\n    swiper.updateSlides();\n    swiper.updateProgress();\n    swiper.updateSlidesClasses();\n    function setTranslate() {\n      const translateValue = swiper.rtlTranslate ? swiper.translate * -1 : swiper.translate;\n      const newTranslate = Math.min(Math.max(translateValue, swiper.maxTranslate()), swiper.minTranslate());\n      swiper.setTranslate(newTranslate);\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n    }\n    let translated;\n    if (params.freeMode && params.freeMode.enabled && !params.cssMode) {\n      setTranslate();\n      if (params.autoHeight) {\n        swiper.updateAutoHeight();\n      }\n    } else {\n      if ((params.slidesPerView === 'auto' || params.slidesPerView > 1) && swiper.isEnd && !params.centeredSlides) {\n        const slides = swiper.virtual && params.virtual.enabled ? swiper.virtual.slides : swiper.slides;\n        translated = swiper.slideTo(slides.length - 1, 0, false, true);\n      } else {\n        translated = swiper.slideTo(swiper.activeIndex, 0, false, true);\n      }\n      if (!translated) {\n        setTranslate();\n      }\n    }\n    if (params.watchOverflow && snapGrid !== swiper.snapGrid) {\n      swiper.checkOverflow();\n    }\n    swiper.emit('update');\n  }\n  changeDirection(newDirection, needUpdate) {\n    if (needUpdate === void 0) {\n      needUpdate = true;\n    }\n    const swiper = this;\n    const currentDirection = swiper.params.direction;\n    if (!newDirection) {\n      // eslint-disable-next-line\n      newDirection = currentDirection === 'horizontal' ? 'vertical' : 'horizontal';\n    }\n    if (newDirection === currentDirection || newDirection !== 'horizontal' && newDirection !== 'vertical') {\n      return swiper;\n    }\n    swiper.el.classList.remove(`${swiper.params.containerModifierClass}${currentDirection}`);\n    swiper.el.classList.add(`${swiper.params.containerModifierClass}${newDirection}`);\n    swiper.emitContainerClasses();\n    swiper.params.direction = newDirection;\n    swiper.slides.forEach(slideEl => {\n      if (newDirection === 'vertical') {\n        slideEl.style.width = '';\n      } else {\n        slideEl.style.height = '';\n      }\n    });\n    swiper.emit('changeDirection');\n    if (needUpdate) swiper.update();\n    return swiper;\n  }\n  changeLanguageDirection(direction) {\n    const swiper = this;\n    if (swiper.rtl && direction === 'rtl' || !swiper.rtl && direction === 'ltr') return;\n    swiper.rtl = direction === 'rtl';\n    swiper.rtlTranslate = swiper.params.direction === 'horizontal' && swiper.rtl;\n    if (swiper.rtl) {\n      swiper.el.classList.add(`${swiper.params.containerModifierClass}rtl`);\n      swiper.el.dir = 'rtl';\n    } else {\n      swiper.el.classList.remove(`${swiper.params.containerModifierClass}rtl`);\n      swiper.el.dir = 'ltr';\n    }\n    swiper.update();\n  }\n  mount(element) {\n    const swiper = this;\n    if (swiper.mounted) return true;\n\n    // Find el\n    let el = element || swiper.params.el;\n    if (typeof el === 'string') {\n      el = document.querySelector(el);\n    }\n    if (!el) {\n      return false;\n    }\n    el.swiper = swiper;\n    if (el.parentNode && el.parentNode.host && el.parentNode.host.nodeName === swiper.params.swiperElementNodeName.toUpperCase()) {\n      swiper.isElement = true;\n    }\n    const getWrapperSelector = () => {\n      return `.${(swiper.params.wrapperClass || '').trim().split(' ').join('.')}`;\n    };\n    const getWrapper = () => {\n      if (el && el.shadowRoot && el.shadowRoot.querySelector) {\n        const res = el.shadowRoot.querySelector(getWrapperSelector());\n        // Children needs to return slot items\n        return res;\n      }\n      return elementChildren(el, getWrapperSelector())[0];\n    };\n    // Find Wrapper\n    let wrapperEl = getWrapper();\n    if (!wrapperEl && swiper.params.createElements) {\n      wrapperEl = createElement('div', swiper.params.wrapperClass);\n      el.append(wrapperEl);\n      elementChildren(el, `.${swiper.params.slideClass}`).forEach(slideEl => {\n        wrapperEl.append(slideEl);\n      });\n    }\n    Object.assign(swiper, {\n      el,\n      wrapperEl,\n      slidesEl: swiper.isElement && !el.parentNode.host.slideSlots ? el.parentNode.host : wrapperEl,\n      hostEl: swiper.isElement ? el.parentNode.host : el,\n      mounted: true,\n      // RTL\n      rtl: el.dir.toLowerCase() === 'rtl' || elementStyle(el, 'direction') === 'rtl',\n      rtlTranslate: swiper.params.direction === 'horizontal' && (el.dir.toLowerCase() === 'rtl' || elementStyle(el, 'direction') === 'rtl'),\n      wrongRTL: elementStyle(wrapperEl, 'display') === '-webkit-box'\n    });\n    return true;\n  }\n  init(el) {\n    const swiper = this;\n    if (swiper.initialized) return swiper;\n    const mounted = swiper.mount(el);\n    if (mounted === false) return swiper;\n    swiper.emit('beforeInit');\n\n    // Set breakpoint\n    if (swiper.params.breakpoints) {\n      swiper.setBreakpoint();\n    }\n\n    // Add Classes\n    swiper.addClasses();\n\n    // Update size\n    swiper.updateSize();\n\n    // Update slides\n    swiper.updateSlides();\n    if (swiper.params.watchOverflow) {\n      swiper.checkOverflow();\n    }\n\n    // Set Grab Cursor\n    if (swiper.params.grabCursor && swiper.enabled) {\n      swiper.setGrabCursor();\n    }\n\n    // Slide To Initial Slide\n    if (swiper.params.loop && swiper.virtual && swiper.params.virtual.enabled) {\n      swiper.slideTo(swiper.params.initialSlide + swiper.virtual.slidesBefore, 0, swiper.params.runCallbacksOnInit, false, true);\n    } else {\n      swiper.slideTo(swiper.params.initialSlide, 0, swiper.params.runCallbacksOnInit, false, true);\n    }\n\n    // Create loop\n    if (swiper.params.loop) {\n      swiper.loopCreate(undefined, true);\n    }\n\n    // Attach events\n    swiper.attachEvents();\n    const lazyElements = [...swiper.el.querySelectorAll('[loading=\"lazy\"]')];\n    if (swiper.isElement) {\n      lazyElements.push(...swiper.hostEl.querySelectorAll('[loading=\"lazy\"]'));\n    }\n    lazyElements.forEach(imageEl => {\n      if (imageEl.complete) {\n        processLazyPreloader(swiper, imageEl);\n      } else {\n        imageEl.addEventListener('load', e => {\n          processLazyPreloader(swiper, e.target);\n        });\n      }\n    });\n    preload(swiper);\n\n    // Init Flag\n    swiper.initialized = true;\n    preload(swiper);\n\n    // Emit\n    swiper.emit('init');\n    swiper.emit('afterInit');\n    return swiper;\n  }\n  destroy(deleteInstance, cleanStyles) {\n    if (deleteInstance === void 0) {\n      deleteInstance = true;\n    }\n    if (cleanStyles === void 0) {\n      cleanStyles = true;\n    }\n    const swiper = this;\n    const {\n      params,\n      el,\n      wrapperEl,\n      slides\n    } = swiper;\n    if (typeof swiper.params === 'undefined' || swiper.destroyed) {\n      return null;\n    }\n    swiper.emit('beforeDestroy');\n\n    // Init Flag\n    swiper.initialized = false;\n\n    // Detach events\n    swiper.detachEvents();\n\n    // Destroy loop\n    if (params.loop) {\n      swiper.loopDestroy();\n    }\n\n    // Cleanup styles\n    if (cleanStyles) {\n      swiper.removeClasses();\n      if (el && typeof el !== 'string') {\n        el.removeAttribute('style');\n      }\n      if (wrapperEl) {\n        wrapperEl.removeAttribute('style');\n      }\n      if (slides && slides.length) {\n        slides.forEach(slideEl => {\n          slideEl.classList.remove(params.slideVisibleClass, params.slideFullyVisibleClass, params.slideActiveClass, params.slideNextClass, params.slidePrevClass);\n          slideEl.removeAttribute('style');\n          slideEl.removeAttribute('data-swiper-slide-index');\n        });\n      }\n    }\n    swiper.emit('destroy');\n\n    // Detach emitter events\n    Object.keys(swiper.eventsListeners).forEach(eventName => {\n      swiper.off(eventName);\n    });\n    if (deleteInstance !== false) {\n      if (swiper.el && typeof swiper.el !== 'string') {\n        swiper.el.swiper = null;\n      }\n      deleteProps(swiper);\n    }\n    swiper.destroyed = true;\n    return null;\n  }\n  static extendDefaults(newDefaults) {\n    extend(extendedDefaults, newDefaults);\n  }\n  static get extendedDefaults() {\n    return extendedDefaults;\n  }\n  static get defaults() {\n    return defaults;\n  }\n  static installModule(mod) {\n    if (!Swiper.prototype.__modules__) Swiper.prototype.__modules__ = [];\n    const modules = Swiper.prototype.__modules__;\n    if (typeof mod === 'function' && modules.indexOf(mod) < 0) {\n      modules.push(mod);\n    }\n  }\n  static use(module) {\n    if (Array.isArray(module)) {\n      module.forEach(m => Swiper.installModule(m));\n      return Swiper;\n    }\n    Swiper.installModule(module);\n    return Swiper;\n  }\n}\nObject.keys(prototypes).forEach(prototypeGroup => {\n  Object.keys(prototypes[prototypeGroup]).forEach(protoMethod => {\n    Swiper.prototype[protoMethod] = prototypes[prototypeGroup][protoMethod];\n  });\n});\nSwiper.use([Resize, Observer]);\n\nexport { Swiper as S, defaults as d };\n", "import { extend } from 'flarum/common/extend';\r\nimport app from 'flarum/forum/app';\r\nimport HeaderPrimary from 'flarum/forum/components/HeaderPrimary';\r\n\r\nimport Swiper from 'swiper';\r\nimport EffectCoverflow from \"swiper\";\r\nimport Navigation from \"swiper\";\r\nimport Pagination from \"swiper\";\r\nimport Autoplay from \"swiper\";\r\n\r\nconst checkTime = 10;\r\nlet tronscanListLoading = false;\r\nlet tronscanList = null;\r\nlet linksQueueListLoading = false;\r\nlet linksQueueList = null;\r\nlet linksQueuePointer = 0;\r\nlet buttonsCustomizationListLoading = false;\r\nlet buttonsCustomizationList = null;\r\nconst isMobileView = mobileCheck();\r\n\r\napp.initializers.add('wusong8899-client1-header-adv', () => {\r\n    extend(HeaderPrimary.prototype, 'view', function (vnode) {\r\n        const routeName = app.current.get('routeName');\r\n\r\n        if (routeName) {\r\n            if (routeName !== \"tags\") {\r\n\r\n            } else {\r\n                attachAdvertiseHeader(vnode);\r\n            }\r\n        }\r\n    });\r\n\r\n    // extend(HeaderPrimary.prototype, 'oncreate', function (vnode) {\r\n    //     $(\"#app .App-content\").append($(\"#customFooter\"));\r\n    // });\r\n});\r\n\r\nfunction mobileCheck() {\r\n    let check = false;\r\n    (function (a) { if (/(android|bb\\d+|meego).+mobile|avantgo|bada\\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(a) || /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\\-(n|u)|c55\\/|capi|ccwa|cdm\\-|cell|chtm|cldc|cmd\\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\\-s|devi|dica|dmob|do(c|p)o|ds(12|\\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\\-|_)|g1 u|g560|gene|gf\\-5|g\\-mo|go(\\.w|od)|gr(ad|un)|haie|hcit|hd\\-(m|p|t)|hei\\-|hi(pt|ta)|hp( i|ip)|hs\\-c|ht(c(\\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\\-(20|go|ma)|i230|iac( |\\-|\\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\\/)|klon|kpt |kwc\\-|kyo(c|k)|le(no|xi)|lg( g|\\/(k|l|u)|50|54|\\-[a-w])|libw|lynx|m1\\-w|m3ga|m50\\/|ma(te|ui|xo)|mc(01|21|ca)|m\\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\\-2|po(ck|rt|se)|prox|psio|pt\\-g|qa\\-a|qc(07|12|21|32|60|\\-[2-7]|i\\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\\-|oo|p\\-)|sdk\\/|se(c(\\-|0|1)|47|mc|nd|ri)|sgh\\-|shar|sie(\\-|m)|sk\\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\\-|v\\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\\-|tdg\\-|tel(i|m)|tim\\-|t\\-mo|to(pl|sh)|ts(70|m\\-|m3|m5)|tx\\-9|up(\\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\\-|your|zeto|zte\\-/i.test(a.substr(0, 4))) check = true; })(navigator.userAgent || navigator.vendor || window.opera);\r\n    return check;\r\n};\r\n\r\nfunction parseTronscanResults(results) {\r\n    tronscanList = [];\r\n    [].push.apply(tronscanList, results);\r\n    return results;\r\n}\r\n\r\nfunction loadTronscanList() {\r\n    if (tronscanListLoading === false) {\r\n        tronscanListLoading = true;\r\n        return app.store\r\n            .find(\"syncTronscanList\")\r\n            .catch(() => { })\r\n            .then(parseTronscanResults.bind(this));\r\n    }\r\n}\r\n\r\nfunction parseButtonsCustomizationResults(results) {\r\n    buttonsCustomizationList = [];\r\n    [].push.apply(buttonsCustomizationList, results);\r\n    return results;\r\n}\r\n\r\nfunction loadButtonsCustomizationList() {\r\n    if (buttonsCustomizationListLoading === false) {\r\n        buttonsCustomizationListLoading = true;\r\n        return app.store\r\n            .find(\"buttonsCustomizationList\")\r\n            .catch(() => { })\r\n            .then(parseButtonsCustomizationResults.bind(this));\r\n    }\r\n}\r\n\r\nfunction parseLinksQueueResults(results) {\r\n    linksQueueList = [];\r\n    [].push.apply(linksQueueList, results);\r\n    return results;\r\n}\r\n\r\nfunction loadLinksQueueList() {\r\n    if (linksQueueListLoading === false) {\r\n        linksQueueListLoading = true;\r\n        return app.store\r\n            .find(\"linksQueueList\")\r\n            .catch(() => { })\r\n            .then(parseLinksQueueResults.bind(this));\r\n    }\r\n}\r\n\r\nfunction changeCategoryLayout() {\r\n    const tagTile = $(\".TagTile\");\r\n\r\n    if ($(\"#swiperTagContainer\").length === 0) {\r\n        let swiperContainer = document.createElement(\"div\");\r\n        swiperContainer.className = \"swiperTagContainer\";\r\n        swiperContainer.id = \"swiperTagContainer\";\r\n\r\n        let swiper = document.createElement(\"div\");\r\n        swiper.className = \"swiper tagSwiper\";\r\n\r\n        let TagTextOuterContainer = document.createElement(\"div\");\r\n        TagTextOuterContainer.className = \"TagTextOuterContainer\";\r\n\r\n        swiperContainer.appendChild(TagTextOuterContainer);\r\n        TagTextOuterContainer.appendChild(swiper);\r\n\r\n        let swiper_wrapper = document.createElement(\"div\");\r\n        swiper_wrapper.className = \"swiper-wrapper\";\r\n        swiper_wrapper.id = \"swiperTagWrapper\";\r\n        swiper.appendChild(swiper_wrapper);\r\n\r\n        for (let i = 0; i < tagTile.length; i++) {\r\n            let tag = tagTile[i];\r\n            let tagURL = $(tag).find(\"a\").attr(\"href\")\r\n            let tagBackground = $(tag).css(\"background\");\r\n            let tagName = $(tag).find(\".TagTile-name\").text();\r\n            let tagNameColor = $(tag).find(\".TagTile-name\").css(\"color\");\r\n            let tagDesc = $(tag).find(\".TagTile-description\").text();\r\n            let tagDescColor = $(tag).find(\".TagTile-description\").css(\"color\");\r\n\r\n            if (tagName === \"Review\") {\r\n                // tagBackground = \"url(https://k-img.picimgfield.com/live/image/games/psh_mysterymissiontothemoon-en-US.png);\";\r\n            }\r\n\r\n            let swiper_slide = document.createElement(\"div\");\r\n            swiper_slide.className = \"swiper-slide swiper-slide-tag\";\r\n            swiper_slide.innerHTML = \"<a href='\" + tagURL + \"'><div class='\" + (isMobileView ? 'swiper-slide-tag-inner-mobile' : 'swiper-slide-tag-inner') + \"' style='background:\" + tagBackground + \";background-size: cover;background-position: center;background-repeat: no-repeat;'><div style='font-weight:bold;font-size:14px;color:\" + tagNameColor + \"'>\" + tagName + \"</div></div></a>\";\r\n\r\n            swiper_wrapper.appendChild(swiper_slide);\r\n        }\r\n\r\n        $(\"#content .container .TagsPage-content\").prepend(swiperContainer);\r\n        $(TagTextOuterContainer).prepend(\"<div class='TagTextContainer'><div class='TagTextIcon'></div>中文玩家社区资讯</div>\");\r\n        $(TagTextOuterContainer).append('<div style=\"text-align:center;padding-top: 10px;\"><button class=\"Button Button--primary\" type=\"button\" style=\"font-weight: normal !important; color:#ffa000; background: #1a1d2e !important;border-radius: 2rem !important;\"><div style=\"margin-top: 5px;\" class=\"Button-label\"><img onClick=\"window.open(\\'https://kick.com/wangming886\\', \\'_blank\\')\" style=\"width: 32px;\" src=\"https://mutluresim.com/images/2023/04/10/KcgSG.png\"><img onClick=\"window.open(\\'https://m.facebook.com\\', \\'_blank\\')\" style=\"width: 32px;margin-left: 20px;\" src=\"https://mutluresim.com/images/2023/04/10/KcF6i.png\"><img onClick=\"window.open(\\'https://twitter.com/youngron131_\\', \\'_blank\\')\" style=\"width: 32px;margin-left: 20px;\" src=\"https://mutluresim.com/images/2023/04/10/KcDas.png\"><img onClick=\"window.open(\\'https://m.youtube.com/@ag8888\\',\\'_blank\\')\" style=\"width: 32px;margin-left: 20px;\" src=\"https://mutluresim.com/images/2023/04/10/KcQjd.png\"><img onClick=\"window.open(\\'https://www.instagram.com/p/CqLvh94Sk8F/?igshid=YmMyMTA2M2Y=\\', \\'_blank\\')\" style=\"width: 32px;margin-left: 20px;\" src=\"https://mutluresim.com/images/2023/04/10/KcBAL.png\"></div></button></div>');\r\n        // $(\"#content .container .TagsPage-content\").append(\"<iframe style='width: 110%;border: 0;height: 500px;margin-top: 30px;margin-left: -20px;' name='contentOnly' src='https://lg666.cc/biddingRank'></iframe>\");\r\n        $(\".TagTiles\").remove();\r\n\r\n        if (isMobileView === true) {\r\n            $(\"#app\").css(\"overflow-x\", \"hidden\");\r\n            $(\".App-content\").css(\"min-height\", \"auto\");\r\n            $(\".App-content\").css(\"background\", \"\");\r\n        }\r\n\r\n        new Swiper(\".tagSwiper\", {\r\n            loop: true,\r\n            spaceBetween: isMobileView ? 90 : 10,\r\n            slidesPerView: isMobileView ? 2 : 7,\r\n            autoplay: {\r\n                delay: 3000,\r\n                disableOnInteraction: false,\r\n            },\r\n            modules: [Autoplay]\r\n        });\r\n\r\n        addTronscan();\r\n    }\r\n}\r\n\r\nfunction addTronscan() {\r\n    let TronscanTextContainer = document.getElementById(\"TronscanTextContainer\");\r\n\r\n    if (TronscanTextContainer === null) {\r\n        TronscanTextContainer = document.createElement(\"div\");\r\n        TronscanTextContainer.id = \"TronscanTextContainer\";\r\n        TronscanTextContainer.innerHTML = \"<div class='TronscanTextIcon'></div>知名博彩公司USDT/TRC公开链钱包额度\";\r\n        TronscanTextContainer.className = \"TronscanTextContainer\";\r\n        $(\"#swiperTagContainer\").append(TronscanTextContainer);\r\n\r\n        let swiper = document.createElement(\"div\");\r\n        swiper.className = \"swiper tronscanSwiper\";\r\n        $(\"#swiperTagContainer\").append(swiper);\r\n\r\n        let swiper_wrapper = document.createElement(\"div\");\r\n        swiper_wrapper.className = \"swiper-wrapper\";\r\n        swiper.appendChild(swiper_wrapper);\r\n\r\n        for (let i = 0; i < tronscanList.length; i++) {\r\n            let tronscanData = tronscanList[i];\r\n            let tronscanName = tronscanData.name();\r\n            let tronscanValueUsd = parseInt(tronscanData.valueUsd()) + \" USD\";\r\n            let tronscanBackground = \"url(\" + tronscanData.img() + \");\";\r\n\r\n            let swiper_slide = document.createElement(\"div\");\r\n            swiper_slide.className = \"swiper-slide swiper-slide-tag\";\r\n            swiper_slide.innerHTML = \"<div style='width:100px;height:130px;border-radius: 12px;background: \" + tronscanBackground + \";background-size: cover;background-position: center;background-repeat: no-repeat;word-break: break-all;'><div style='display:inline-block;position: absolute;top: 56px;height:20px;width:100px;background: rgba(255,255,255,0.5);'></div><div class='tronscanMask'><div style='display: flex;width: 90px;justify-content: center;font-weight: bold;color:#02F78E;font-size:10px;'><span>\" + tronscanValueUsd + \"</span></div></div></div>\";\r\n\r\n            swiper_wrapper.appendChild(swiper_slide);\r\n        }\r\n\r\n        new Swiper(\".tronscanSwiper\", {\r\n            loop: true,\r\n            spaceBetween: isMobileView ? 80 : 10,\r\n            slidesPerView: isMobileView ? 4 : 7,\r\n        });\r\n    }\r\n\r\n}\r\n\r\nfunction moveLeaderBoard() {\r\n    $(\".item-MoneyLeaderboard\").addClass(\"App-primaryControl\");\r\n    $(\".item-forum-checkin\").parent().append($(\".item-MoneyLeaderboard\"));\r\n    $(\".item-MoneyLeaderboard\").css(\"right\", \"75px\");\r\n}\r\n\r\nfunction addButtons() {\r\n    let selectTitleContainer = document.getElementById(\"selectTitleContainer\");\r\n\r\n    if (selectTitleContainer === null) {\r\n        selectTitleContainer = document.createElement(\"div\");\r\n        selectTitleContainer.id = \"selectTitleContainer\";\r\n        selectTitleContainer.className = \"selectTitleContainer\";\r\n\r\n        let buttonsCustomization = \"\";\r\n        let buttonsCustomizationMap = {};\r\n        let totalButtons = 3;\r\n        for (let i = 0; i < buttonsCustomizationList.length; i++) {\r\n            let buttonsCustomizationData = buttonsCustomizationList[i];\r\n            let buttonsCustomizationName = buttonsCustomizationData.name();\r\n            let buttonsCustomizationIcon = buttonsCustomizationData.icon();\r\n            let buttonsCustomizationColor = buttonsCustomizationData.color();\r\n            let buttonsCustomizationURL = buttonsCustomizationData.url();\r\n\r\n            totalButtons++;\r\n            buttonsCustomizationMap[totalButtons] = { url: buttonsCustomizationURL };\r\n            buttonsCustomization += '<button id=\"client1HeaderButton' + totalButtons + '\" number=\"' + totalButtons + '\" type=\"button\" class=\"u-btn\"><i class=\"' + buttonsCustomizationIcon + '\"></i><div class=\"u-btn-text\">' + buttonsCustomizationName + '</div></button>';\r\n        }\r\n\r\n        let selectTitle = document.createElement(\"div\");\r\n        selectTitle.className = \"selectTitle\";\r\n        selectTitle.innerHTML = '<div class=\"switch-btns\" style=\"max-width:' + $(\".TagsPage-content\").width() + 'px\"><div class=\"btns-container\"><button id=\"client1HeaderButton0\" type=\"button\" class=\"u-btn\" number=\"0\"><i class=\"fas fa-paw\"></i><div class=\"u-btn-text\">论坛</div></button><button id=\"client1HeaderButton1\" number=\"1\" type=\"button\" class=\"u-btn\"><i class=\"fab fa-twitch\"></i><div class=\"u-btn-text\">直播</div></button><button id=\"client1HeaderButton2\" number=\"2\" type=\"button\" class=\"u-btn\"><i class=\"fas fa-dice\"></i><div class=\"u-btn-text\">游戏</div></button><button id=\"client1HeaderButton3\" number=\"3\" type=\"button\" class=\"u-btn\"><i class=\"fas fa-gifts\"></i><div class=\"u-btn-text\">商城</div></button>' + buttonsCustomization + '<div id=\"buttonSelectedBackground\" class=\"selected-bg\" style=\"left: 0px; top: 0px; opacity: 1;\"></div></div></div>';\r\n        selectTitleContainer.appendChild(selectTitle);\r\n\r\n        $(\"#content .TagsPage-content\").prepend(selectTitleContainer);\r\n\r\n        const eventType = isMobileView ? \"touchend\" : \"click\";\r\n        let leftValuePrev = 0;\r\n        let leftValueMap = {};\r\n        let leftModifier = isMobileView ? 3 : 0;\r\n\r\n        leftValueMap[0] = 0;\r\n        for (let i = 0; i < totalButtons; i++) {\r\n            let leftValue = $(\"#client1HeaderButton\" + i).outerWidth();\r\n\r\n            if (i === 1 || i === 2) {\r\n                continue;\r\n            }\r\n\r\n            if (i === 0) {\r\n                $(\"#buttonSelectedBackground\").width(leftValue);\r\n            }\r\n\r\n            leftValueMap[i + 1] = leftValue + leftValuePrev - leftModifier;\r\n            leftValuePrev += leftValue;\r\n        }\r\n\r\n        $('.u-btn').on(eventType, function () {\r\n            const number = parseInt($(this).attr('number'));\r\n            let zhiboIframe = document.getElementById(\"zhiboIframe\");\r\n\r\n            $(\".App\").css(\"min-height\", \"100vh\");\r\n\r\n            if (number === 0) {\r\n                $(\".swiperTagContainer\").css(\"display\", \"\");\r\n                $(\".zhiboContainer\").css(\"display\", \"none\");\r\n                $(\".youxiContainer\").css(\"display\", \"none\");\r\n                $(\".buttonCustomizationContainer\").css(\"display\", \"none\");\r\n                $(\".shangchengContainer\").css(\"display\", \"none\");\r\n                $(\".App\").css(\"min-height\", \"50vh\");\r\n                zhiboIframe.src = \"\";\r\n            } else if (number === 1) {\r\n                $(\".swiperTagContainer\").css(\"display\", \"none\");\r\n                $(\".zhiboContainer\").css(\"display\", \"inline-block\");\r\n                $(\".youxiContainer\").css(\"display\", \"none\");\r\n                $(\".buttonCustomizationContainer\").css(\"display\", \"none\");\r\n                $(\".shangchengContainer\").css(\"display\", \"none\");\r\n\r\n                const iframeHeight = window.innerHeight - $(\"#app-navigation\").outerHeight() - $(\".selectTitleContainer\").outerHeight() - $(\"#linksQueuePrev\").outerHeight();\r\n                $(\"#zhiboIframe\").css(\"height\", iframeHeight + \"px\");\r\n\r\n                if (linksQueueList[linksQueuePointer]) {\r\n                    let linksQueueURL = linksQueueList[linksQueuePointer].attribute(\"links\");\r\n\r\n                    if (zhiboIframe.src !== linksQueueURL) {\r\n                        zhiboIframe.src = linksQueueURL;\r\n                    }\r\n                }\r\n            } else if (number === 2) {\r\n                $(\".swiperTagContainer\").css(\"display\", \"none\");\r\n                $(\".zhiboContainer\").css(\"display\", \"none\");\r\n                $(\".youxiContainer\").css(\"display\", \"flex\");\r\n                $(\".buttonCustomizationContainer\").css(\"display\", \"none\");\r\n                $(\".shangchengContainer\").css(\"display\", \"none\");\r\n                zhiboIframe.src = \"\";\r\n            } else if (number === 3) {\r\n                $(\".swiperTagContainer\").css(\"display\", \"none\");\r\n                $(\".zhiboContainer\").css(\"display\", \"none\");\r\n                $(\".youxiContainer\").css(\"display\", \"none\");\r\n                $(\".buttonCustomizationContainer\").css(\"display\", \"none\");\r\n                $(\".shangchengContainer\").css(\"display\", \"flex\");\r\n                zhiboIframe.src = \"\";\r\n            } else {\r\n                const customButtonData = buttonsCustomizationMap[number];\r\n\r\n                if (customButtonData) {\r\n                    let iframeHeight = window.innerHeight - $(\"#app-navigation\").outerHeight() - $(\".selectTitleContainer\").outerHeight() - $(\"#linksQueuePrev\").outerHeight();\r\n                    let paddingBottom = 0;\r\n                    let scrolling = \"yes\";\r\n                    let containerHeight = $(\".swiperTagContainer\").css(\"height\");\r\n\r\n                    if (number == 5) {\r\n                        iframeHeight = 550;\r\n                        containerHeight = 550;\r\n                    } else if (number == 6) {\r\n                        iframeHeight = containerHeight = 380;\r\n                        scrolling = \"no\";\r\n                    } else if (number == 7) {\r\n                        paddingBottom = 20;\r\n                    } else if (number == 8) {\r\n                        paddingBottom = 20;\r\n                    }\r\n\r\n                    $(\".buttonCustomizationContainer\").css(\"padding-bottom\", paddingBottom + \"px\");\r\n                    $(\"#customButtonIframe\").css(\"padding-bottom\", paddingBottom + \"px\");\r\n                    $('#customButtonIframe').attr(\"scrolling\", scrolling);\r\n\r\n                    $(\".buttonCustomizationContainer\").css(\"height\", containerHeight + \"px\");\r\n                    $(\"#customButtonIframe\").css(\"height\", iframeHeight + \"px\");\r\n\r\n                    let customButtonIframe = document.getElementById(\"customButtonIframe\");\r\n                    $(\".swiperTagContainer\").css(\"display\", \"none\");\r\n                    $(\".zhiboContainer\").css(\"display\", \"none\");\r\n                    $(\".youxiContainer\").css(\"display\", \"none\");\r\n                    $(\".buttonCustomizationContainer\").css(\"display\", \"inline-block\");\r\n                    $(\".shangchengContainer\").css(\"display\", \"none\");\r\n\r\n                    customButtonIframe.src = customButtonData.url;\r\n                }\r\n            }\r\n\r\n            $(\"#buttonSelectedBackground\").width($(this).outerWidth());\r\n\r\n            if (leftValueMap[number] !== undefined) {\r\n                $(\"#buttonSelectedBackground\").css(\"left\", leftValueMap[number]);\r\n            }\r\n        });\r\n\r\n    }\r\n}\r\n\r\nfunction addZhiBoContainer() {\r\n    const swiperTagContainerHeight = $(\".swiperTagContainer\").css(\"height\");\r\n\r\n    let zhiboContainer = document.createElement(\"div\");\r\n    zhiboContainer.className = \"zhiboContainer\";\r\n    zhiboContainer.style.height = $(\".swiperTagContainer\").css(\"height\");\r\n\r\n    const refreshButton = \"<div style='padding-top: 0px;padding-bottom: 0px;' class='selectTitle'><div class='switch-btns'><div class='btns-container'><button type='button' class='u-btn'><div id='refreshZhiBoButton' class='u-btn-text'>刷新直播间</div></button><div id='buttonSelectedBackground' class='selected-bg' style='left: 0px; top: 0px; opacity: 1;'></div></div></div></div>\";\r\n    const prevButton = \"<div style='padding-top: 0px;padding-bottom: 0px;' class='selectTitle'><div class='switch-btns'><div class='btns-container'><button type='button' class='u-btn'><div style='color:#666' id='prevZhiBoButton' class='u-btn-text'>上个直播间</div></button><div id='buttonSelectedBackground' class='selected-bg' style='left: 0px; top: 0px; opacity: 1;'></div></div></div></div>\";\r\n    const nextButton = \"<div style='padding-top: 0px;padding-bottom: 0px;' class='selectTitle'><div class='switch-btns'><div class='btns-container'><button type='button' class='u-btn'><div id='nextZhiBoButton' class='u-btn-text'>切换直播间</div></button><div id='buttonSelectedBackground' class='selected-bg' style='left: 0px; top: 0px; opacity: 1;'></div></div></div></div>\";\r\n    zhiboContainer.innerHTML = \"<div id='linksQueueRefresh' style='z-index: 1000;display:inline-block;scale:0.8;position: fixed;bottom: \" + (isMobileView ? 0 : -6) + \"px;'>\" + refreshButton + \"</div><div class='zhiboSubContainer'><div id='linksQueuePrev' style='display:inline-block;scale:0.8'>\" + prevButton + \"</div><div id='linksQueueNext' style='display:inline-block;scale:0.8'>\" + nextButton + \"</div></div><iframe id='zhiboIframe' name='contentOnly' class='zhiboIframe' src=''></iframe>\";\r\n\r\n    $(\"#content .TagsPage-content\").prepend(zhiboContainer);\r\n\r\n    const eventType = isMobileView ? \"touchend\" : \"click\";\r\n    let zhiboIframe = document.getElementById(\"zhiboIframe\");\r\n\r\n    $('#linksQueueRefresh').on(eventType, function () {\r\n        zhiboIframe.src = \"\";\r\n\r\n        setTimeout(function () {\r\n            let linksQueueURL = linksQueueList[linksQueuePointer].attribute(\"links\");\r\n            zhiboIframe.src = linksQueueURL;\r\n        }, 100)\r\n    });\r\n\r\n    $('#linksQueuePrev').on(eventType, function () {\r\n\r\n        linksQueuePointer--;\r\n\r\n        if (linksQueueList[linksQueuePointer] !== undefined) {\r\n            let linksQueueURL = linksQueueList[linksQueuePointer].attribute(\"links\");\r\n            zhiboIframe.src = linksQueueURL;\r\n        }\r\n\r\n        $(\"#nextZhiBoButton\").css(\"color\", \"\");\r\n        if (linksQueueList[linksQueuePointer - 1] === undefined) {\r\n            $(\"#prevZhiBoButton\").css(\"color\", \"#666\");\r\n        } else {\r\n            $(\"#prevZhiBoButton\").css(\"color\", \"\");\r\n        }\r\n    });\r\n\r\n    $('#linksQueueNext').on(eventType, function () {\r\n        linksQueuePointer++;\r\n\r\n        if (linksQueueList[linksQueuePointer] !== undefined) {\r\n            let linksQueueURL = linksQueueList[linksQueuePointer].attribute(\"links\");\r\n            zhiboIframe.src = linksQueueURL;\r\n        }\r\n\r\n        $(\"#prevZhiBoButton\").css(\"color\", \"\");\r\n        if (linksQueueList[linksQueuePointer + 1] === undefined) {\r\n            $(\"#nextZhiBoButton\").css(\"color\", \"#666\");\r\n        } else {\r\n            $(\"#nextZhiBoButton\").css(\"color\", \"\");\r\n        }\r\n    });\r\n}\r\n\r\nfunction addButtonCustomizationContainer() {\r\n    const swiperTagContainerHeight = $(\".swiperTagContainer\").css(\"height\");\r\n\r\n    let buttonCustomizationContainer = document.createElement(\"div\");\r\n    buttonCustomizationContainer.className = \"buttonCustomizationContainer\";\r\n    buttonCustomizationContainer.style.height = $(\".swiperTagContainer\").css(\"height\");\r\n    buttonCustomizationContainer.innerHTML = \"<iframe id='customButtonIframe' name='contentOnly' class='customButtonIframe' src=''></iframe>\"\r\n\r\n    $(\"#content .TagsPage-content\").prepend(buttonCustomizationContainer);\r\n}\r\n\r\nfunction addYouXiContainer() {\r\n    const swiperTagContainerHeight = $(\".swiperTagContainer\").css(\"height\");\r\n\r\n    let youxiContainer = document.createElement(\"div\");\r\n    youxiContainer.className = \"youxiContainer\";\r\n    youxiContainer.style.height = $(\".swiperTagContainer\").css(\"height\");\r\n    youxiContainer.innerText = app.translator.trans(\"wusong8899-client1.forum.under-construction\");\r\n\r\n    $(\"#content .TagsPage-content\").prepend(youxiContainer);\r\n}\r\n\r\nfunction addShangChengContainer() {\r\n    const swiperTagContainerHeight = $(\".swiperTagContainer\").css(\"height\");\r\n\r\n    let shangchengContainer = document.createElement(\"div\");\r\n    shangchengContainer.className = \"shangchengContainer\";\r\n    shangchengContainer.style.height = $(\".swiperTagContainer\").css(\"height\");\r\n    shangchengContainer.innerText = app.translator.trans(\"wusong8899-client1.forum.under-construction\");\r\n\r\n    $(\"#content .TagsPage-content\").prepend(shangchengContainer);\r\n}\r\n\r\nfunction addHeaderIcon() {\r\n    let headerIconContainer = document.getElementById(\"wusong8899Client1HeaderIcon\");\r\n\r\n    if (headerIconContainer === null) {\r\n        headerIconContainer = document.createElement(\"div\");\r\n        headerIconContainer.id = \"wusong8899Client1HeaderIcon\";\r\n        headerIconContainer.style.display = 'inline-block';\r\n        headerIconContainer.style.marginTop = '8px';\r\n        headerIconContainer.innerHTML = '<img src=\"https://lg666.cc/assets/files/2023-01-18/1674049401-881154-test-16.png\" style=\"height: 24px;\" />';\r\n\r\n        $(\"#app-navigation\").find(\".App-backControl\").prepend(headerIconContainer);\r\n    }\r\n}\r\n\r\nfunction attachAdvertiseHeader(vdom: Vnode<any>): void {\r\n    if (isMobileView) {\r\n        $(\".item-newDiscussion\").find(\"span.Button-label\").html(\"<div class='buttonRegister'>登录</div>\");\r\n        $(\".item-newDiscussion\").find(\"span.Button-label\").css(\"display\", \"block\");\r\n        $(\".item-newDiscussion\").find(\"span.Button-label\").css(\"font-size\", \"14px\");\r\n        $(\".item-newDiscussion\").find(\"span.Button-label\").css(\"word-spacing\", \"-1px\");\r\n    }\r\n\r\n    $(\".item-newDiscussion\").find(\"i\").css(\"display\", \"none\");\r\n    $(\".item-nav\").remove();\r\n    $(\".TagTiles\").css(\"display\", \"none\");\r\n\r\n    let task = setInterval(function () {\r\n        if (vdom.dom) {\r\n            clearInterval(task);\r\n\r\n            if (vdom.dom !== undefined) {\r\n                let TransitionTime = app.forum.attribute('Client1HeaderAdvTransitionTime');\r\n\r\n                if (!TransitionTime) {\r\n                    TransitionTime = 5000;\r\n                }\r\n\r\n                let screenWidth = $(window).width();\r\n                let styleWidth = screenWidth * 2 - 50;\r\n\r\n                let swiperContainer = document.getElementById(\"swiperAdContainer\");\r\n\r\n                if (swiperContainer !== null) {\r\n                    return;\r\n                }\r\n\r\n                swiperContainer = document.createElement(\"div\");\r\n                swiperContainer.className = \"swiperAdContainer\";\r\n                swiperContainer.id = \"swiperAdContainer\";\r\n\r\n                if (isMobileView === true) {\r\n                    swiperContainer.style.width = styleWidth + \"px\";\r\n                    swiperContainer.style.marginLeft = -(styleWidth * 0.254) + \"px\";\r\n                }\r\n\r\n                let swiper = document.createElement(\"div\");\r\n                swiper.className = \"swiper adSwiper\";\r\n                swiperContainer.appendChild(swiper);\r\n\r\n                let swiper_wrapper = document.createElement(\"div\");\r\n                swiper_wrapper.className = \"swiper-wrapper\";\r\n                swiper.appendChild(swiper_wrapper);\r\n\r\n                for (let i = 1; i <= 30; i++) {\r\n                    let swiper_slide = document.createElement(\"div\");\r\n                    let imageSrc = app.forum.attribute('Client1HeaderAdvImage' + i);\r\n                    let imageLink = app.forum.attribute('Client1HeaderAdvLink' + i);\r\n\r\n                    if (imageSrc) {\r\n                        swiper_slide.className = \"swiper-slide\";\r\n                        swiper_slide.innerHTML = \"<img onclick='window.location.href=\\\"\" + imageLink + \"\\\"' src='\" + imageSrc + \"' />\";\r\n                        swiper_wrapper.appendChild(swiper_slide);\r\n                    }\r\n                }\r\n\r\n                let swiper_button_next = document.createElement(\"div\");\r\n                swiper_button_next.className = \"swiper-button-next\";\r\n                swiper.appendChild(swiper_button_next);\r\n\r\n                let swiper_button_prev = document.createElement(\"div\");\r\n                swiper_button_prev.className = \"swiper-button-prev\";\r\n                swiper.appendChild(swiper_button_prev);\r\n\r\n                let swiper_pagination = document.createElement(\"div\");\r\n                swiper_pagination.className = \"swiper-pagination\";\r\n                swiper.appendChild(swiper_pagination);\r\n\r\n                $(\"#content .container\").prepend(swiperContainer);\r\n\r\n                new Swiper(\".adSwiper\", {\r\n                    autoplay: {\r\n                        delay: TransitionTime,\r\n                    },\r\n                    loop: true,\r\n                    spaceBetween: 30,\r\n                    effect: \"coverflow\",\r\n                    centeredSlides: true,\r\n                    slidesPerView: 2,\r\n                    coverflowEffect: {\r\n                        rotate: 0,\r\n                        depth: 100,\r\n                        modifier: 1,\r\n                        slideShadows: true,\r\n                        stretch: 0\r\n                    },\r\n                    pagination: {\r\n                        el: '.swiper-pagination',\r\n                        type: 'bullets',\r\n                    },\r\n                    navigation: {\r\n                        nextEl: '.swiper-button-next',\r\n                        prevEl: '.swiper-button-prev',\r\n                    },\r\n                    modules: [EffectCoverflow, Navigation, Pagination, Autoplay]\r\n                });\r\n\r\n                loadLinksQueueList();\r\n                loadTronscanList();\r\n                loadButtonsCustomizationList();\r\n\r\n                let checkDataTask = setInterval(function () {\r\n                    if (tronscanList !== null && linksQueueList !== null && buttonsCustomizationList !== null) {\r\n                        clearInterval(checkDataTask);\r\n\r\n                        if ($(\"#swiperTagContainer\").length === 0) {\r\n                            changeCategoryLayout();\r\n                            addZhiBoContainer();\r\n                            addYouXiContainer();\r\n                            addButtonCustomizationContainer();\r\n                            addShangChengContainer();\r\n                            addButtons();\r\n                            moveLeaderBoard();\r\n                        }\r\n\r\n                        if (!app.session.user) {\r\n                            addHeaderIcon();\r\n                        }\r\n                    }\r\n                }, 100);\r\n            }\r\n        }\r\n    }, checkTime);\r\n}\r\n\r\n\r\n\r\n\r\n\r\n"], "names": ["__webpack_require__", "module", "getter", "__esModule", "d", "a", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "flarum", "core", "compat", "isObject", "constructor", "extend", "target", "src", "noExtend", "keys", "filter", "indexOf", "for<PERSON>ach", "length", "ssrDocument", "body", "addEventListener", "removeEventListener", "activeElement", "blur", "nodeName", "querySelector", "querySelectorAll", "getElementById", "createEvent", "initEvent", "createElement", "children", "childNodes", "style", "setAttribute", "getElementsByTagName", "createElementNS", "importNode", "location", "hash", "host", "hostname", "href", "origin", "pathname", "protocol", "search", "doc", "document", "ssrWindow", "navigator", "userAgent", "history", "replaceState", "pushState", "go", "back", "CustomEvent", "this", "getComputedStyle", "getPropertyValue", "Image", "Date", "screen", "setTimeout", "clearTimeout", "matchMedia", "requestAnimationFrame", "callback", "cancelAnimationFrame", "id", "win", "window", "nextTick", "delay", "now", "toString", "slice", "isNode", "node", "HTMLElement", "nodeType", "to", "arguments", "undefined", "i", "nextSource", "keysArray", "nextIndex", "len", "<PERSON><PERSON><PERSON>", "desc", "getOwnPropertyDescriptor", "__swiper__", "setCSSProperty", "el", "varName", "varValue", "setProperty", "animateCSSModeScroll", "_ref", "swiper", "targetPosition", "side", "startPosition", "translate", "time", "startTime", "duration", "params", "speed", "wrapperEl", "scrollSnapType", "cssModeFrameID", "dir", "isOutOfBound", "current", "animate", "getTime", "progress", "Math", "max", "min", "easeProgress", "cos", "PI", "currentPosition", "scrollTo", "overflow", "elementChildren", "element", "selector", "HTMLSlotElement", "push", "assignedElements", "matches", "showWarning", "text", "console", "warn", "err", "tag", "classes", "classList", "add", "Array", "isArray", "trim", "split", "c", "classesToTokens", "elementStyle", "elementIndex", "child", "previousSibling", "elementOuterSize", "size", "<PERSON><PERSON><PERSON><PERSON>", "parseFloat", "offsetWidth", "support", "deviceCached", "browser", "getSupport", "smoothScroll", "documentElement", "touch", "DocumentTouch", "calcSupport", "getDevice", "overrides", "_temp", "platform", "ua", "device", "ios", "android", "screenWidth", "width", "screenHeight", "height", "match", "ipad", "ipod", "iphone", "windows", "macos", "os", "calcDevice", "<PERSON><PERSON><PERSON><PERSON>", "needPerspectiveFix", "<PERSON><PERSON><PERSON><PERSON>", "toLowerCase", "String", "includes", "major", "minor", "map", "num", "Number", "isWebView", "test", "isSafariB<PERSON><PERSON>", "need3dFix", "calcB<PERSON>er", "eventsEmitter", "on", "events", "handler", "priority", "self", "eventsListeners", "destroyed", "method", "event", "once", "once<PERSON><PERSON><PERSON>", "off", "__emitterProxy", "_len", "args", "_key", "apply", "onAny", "eventsAnyListeners", "offAny", "index", "splice", "<PERSON><PERSON><PERSON><PERSON>", "emit", "data", "context", "_len2", "_key2", "unshift", "toggleSlideClasses$1", "slideEl", "condition", "className", "contains", "remove", "toggleSlideClasses", "processLazyPreloader", "imageEl", "closest", "isElement", "slideClass", "lazyEl", "lazyPreloaderClass", "shadowRoot", "unlazy", "slides", "removeAttribute", "preload", "amount", "lazyPreloadPrevNext", "<PERSON><PERSON><PERSON><PERSON>iew", "slidesPerViewDynamic", "ceil", "activeIndex", "grid", "rows", "activeColumn", "preloadColumns", "from", "_", "column", "slideIndexLastInView", "rewind", "loop", "realIndex", "update", "updateSize", "clientWidth", "clientHeight", "isHorizontal", "isVertical", "parseInt", "isNaN", "assign", "updateSlides", "getDirectionPropertyValue", "label", "getDirectionLabel", "slidesEl", "swiperSize", "rtlTranslate", "rtl", "wrongRTL", "isVirtual", "virtual", "enabled", "previousSlidesLength", "<PERSON><PERSON><PERSON><PERSON>", "snapGrid", "slidesGrid", "slidesSizesGrid", "offsetBefore", "slidesOffsetBefore", "offsetAfter", "slidesOffsetAfter", "previousSnapGridLength", "previousSlidesGridLength", "spaceBetween", "slidePosition", "prevSlideSize", "replace", "virtualSize", "marginLeft", "marginRight", "marginBottom", "marginTop", "centeredSlides", "cssMode", "gridEnabled", "slideSize", "initSlides", "unsetSlides", "shouldResetSlideSize", "breakpoints", "slide", "updateSlide", "slideStyles", "currentTransform", "transform", "currentWebKitTransform", "webkitTransform", "roundLengths", "paddingLeft", "paddingRight", "boxSizing", "floor", "swiperSlideSize", "abs", "slidesPerGroup", "slidesPerGroupSkip", "effect", "setWrapperSize", "updateWrapperSize", "newSlidesGrid", "slidesGridItem", "groups", "slidesBefore", "slidesAfter", "groupSize", "slideIndex", "centeredSlidesBounds", "allSlidesSize", "slideSizeValue", "maxSnap", "snap", "centerInsufficientSlides", "offsetSize", "allSlidesOffset", "snapIndex", "addToSnapGrid", "addToSlidesGrid", "v", "watchOverflow", "checkOverflow", "watchSlidesProgress", "updateSlidesOffset", "backFaceHiddenClass", "containerModifierClass", "hasClassBackfaceClassAdded", "maxBackfaceHiddenSlides", "updateAutoHeight", "activeSlides", "newHeight", "setTransition", "getSlideByIndex", "getSlideIndexByData", "visibleSlides", "offsetHeight", "minusOffset", "offsetLeft", "offsetTop", "swiperSlideOffset", "cssOverflowAdjustment", "updateSlidesProgress", "offsetCenter", "visibleSlidesIndexes", "slideOffset", "slideProgress", "minTranslate", "originalSlideProgress", "slideBefore", "slideAfter", "isFullyVisible", "isVisible", "slideVisibleClass", "slideFullyVisibleClass", "originalProgress", "updateProgress", "multiplier", "translatesDiff", "maxTranslate", "isBeginning", "isEnd", "progressLoop", "wasBeginning", "wasEnd", "isBeginningRounded", "isEndRounded", "firstSlideIndex", "lastSlideIndex", "firstSlideTranslate", "lastSlideTranslate", "translateMax", "translateAbs", "autoHeight", "updateSlidesClasses", "getFilteredSlide", "activeSlide", "prevSlide", "nextSlide", "find", "nextEls", "nextElement<PERSON><PERSON>ling", "next", "elementNextAll", "prevEls", "previousElementSibling", "prev", "elementPrevAll", "slideActiveClass", "slideNextClass", "slidePrevClass", "emitSlidesClasses", "updateActiveIndex", "newActiveIndex", "previousIndex", "previousRealIndex", "previousSnapIndex", "getVirtualRealIndex", "aIndex", "normalizeSlideIndex", "getActiveIndexByTranslate", "skip", "firstSlideInColumn", "activeSlideIndex", "getAttribute", "initialized", "runCallbacksOnInit", "updateClickedSlide", "path", "pathEl", "slideFound", "clickedSlide", "clickedIndex", "slideToClickedSlide", "getTranslate", "axis", "virtualTranslate", "currentTranslate", "matrix", "curTransform", "transformMatrix", "curStyle", "currentStyle", "WebKitCSSMatrix", "join", "MozTransform", "OTransform", "MsTransform", "msTransform", "m41", "m42", "setTranslate", "byController", "newProgress", "x", "y", "previousTranslate", "translateTo", "runCallbacks", "translateBounds", "internal", "animating", "preventInteractionOnTransition", "newTranslate", "isH", "behavior", "onTranslateToWrapperTransitionEnd", "e", "transitionEmit", "direction", "step", "slideTo", "initial", "normalizedTranslate", "normalizedGrid", "normalizedGridNext", "allowSlideNext", "allowSlidePrev", "transitionStart", "transitionEnd", "t", "_immediateVirtual", "_cssModeVirtualInitialSet", "initialSlide", "onSlideToWrapperTransitionEnd", "slideToLoop", "newIndex", "targetSlideIndex", "cols", "needLoopFix", "loopFix", "slideRealIndex", "slideNext", "perGroup", "slidesPerGroupAuto", "increment", "loopPreventsSliding", "_clientLeft", "clientLeft", "slidePrev", "normalize", "val", "normalizedSnapGrid", "isFreeMode", "freeMode", "prevSnap", "prevSnapIndex", "prevIndex", "lastIndex", "slideReset", "slideToClosest", "threshold", "currentSnap", "slideToIndex", "getSlideIndexWhenGrid", "slideSelector", "isGrid", "getSlideIndex", "loopCreate", "loopAddBlankSlides", "slideBlankClass", "recalcSlides", "clearBlankSlides", "shouldFillGroup", "shouldFillGrid", "addBlankSlides", "amountOfSlides", "append", "byMousewheel", "loopedSlides", "loopAdditionalSlides", "fill", "prependSlidesIndexes", "appendSlidesIndexes", "isInitialOverflow", "isNext", "isPrev", "slidesPrepended", "slidesAppended", "activeColIndexWithShift", "colIndexToPrepend", "__preventObserver__", "swiperLoopMoveDOM", "prepend", "currentSlideTranslate", "diff", "touchEventsData", "startTranslate", "shift", "controller", "control", "loopParams", "loop<PERSON><PERSON><PERSON>", "newSlidesOrder", "swiperSlideIndex", "preventEdgeSwipe", "startX", "edgeSwipeDetection", "edgeSwipeThreshold", "innerWidth", "preventDefault", "onTouchStart", "originalEvent", "type", "pointerId", "targetTouches", "touchId", "identifier", "pageX", "touches", "simulate<PERSON>ouch", "pointerType", "targetEl", "touchEventsTarget", "parent", "<PERSON><PERSON><PERSON><PERSON>", "slot", "elementsQueue", "elementToCheck", "elementIsChildOfSlot", "elementIsChildOf", "which", "button", "isTouched", "isMoved", "swipingClassHasValue", "noSwipingClass", "eventPath", "<PERSON><PERSON><PERSON>", "noSwipingSelector", "isTargetShadow", "noSwiping", "base", "__closestFrom", "assignedSlot", "found", "getRootNode", "closestElement", "allowClick", "swi<PERSON><PERSON><PERSON><PERSON>", "currentX", "currentY", "pageY", "startY", "allowTouchCallbacks", "isScrolling", "startMoving", "touchStartTime", "swipeDirection", "allowThresholdMove", "focusableElements", "shouldPreventDefault", "allowTouchMove", "touchStartPreventDefault", "touchStartForcePreventDefault", "isContentEditable", "onTouchMove", "targetTouch", "changedTouches", "preventedByNestedSwiper", "touchReleaseOnEdges", "previousX", "previousY", "diffX", "diffY", "sqrt", "touchAngle", "atan2", "preventTouchMoveFromPointerMove", "cancelable", "touchMoveStopPropagation", "nested", "stopPropagation", "touchesDiff", "oneWayMovement", "touchRatio", "prevTouchesDirection", "touchesDirection", "isLoop", "allowLoopFix", "evt", "bubbles", "detail", "bySwiperTouchMove", "dispatchEvent", "allowMomentumBounce", "grabCursor", "setGrabCursor", "_loopSwapReset", "loopSwapReset", "disableParentSwiper", "resistanceRatio", "resistance", "follow<PERSON><PERSON>", "onTouchEnd", "touchEndTime", "timeDiff", "pathTree", "lastClickTime", "currentPos", "swipeToLast", "stopIndex", "rewindFirstIndex", "rewindLastIndex", "ratio", "longSwipesMs", "longSwipes", "longSwipesRatio", "shortSwipes", "navigation", "nextEl", "prevEl", "onResize", "setBreakpoint", "isVirtualLoop", "autoplay", "running", "paused", "resizeTimeout", "resume", "onClick", "preventClicks", "preventClicksPropagation", "stopImmediatePropagation", "onScroll", "scrollLeft", "scrollTop", "onLoad", "onDocumentTouchStart", "documentTouchHandlerProceeded", "touchAction", "capture", "dom<PERSON>ethod", "swiperMethod", "passive", "updateOnWindowResize", "isGridEnabled", "breakpointsBase", "breakpoint<PERSON><PERSON><PERSON>", "breakpoint", "getBreakpoint", "currentBreakpoint", "breakpointP<PERSON>ms", "originalParams", "wasMultiRow", "isMultiRow", "wasGrabCursor", "isGrabCursor", "wasEnabled", "emitContainerClasses", "unsetGrabCursor", "wasModuleEnabled", "isModuleEnabled", "disable", "enable", "directionChanged", "needsReLoop", "<PERSON><PERSON><PERSON>", "changeDirection", "isEnabled", "<PERSON><PERSON><PERSON>", "containerEl", "currentHeight", "innerHeight", "points", "point", "minRatio", "substr", "value", "sort", "b", "defaults", "init", "swiperElementNodeName", "resizeObserver", "createElements", "eventsPrefix", "url", "uniqueNavElements", "passiveListeners", "wrapperClass", "_emitClasses", "moduleExtendParams", "allModulesParams", "moduleParamName", "moduleParams", "auto", "prototypes", "transition", "transitionDuration", "transitionDelay", "moving", "isLocked", "cursor", "attachEvents", "bind", "detachEvents", "wasLocked", "lastSlideRightEdge", "addClasses", "classNames", "suffixes", "entries", "prefix", "resultClasses", "item", "prepareClasses", "removeClasses", "extendedDefaults", "Swiper", "swipers", "newParams", "modules", "__modules__", "mod", "extendParams", "swiperParams", "passedParams", "eventName", "velocity", "trunc", "clickTimeout", "velocities", "imagesToLoad", "imagesLoaded", "property", "setProgress", "cls", "getSlideClasses", "updates", "view", "exact", "spv", "breakLoop", "translateValue", "translated", "complete", "newDirection", "needUpdate", "currentDirection", "changeLanguageDirection", "mount", "mounted", "parentNode", "toUpperCase", "getWrapperSelector", "slideSlots", "hostEl", "lazyElements", "destroy", "deleteInstance", "cleanStyles", "object", "deleteProps", "extendDefaults", "newDefaults", "installModule", "use", "m", "prototypeGroup", "protoMethod", "observer", "animationFrame", "resize<PERSON><PERSON>ler", "orientationChangeHandler", "ResizeObserver", "newWidth", "_ref2", "contentBoxSize", "contentRect", "inlineSize", "blockSize", "observe", "unobserve", "observers", "attach", "options", "MutationObserver", "WebkitMutationObserver", "mutations", "observerUpdate", "attributes", "childList", "characterData", "observeParents", "observeSlideChildren", "containerParents", "parents", "parentElement", "elementParents", "disconnect", "check", "tronscanListLoading", "tronscanList", "linksQueueListLoading", "linksQueueList", "links<PERSON><PERSON>uePointer", "buttonsCustomizationListLoading", "buttonsCustomizationList", "isMobile<PERSON>iew", "vendor", "opera", "parseTronscanResults", "results", "parseButtonsCustomizationResults", "parseLinksQueueResults", "app", "HeaderPrimary", "vnode", "routeName", "vdom", "$", "html", "css", "task", "setInterval", "dom", "clearInterval", "TransitionTime", "attribute", "styleWidth", "swiper<PERSON><PERSON><PERSON>", "append<PERSON><PERSON><PERSON>", "swiper_wrapper", "swiper_slide", "imageSrc", "imageLink", "innerHTML", "swiper_button_next", "swiper_button_prev", "swiper_pagination", "coverflowEffect", "rotate", "depth", "modifier", "slideShadows", "stretch", "pagination", "EffectCoverflow", "Navigation", "Pagination", "Autoplay", "then", "loadLinksQueueList", "loadTronscanList", "loadButtonsCustomizationList", "checkDataTask", "headerIconContainer", "tagTile", "TagTextOuterContainer", "tagURL", "attr", "tagBackground", "tagName", "tagNameColor", "disableOnInteraction", "TronscanTextContainer", "tronscanData", "tronscanValueUsd", "name", "valueUsd", "tronscanBackground", "img", "addTronscan", "changeCategoryLayout", "zhiboContainer", "eventType", "zhiboIframe", "linksQueueURL", "addZhiBoContainer", "youxiC<PERSON><PERSON>", "innerText", "trans", "addYouXiContainer", "buttonCustomizationContainer", "addButtonCustomizationContainer", "shangchengContainer", "addShangChengContainer", "selectTitleContainer", "buttonsCustomization", "buttonsCustomizationMap", "totalButtons", "buttonsCustomizationData", "buttonsCustomizationName", "buttonsCustomizationIcon", "icon", "buttonsCustomizationURL", "color", "selectTitle", "leftValuePrev", "leftValueMap", "leftModifier", "leftValue", "outerWidth", "number", "iframeHeight", "outerHeight", "customButtonData", "paddingBottom", "scrolling", "containerHeight", "customButtonIframe", "addButtons", "addClass", "user", "display", "attachAdvertiseHeader"], "sourceRoot": ""}