{"version": 3, "file": "admin.js", "mappings": "MACA,IAAIA,EAAsB,CCA1BA,EAAyBC,IACxB,IAAIC,EAASD,GAAUA,EAAOE,WAC7B,IAAOF,EAAiB,QACxB,IAAM,EAEP,OADAD,EAAoBI,EAAEF,EAAQ,CAAEG,EAAGH,IAC5BA,GCLRF,EAAwB,CAACM,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXP,EAAoBS,EAAEF,EAAYC,KAASR,EAAoBS,EAAEH,EAASE,IAC5EE,OAAOC,eAAeL,EAASE,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,MCJ3ER,EAAwB,CAACc,EAAKC,IAAUL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,I,mBCAlF,MAAM,EAA+BI,OAAOC,KAAKC,OAAO,a,aCExDC,IAAAA,aAAiBC,IAAI,gCAAiC,WAClDD,IAAAA,cAAiB,IACV,iCACJE,gBAAgB,CACfC,QAAS,+CACTC,KAAM,SACNC,MAAOL,IAAAA,WAAeM,MAAM,6CAE7BJ,gBAAgB,CACfC,QAAS,sCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,oCAE7BJ,gBAAgB,CACfC,QAAS,uCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,qCAE7BJ,gBAAgB,CACfC,QAAS,sCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,oCAE7BJ,gBAAgB,CACfC,QAAS,uCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,qCAE7BJ,gBAAgB,CACfC,QAAS,sCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,oCAE7BJ,gBAAgB,CACfC,QAAS,uCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,qCAE7BJ,gBAAgB,CACfC,QAAS,sCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,oCAE7BJ,gBAAgB,CACfC,QAAS,uCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,qCAE7BJ,gBAAgB,CACfC,QAAS,sCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,oCAE7BJ,gBAAgB,CACfC,QAAS,uCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,qCAE7BJ,gBAAgB,CACfC,QAAS,sCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,oCAE7BJ,gBAAgB,CACfC,QAAS,uCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,qCAE7BJ,gBAAgB,CACfC,QAAS,sCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,oCAE7BJ,gBAAgB,CACfC,QAAS,uCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,qCAE7BJ,gBAAgB,CACfC,QAAS,sCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,oCAE7BJ,gBAAgB,CACfC,QAAS,uCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,qCAE7BJ,gBAAgB,CACfC,QAAS,sCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,oCAE7BJ,gBAAgB,CACfC,QAAS,uCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,qCAE7BJ,gBAAgB,CACfC,QAAS,uCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,qCAE7BJ,gBAAgB,CACfC,QAAS,wCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,sCAE7BJ,gBAAgB,CACfC,QAAS,uCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,qCAE7BJ,gBAAgB,CACfC,QAAS,wCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,sCAE7BJ,gBAAgB,CACfC,QAAS,uCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,qCAE7BJ,gBAAgB,CACfC,QAAS,wCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,sCAE7BJ,gBAAgB,CACfC,QAAS,uCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,qCAE7BJ,gBAAgB,CACfC,QAAS,wCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,sCAE7BJ,gBAAgB,CACfC,QAAS,uCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,qCAE7BJ,gBAAgB,CACfC,QAAS,wCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,sCAE7BJ,gBAAgB,CACfC,QAAS,uCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,qCAE7BJ,gBAAgB,CACfC,QAAS,wCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,sCAE7BJ,gBAAgB,CACfC,QAAS,uCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,qCAE7BJ,gBAAgB,CACfC,QAAS,wCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,sCAE7BJ,gBAAgB,CACfC,QAAS,uCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,qCAE7BJ,gBAAgB,CACfC,QAAS,wCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,sCAE7BJ,gBAAgB,CACfC,QAAS,uCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,qCAE7BJ,gBAAgB,CACfC,QAAS,wCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,sCAE7BJ,gBAAgB,CACfC,QAAS,uCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,qCAE7BJ,gBAAgB,CACfC,QAAS,wCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,sCAE7BJ,gBAAgB,CACfC,QAAS,uCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,qCAE7BJ,gBAAgB,CACfC,QAAS,wCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,sCAE7BJ,gBAAgB,CACfC,QAAS,uCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,qCAE7BJ,gBAAgB,CACfC,QAAS,wCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,sCAE7BJ,gBAAgB,CACfC,QAAS,uCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,qCAE7BJ,gBAAgB,CACfC,QAAS,wCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,sCAE7BJ,gBAAgB,CACfC,QAAS,uCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,qCAE7BJ,gBAAgB,CACfC,QAAS,wCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,sCAE7BJ,gBAAgB,CACfC,QAAS,uCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,qCAE7BJ,gBAAgB,CACfC,QAAS,wCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,sCAE7BJ,gBAAgB,CACfC,QAAS,uCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,qCAE7BJ,gBAAgB,CACfC,QAAS,wCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,sCAE7BJ,gBAAgB,CACfC,QAAS,uCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,qCAE7BJ,gBAAgB,CACfC,QAAS,wCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,sCAE7BJ,gBAAgB,CACfC,QAAS,uCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,qCAE7BJ,gBAAgB,CACfC,QAAS,wCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,sCAE7BJ,gBAAgB,CACfC,QAAS,uCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,qCAE7BJ,gBAAgB,CACfC,QAAS,wCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,sCAE7BJ,gBAAgB,CACfC,QAAS,uCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,qCAE7BJ,gBAAgB,CACfC,QAAS,wCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,sCAE7BJ,gBAAgB,CACfC,QAAS,uCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,qCAE7BJ,gBAAgB,CACfC,QAAS,wCACTC,KAAM,MACNC,MAAOL,IAAAA,WAAeM,MAAM,qCAEpC,E", "sources": ["webpack://@justoverclock/header-slideshow/webpack/bootstrap", "webpack://@justoverclock/header-slideshow/webpack/runtime/compat get default export", "webpack://@justoverclock/header-slideshow/webpack/runtime/define property getters", "webpack://@justoverclock/header-slideshow/webpack/runtime/hasOwnProperty shorthand", "webpack://@justoverclock/header-slideshow/external root \"flarum.core.compat['admin/app']\"", "webpack://@justoverclock/header-slideshow/./src/admin/index.js"], "sourcesContent": ["// The require scope\nvar __webpack_require__ = {};\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "const __WEBPACK_NAMESPACE_OBJECT__ = flarum.core.compat['admin/app'];", "import app from 'flarum/admin/app';\r\n\r\napp.initializers.add('wusong8899/client1-header-adv', () => {\r\n    app.extensionData\r\n      .for('wusong8899-client1-header-adv')\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.TransitionTime',\r\n        type: 'number',\r\n        label: app.translator.trans('wusong8899-client1.admin.TransitionTime'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Link1',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Link1'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Image1',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Image1'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Link2',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Link2'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Image2',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Image2'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Link3',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Link3'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Image3',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Image3'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Link4',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Link4'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Image4',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Image4'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Link5',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Link5'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Image5',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Image5'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Link6',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Link6'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Image6',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Image6'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Link7',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Link7'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Image7',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Image7'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Link8',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Link8'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Image8',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Image8'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Link9',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Link9'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Image9',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Image9'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Link10',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Link10'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Image10',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Image10'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Link11',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Link11'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Image11',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Image11'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Link12',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Link12'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Image12',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Image12'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Link13',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Link13'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Image13',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Image13'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Link14',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Link14'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Image14',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Image14'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Link15',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Link15'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Image15',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Image15'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Link16',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Link16'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Image16',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Image16'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Link17',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Link17'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Image17',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Image17'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Link18',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Link18'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Image18',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Image18'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Link19',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Link19'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Image19',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Image19'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Link20',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Link20'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Image20',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Image20'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Link21',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Link21'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Image21',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Image21'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Link22',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Link22'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Image22',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Image22'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Link23',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Link23'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Image23',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Image23'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Link24',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Link24'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Image24',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Image24'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Link25',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Link25'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Image25',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Image25'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Link26',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Link26'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Image26',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Image26'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Link27',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Link27'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Image27',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Image27'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Link28',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Link28'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Image28',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Image28'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Link29',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Link29'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Image29',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Image29'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Link30',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Link30'),\r\n      })\r\n      .registerSetting({\r\n        setting: 'wusong8899-client1-header-adv.Image30',\r\n        type: 'URL',\r\n        label: app.translator.trans('wusong8899-client1.admin.Image30'),\r\n      });\r\n});\r\n"], "names": ["__webpack_require__", "module", "getter", "__esModule", "d", "a", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "flarum", "core", "compat", "app", "add", "registerSetting", "setting", "type", "label", "trans"], "sourceRoot": ""}